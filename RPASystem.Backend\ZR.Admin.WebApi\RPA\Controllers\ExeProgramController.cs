﻿using Microsoft.AspNetCore.Mvc;
using RPASystem.Service;
using RPASystem.Model;
namespace RPASystem.WebApi.Controllers
{
    /// <summary>
    /// EXE程序管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ExeProgramController : ControllerBase
    {
        private readonly IExeProgramService exeProgramService;

        public ExeProgramController(IExeProgramService exeProgramService)
        {
            this.exeProgramService = exeProgramService;
        }

        /// <summary>
        /// 获取所有EXE程序，用于下拉框
        /// </summary>
        [HttpGet("GetAllExeProgramsForList")] // 添加具体的路由路径
        public IActionResult GetAllExeProgramsForList()
        {
            try 
            {
                var programs = exeProgramService.GetAllExeProgramsForList();
                return Ok(programs);  // 直接返回数据，因为Service层已经处理好了格式
            }
            catch (Exception ex)
            {
                return BadRequest(new { 
                    success = false, 
                    message = ex.Message 
                });
            }
        }

        /// <summary>
        /// 根据ID获取EXE程序
        /// </summary>
        [HttpGet("{id:long}")]
        public IActionResult GetExeProgram(long id)
        {
            var program = exeProgramService.GetExeProgramById(id);
            if (program == null)
            {
                return NotFound();
            }
            return Ok(program);
        }

        public class ExeProgramForm : ExeProgram
        {
            public IFormFile? ProgramPackageFile { get; set; }
        }

        /// <summary>
        /// 创建或更新EXE程序
        /// </summary>
        [HttpPost]
        public IActionResult SaveExeProgram([FromForm] ExeProgramForm form)
        {
            try
            {
                exeProgramService.SaveExeProgram(form, form.ProgramPackageFile);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 删除EXE程序
        /// </summary>
        [HttpDelete("{id}")]
        public IActionResult DeleteExeProgram(long id)
        {
            exeProgramService.DeleteExeProgram(id);
            return Ok();
        }

        /// <summary>
        /// 搜索EXE程序
        /// </summary>
        [HttpGet("search")]
        public IActionResult SearchExePrograms([FromQuery] string? query = null) // 修改参数为可空，并提供默认值
        {
            try 
            {
                var exePrograms = exeProgramService.SearchExePrograms(query ?? string.Empty); // 如果query为null，使用空字符串
                return Ok(exePrograms);
            }
            catch (Exception ex)
            {
                return BadRequest(new { 
                    success = false, 
                    message = ex.Message 
                });
            }
        }

        [HttpGet("resources")]
        public IActionResult GetResources()
        {
            try
            {
                var resources = exeProgramService.GetAllResources();
                return Ok(resources);
            }
            catch (Exception ex)
            {
                return BadRequest($"获取资源列表失败: {ex.Message}");
            }
        }

        [HttpGet("download/{programName}")]
        public IActionResult DownloadExeProgram(string programName)
        {
            var programPackage = exeProgramService.DownloadExeProgram(programName);
            if (programPackage == null)
            {
                return NotFound($"未找到程序包: {programName}");
            }
            return File(programPackage, "application/octet-stream", $"{programName}.zip");
        }
    }


}
