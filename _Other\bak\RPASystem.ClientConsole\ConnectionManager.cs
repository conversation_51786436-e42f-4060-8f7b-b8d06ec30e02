﻿using Microsoft.AspNetCore.SignalR.Client;
using NLog;
using RPASystem.WebApi.Models;
using System;
using System.Collections.Concurrent;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace RPASystem.ClientConsole
{
    public class ConnectionManager
    {
        private static readonly ILogger logger = LogManager.GetCurrentClassLogger();
        private HubConnection connection;
        private readonly ConcurrentDictionary<long, CancellationTokenSource> taskCancellationTokens = new ConcurrentDictionary<long, CancellationTokenSource>();

        public async Task StartConnectionAsync(string url, string machineName)
        {
            connection = new HubConnectionBuilder()
                .WithUrl($"{url}/resourceMachineHub?machineName={machineName}")
                .Build();

            connection.On<JobModel>("ReceiveTask", (jobModel) =>
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await HandleReceiveTaskAsync(jobModel);
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex, "处理任务时发生错误");
                    }
                });
            });

            connection.On<long>("StopTask", (taskId) =>
            {
                HandleStopTask(taskId);
            });

            connection.Closed += async (error) =>
            {
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 连接断开，正在重连...");
                await ConnectOrReconnectAsync();
            };

            await ConnectOrReconnectAsync();
        }

        private async Task HandleReceiveTaskAsync(JobModel jobModel)
        {
            Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 正在运行任务：{jobModel.JobTaskName}   任务类型：{jobModel.ProgramType}");

            RunResult result;

            var cts = new CancellationTokenSource();
            taskCancellationTokens[jobModel.Id] = cts;

            try
            {
                if (jobModel.ProgramType == ProgramTypeEnum.RPA)
                {
                    ISRPAStarter isrpaStarter = new ISRPAStarter(jobModel.ProgramName, jobModel.Parameter, jobModel.ZipFile, jobModel.JobTaskName, jobModel.TopJobTaskName);
                    result = await isrpaStarter.FastRunAsync(cts.Token);
                }
                else
                {
                    result = await EXEStarter.RunAsync(jobModel.ProgramName + ".exe", jobModel.ZipFile, jobModel.Parameter, cancellationToken: cts.Token);
                }
                JobTaskStatusEnum jobTaskStatus = result.IsSucceed ? JobTaskStatusEnum.Success : JobTaskStatusEnum.Failed;
                await connection.SendAsync("TaskCompleted", jobModel.Id, jobTaskStatus, result.ToJson());
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 任务运行结束：{jobModel.JobTaskName}  返回值：{result.ToJson()}");
            }
            catch (OperationCanceledException)
            {
                result = new RunResult
                {
                    IsSucceed = false,
                    Status = 0,
                    ErrorMessage = "任务被取消"
                };
                await connection.SendAsync("TaskCompleted", jobModel.Id, JobTaskStatusEnum.Cancelled, result.ToJson());
            }
            catch (Exception ex)
            {
                result = new RunResult
                {
                    IsSucceed = false,
                    Status = 0,
                    ErrorMessage = $"任务执行失败: {ex.Message}"
                };
                logger.Error(ex, $"任务执行失败: {jobModel.JobTaskName}");
                await connection.SendAsync("TaskCompleted", jobModel.Id, JobTaskStatusEnum.Failed, result.ToJson());
            }
            finally
            {
                taskCancellationTokens.TryRemove(jobModel.Id, out _);
            }


        }

        private void HandleStopTask(long taskId)
        {
            Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 收到停止任务指令：{taskId}");
            if (taskCancellationTokens.TryRemove(taskId, out var cts))
            {
                cts.Cancel();
            }
        }

        private async Task ConnectOrReconnectAsync()
        {
            bool connected = false;
            while (!connected)
            {
                try
                {
                    await connection.StartAsync();
                    connected = true;
                    Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 已连接。。。");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 连接服务器失败: {ex.Message}. 5秒后重试...");
                    await Task.Delay(5000);
                }
            }
        }

        public HubConnection GetConnection()
        {
            return connection;
        }
    }
}
