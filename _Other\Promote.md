在任务列表页面实现点击资源机显示远程桌面功能。
- 当任务在运行时，资源机显示蓝色可点击。
- 利用资源机管理里的远程功能，点击"远程"显示远程桌面。
- 资源机管理应该是一个通用模块，可以直接调用。如果不是通知模块，请改成通用模块。

在RPASystem.ClientWin添加发布功能：1、删除RPASystem.ClientWin.exe.config文件。2、将目录下所有文件打包成zip包。包名：RPASystem.ClientWinV3-(取Version.dat文件里第一行数据).zip。3、将zip包移动到"Z:\VV35\RPASystem.ClientWinV3.zip"

请在服务后端完成相对应的功能结构框架设计和代码编写。
请编码"任务管理"的功能。前端、后端、数据库设计，不要命名Task，因为会与框架的Task重名。
请根据数据库表设计，在现有代码的基础上修改或新增代码，在前端、后端正确的位置上添加相对应的功能。

以上是“EXE管理系统”的文档，请先理解。然后帮我优化：
- 优化文档排版，在不减少内容的情况下，优化整理md的排版格式，包括标题# ##，缩进等等，要有层次感。有的没有标题的请补充标题
- 优化表达：表达更明确，更细致
- 补充细节：技术细节可能不完善，请补充
- 补充功能说明或描述：文档里有标识简略的功能，请更详细的补充。
- 补充技术方案：有的技术方案不够明确也不够仔细，请你更详细的补充。


请仔细了解需求并实现功能



请根据当前项目情况和代码结构，在对应的位置写入代码实现功能。包括前后端所有代码。记住，你有写入代码的权限，请开始。


请完整实现////后的所有功能.

请先分析问题，列出解决方案，列出实现步骤。先不写代码，待我确定方案后，再写具体实现代码。

请你先列出解决方案，并推荐一个你认为最佳方案。

有哪些方案？请推荐一个比较简单的。

你认为我说的对吗？如果对请重新生成代码。如果不对请告诉我理由

很好, 请继续完成以下需求:

请分析并解决问题。



/// <summary>
/// 将EXCEL的第一个Sheet页的数据拆分成多个Excel，按每个Excel的条数拆分, 一个Excel有多个Sheet页，只拆分第一个Sheet页，拆分后的其它Sheet页完整保留
/// </summary>
/// <param name="pathInputExcel">Excel的流</param>
/// <param name="excelPerSplitNum">Excel拆分的每个Excel的数量</param>
/// <param name="action">拆分完一个Excel时执行Action，执行完后释放资源</param>
static bool SplitExcelByNum(Stream inputExcel, int excelPerSplitNum, Action<int,Stream> action)
{
    // 如果Excel总条数小于excelPerSplitNum ，则直接返回
    // 如果Excel总条数大于50万, 则异常
    // 在第一个Sheet删除空行
}
用EPPlus操作Excel


### RPA的运行状态：异常、警告、已完成
- RPA的每个单号，运行后会有三种状态：异常、警告、已完成。一个单号结束后将信息写到输入的Excel的标题：RPA状态、PRA信息、RPA截图
- 将输入件没有列时，将自动创建Excel列，标题：RPA状态、PRA信息、RPA截图
- 第二次重跑时只会运行异常的和未完成的单号
#### 记录信息的Python方法：
```python
def ErrorInputLog(no, msg):
def WarningInputLog(no, msg):
def DoneInputLog(no):
# no: 单号, 用于根据单号找到指定行
# msg: 在指定行的"PRA信息"列写入信息
# 输入的Excel文件路径: D:\RPA_List\RPA_List.xlsx
# no单号与第一个Sheet的"订单号"列匹配
# 如果第一列不是`RPA状态`则在第一列插入列：RPA状态、PRA信息、RPA截图
# RPA截图: 全屏截图，截图文件保存至：D:\RPA_List\RPA截图\{日期时间秒}.png .DoneInputLog方法不需要截图
```
请根据以上信息写出Python代码,3个方法的代码。
路径,订单号提取出来做为全局变量
RPA截图图片太大,请缩小成单元格同样大小,高和宽
插入时其它行时,第一次的图片不见了


StopJobTask方法需要新增功能：
- 如何是普通任务，保持当前逻辑不变
- 如果当前任务是父任务，有子任务和子子任务，将所有子任务和子子任务的状态设置为"取消"。如果状态不等于"运行中"或"待运行"状态，则不改变状态。

注意：
请完成JobTaskService里查找所有子任务的功能，尽量查询一次获得所有子任务数据，循环更新状态。