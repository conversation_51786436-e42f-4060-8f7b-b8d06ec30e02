# 优化程序管理-新增程序的排版
## 简要说明
- 程序类型在新增时逻辑都不一样
- 程序类型在新增时，需要选择程序类型，然后根据程序类型填写对应字段信息，添加参数等。
## 注意事项
- 保证现有逻辑不变，只做字段排版调整。
- "程序类型"由下拉框改为单选按钮。
- 当选择RPA类型时, 是否独占默认为true .保留原来的输入参数默认值(InputFile,ServerIP,UserName) . 去除资源选择: Resource
- 当选择EXE类型时, 是否独占默认为false

请仔细看图片的细节, 调整字段排版。
图片只是我画的草图,不一定准确, 请你要有自己的判断.


# 任务列表的系统拆分任务的子任务显示
在任务列表页面，任务类型=系统拆分任务，列表中"程序名称"为蓝色超链接，点击后弹窗显示子任务。
点击后弹窗的方式显示所有子任务。因为子任务可能成千上万个。
任务列表是"系统拆分任务"时, 不再显示子任务列表, 而是需要点击弹窗口才能显示

- 查询功能：
资源机（模糊查询）
状态（可多选）

- 分页功能
  每页显示100条

- 列表显示字段：
任务ID
任务名称
程序名称
优先级
创建时间
开始时间
运行时长
资源选择
资源机
输入参数
输出结果
状态
备注
操作(和任务列表功能一样,只是没有删除功能)







问题：
用SendAsync方法时，如果网络不稳定有可能客户端接收不到消息。
需求：
将SendAsync改成InvokeAsync的方式，确保客户端接收到了消息才处理后续逻辑。

# 客户端删除文件夹的位置添加重试功能，因为有杀毒软件占用文件会导致删除失败，需要重试。
用Polly库，将客户端删除文件夹的位置添加重试功能，因为有杀毒软件占用文件会导致删除失败，需要重试。



客户端连接服务端时，要将机器名，运行状态，运行任务名都更新到服务端，无论在连接还是重连，服务端都知道客户端的最新状态，请帮我分析。先不写代码，用最少的代码实现。


在客户端新增正在运行任务列表功能和已完成任务列表。如图。

# 正在运行任务
- 有新任务时，将任务信息添加到界面显示。（组件：dataGridViewRunningTask）.
- 任务状态为非正在运行时,移出列表
# 已完成任务列表
- 点击已完成任务列表,显示所有历史记录。最新的排在最上。
- 任务完成后将信息保存在本地，用某种格式存储（你来决定，要简单快速），存储任务数最多100条，如果超过则删除最早时间的那条。
- 为了节约资源，当点击已完成任务列表的Tab时，才加载数据。
- 用JobInfoModel类来管理任务列表和本地存储
建议:
将全局List任务列表更新到UI界面.
已完成或失败或取消状态的任务,保存至本地后,从runningTasks中移除.

请参考UI界面图。
UI界面我已经做好了.MainForm.cs和MainForm.Designer.cs

请你阅读整个项目结构和里面的逻辑，注意在相应的位置添加对应代码。
保证现有的功能不变的基础上完成功能.谢谢
内容较多,请仔细思考并一步步操作.






现在ZipFile的包是从服务商摄推送下来的。问题是本地版本已存在，会造成重复下发解压的现象，形成资源浪费。
请改成服务端下发程序名和版本号，如果版本号相同则用客户端本地的版本，不同则从服务器拉取最新版。
我们一步步来，首先删除客户端接收二进制的属性，如ZipFile。服务端也需要删除对应代码。
先完成这个任务，后面我再一步步告诉你如何做。


注意：
在ExeProgramController里添加通过程序名获取最新二进制文件。
ExeProgramService里有现有方法GetExeProgramByName，获取二进制

如何获取版本用于判断：
程序的根目录Version.bat
文件只有版本号。无其它内容。
Version.bat如果没有则创建，并加上版本号，有则判断版本号与下发任务的版本号是否相同。只有相同时才不从服务端拉取。




请你阅读整个项目结构和里面的逻辑，注意在相应的位置添加对应代码。
保证现有的功能不变的基础上完成功能.谢谢
内容较多,请仔细思考并一步步操作.


我想创建一个程序包管理类，
参数：程序路径，参数版本号
参数版本号比本地版本号对比。判断版本号与参数版本号是否相同。不同则从服务端拉取。相同直接返回
版本号存放地程序的根目录Version.bat。文件只有版本号。无其它内容。
Version.bat如果没有则拉取最新程序包后创建，并加上版本号