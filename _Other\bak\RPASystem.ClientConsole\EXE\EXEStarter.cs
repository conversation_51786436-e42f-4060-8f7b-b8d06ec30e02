﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using NLog;

public class EXEStarter
{
    private static readonly ILogger logger = LogManager.GetCurrentClassLogger();

    readonly static string ExeDirBasePath = "D:\\isearch\\RPAEXE\\";

    /// <summary>
    /// 在指定位置运行EXE，并得到EXE返回值
    /// </summary>
    /// <param name="exeDirPath">要运行的EXE位置</param>
    /// <param name="zipFile">zip压缩文件</param>
    /// <param name="exeParameter">exe执行的参数</param>
    /// <param name="exeName">要执行Exe的名称</param>
    /// <returns></returns>
    public static async Task<RunResult> RunAsync(string exeName, byte[] zipFile = null, string exeParameter = "", CancellationToken cancellationToken = default)
    {
        var result = new RunResult
        {
            IsSucceed = true,
            Status = 1
        };
        string exeDirPath = ExeDirBasePath + exeName;
        Directory.CreateDirectory(exeDirPath);
        //-- 如果zipFile不为空，则需要解压缩包到{exeDirPath}目录后再运行压缩包里的EXE，
        if (zipFile != null && zipFile.Length > 0)
        {
            try
            {
                // 如果存在则先删除
                if (Directory.Exists(exeDirPath))
                {
                    Directory.Delete(exeDirPath, true);
                }

                // 确保目标目录存在
                Directory.CreateDirectory(exeDirPath);

                // 创建一个临时文件来保存zip内容
                string tempZipPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".zip");
                File.WriteAllBytes(tempZipPath, zipFile);

                // 解压zip文件到exeDirPath
                ZipFile.ExtractToDirectory(tempZipPath, exeDirPath);

                // 删除临时zip文件
                File.Delete(tempZipPath);

                Console.WriteLine($"成功解压ZIP文件到 {exeDirPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解压ZIP文件失败：{ex.Message}");
                return new RunResult { IsSucceed = false, ErrorMessage = $"解压失败: {ex.Message}", Status = 0 };
            }
        }

        // 运行EXE
        try
        {
            string exePath = Path.Combine(exeDirPath, exeName);
            if (!File.Exists(exePath))
            {
                exePath = Path.Combine(exeDirPath, "Main.exe");
            }
            ProcessStartInfo psi = new ProcessStartInfo(exePath, ConvertToCommandLine(exeParameter))
            {
                UseShellExecute = true,
                WorkingDirectory = exeDirPath,
                CreateNoWindow = false,
            };

            using (Process process = new Process { StartInfo = psi })
            {
                bool isProcessKilled = false;

                // 在进程启动前注册取消回调
                using (cancellationToken.Register(() =>
                {
                    try
                    {
                        if (!process.HasExited)
                        {
                            isProcessKilled = true;
                            process.Kill();
                            Console.WriteLine($"进程 {process.Id} 已被终止");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex, "终止进程失败");
                    }
                }))
                {
                    process.Start();
                    Console.WriteLine($"已启动EXE进程，ID: {process.Id}");

                    try
                    {
                        // 等待进程退出，同时支持取消
                        await Task.Run(() =>
                        {
                            process.WaitForExit();
                        }, cancellationToken);

                        // 如果进程是被Kill的，应该抛出取消异常
                        if (isProcessKilled || cancellationToken.IsCancellationRequested)
                        {
                            throw new OperationCanceledException(cancellationToken);
                        }

                        // 只有正常退出才获取返回值
                        var exeResult = RunResult.FromJson(GetExeReturn(process));
                        result.ReturnResult = exeResult?.ReturnResult;
                        result.ErrorMessage = exeResult?.ErrorMessage;
                    }
                    catch (OperationCanceledException)
                    {
                        // 重新抛出取消异常
                        throw;
                    }
                }
            }
        }
        catch (OperationCanceledException)
        {
            result.IsSucceed = false;
            result.Status = 0;
            result.ErrorMessage = "任务被取消";
            throw;
        }
        catch (Exception ex)
        {
            result.IsSucceed = false;
            result.Status = 0;
            result.ErrorMessage = $"运行EXE失败：{ex.Message}";
            logger.Error(ex, $"运行EXE失败：{exeDirPath}");
            Console.WriteLine(result.ErrorMessage);
            throw;
        }

        return result;
    }

    /// <summary>
    /// Json字符串转CommandLine参数，如：{"aaa":"111"} 转成 --aaa "111"
    /// </summary>
    /// <param name="json">json只接收为空或Json格式字符串</param>
    /// <returns>返回解析成CommandLine参数</returns>
    public static string ConvertToCommandLine(string json)
    {
        if (string.IsNullOrEmpty(json))
        {
            return string.Empty;
        }

        try
        {
            var jsonObject = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(json);
            var commandLineArgs = new List<string>();

            foreach (var kvp in jsonObject)
            {
                commandLineArgs.Add($"--{kvp.Key} \"{kvp.Value}\"");
            }

            return string.Join(" ", commandLineArgs);
        }
        catch (System.Text.Json.JsonException)
        {
            Console.WriteLine("输入的JSON格式不正确");
            return string.Empty;
        }
    }

    /// <summary>
    /// 获取EXE的返回值
    /// </summary>
    /// <param name="process">进程对象</param>
    /// <returns></returns>
    private static string GetExeReturn(Process process)
    {
        string returnFilePath = Path.Combine(process.StartInfo.WorkingDirectory, "ReturnVal", $"{process.Id}.TXT");
        if (File.Exists(returnFilePath))
        {
            string content = File.ReadAllText(returnFilePath);
            content = content.Replace("Ret==>", "");
            // 确保返回的内容是有效的 JSON
            try
            {
                var result = JsonConvert.DeserializeObject<RunResult>(content);
                return JsonConvert.SerializeObject(result);
            }
            catch
            {
                // 如果不是有效的 JSON，创建一个新的 RPAResult 对象
                return JsonConvert.SerializeObject(new RunResult
                {
                    IsSucceed = true,
                    ReturnResult = content,
                    Status = 1
                });
            }
        }
        return JsonConvert.SerializeObject(new RunResult
        {
            IsSucceed = false,
            ErrorMessage = "未找到返回值文件",
            Status = 0
        });
    }
}
