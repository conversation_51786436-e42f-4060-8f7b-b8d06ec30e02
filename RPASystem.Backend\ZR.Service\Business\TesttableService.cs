using Infrastructure.Attribute;
using Infrastructure.Extensions;
using ZR.Model.Business.Dto;
using ZR.Model.Business;
using ZR.Repository;
using ZR.Service.Business.IBusinessService;

namespace ZR.Service.Business
{
    /// <summary>
    /// 只是测试Service业务层处理
    /// </summary>
    [AppService(ServiceType = typeof(ITesttableService), ServiceLifetime = LifeTime.Transient)]
    public class TesttableService : BaseService<Testtable>, ITesttableService
    {
        /// <summary>
        /// 查询只是测试列表
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        public PagedInfo<TesttableDto> GetList(TesttableQueryDto parm)
        {
            var predicate = QueryExp(parm);

            var response = Queryable()
                //.OrderBy("Id desc")
                .Where(predicate.ToExpression())
                .ToPage<Testtable, TesttableDto>(parm);

            return response;
        }


        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public Testtable GetInfo(long Id)
        {
            var response = Queryable()
                .Where(x => x.Id == Id)
                .First();

            return response;
        }

        /// <summary>
        /// 添加只是测试
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Testtable AddTesttable(Testtable model)
        {
            return Insertable(model).ExecuteReturnEntity();
        }

        /// <summary>
        /// 修改只是测试
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateTesttable(Testtable model)
        {
            return Update(model, true);
        }

        /// <summary>
        /// 清空只是测试
        /// </summary>
        /// <returns></returns>
        public bool TruncateTesttable()
        {
            var newTableName = $"testtable_{DateTime.Now:yyyyMMdd}";
            if (Queryable().Any() && !Context.DbMaintenance.IsAnyTable(newTableName))
            {
                Context.DbMaintenance.BackupTable("testtable", newTableName);
            }
            
            return Truncate();
        }
        /// <summary>
        /// 导入只是测试
        /// </summary>
        /// <returns></returns>
        public (string, object, object) ImportTesttable(List<Testtable> list)
        {
            var x = Context.Storageable(list)
                .SplitInsert(it => !it.Any())
                //.WhereColumns(it => it.UserName)//如果不是主键可以这样实现（多字段it=>new{it.x1,it.x2}）
                .ToStorage();
            var result = x.AsInsertable.ExecuteCommand();//插入可插入部分;

            string msg = $"插入{x.InsertList.Count} 更新{x.UpdateList.Count} 错误数据{x.ErrorList.Count} 不计算数据{x.IgnoreList.Count} 删除数据{x.DeleteList.Count} 总共{x.TotalList.Count}";                    
            Console.WriteLine(msg);

            //输出错误信息               
            foreach (var item in x.ErrorList)
            {
                Console.WriteLine("错误" + item.StorageMessage);
            }
            foreach (var item in x.IgnoreList)
            {
                Console.WriteLine("忽略" + item.StorageMessage);
            }

            return (msg, x.ErrorList, x.IgnoreList);
        }

        /// <summary>
        /// 导出只是测试
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        public PagedInfo<TesttableDto> ExportList(TesttableQueryDto parm)
        {
            var predicate = QueryExp(parm);

            var response = Queryable()
                .Where(predicate.ToExpression())
                .Select((it) => new TesttableDto()
                {
                    MyTypeLabel = it.MyType.GetConfigValue<Model.System.SysDictData>("rpa_run_type"),
                }, true)
                .ToPage(parm);

            return response;
        }

        /// <summary>
        /// 查询导出表达式
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        private static Expressionable<Testtable> QueryExp(TesttableQueryDto parm)
        {
            var predicate = Expressionable.Create<Testtable>();

            predicate = predicate.AndIF(!string.IsNullOrEmpty(parm.Name), it => it.Name.Contains(parm.Name));
            predicate = predicate.AndIF(parm.MyType != null, it => it.MyType == parm.MyType);
            return predicate;
        }
    }
}