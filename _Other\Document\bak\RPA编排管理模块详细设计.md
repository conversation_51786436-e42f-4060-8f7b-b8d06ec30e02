### RPA（EXE）编排管理模块详细设计

> 以下EXE省略RPA，PRA可看作是EXE的一种

#### 目标

1. **支持EXE返回值处理**
   - EXE程序的返回值可以在编排脚本中获取并使用。

2. **EXE编排脚本**
   - 自定义类似Python的脚本的语言，能够定义参数，执行EXE程序

3. **中途继跑功能**
   - 假如有10个任务，任务全部运行完后，又可以从第3个任务重新开始运行至最后
  
4. **继续运行功能**
   - 假如服务器断电或死机,10个任务运行到第5条,服务重启后继续从第5条继续运行.

5. **支持脚本调用内置系统函数功能**
   - 如系统定义了SystemExe函数，则解析脚本时将执行SystemExe的函数。

6. **支持并行串行运行**
   - 将多个EXE编排在一起,支持并行运行或串行运行

7. **支持简单if语句**
   - 脚本中可使用简单if语句判断，只支持对字符串的判断
  
8. **支持Return语句结束和返回** 
   - 可Return变量或自定义的字符串

9. **EXE程序和编排输入参数**
    - 编排运行时需要设置参数，而参数是脚本里定义的变量


#### 编排脚本示例

以下是一个示例编排脚本：

```python
# 编排参数 Orchestration parameters 简称OP 
op1 = "aaa"  # 编排参数1，aaa是默认值
op2 = ""     # 编排参数2，默认值可以为空

exe1(op1)  # 调用EXE名称是"exe1"的程序，没有返回值

# 不等exe1执行完，直接跳到下一个exe2执行
ret1 = exe2(exe_p1=op1, exe_p2=op2)  # 执行exe2，传递两个参数,exe_p1、exe_p2是exe2程序需要传入的两个参数。op1和op2为编排参数。完成后获取返回值

# 等待exe1和exe2完全运行结束才继续运行下面的任务
waitAll

# 使用exe2的返回值作为exe3的参数
ret2 = exe3(exe_p1=ret1.x.y, exe_p2=op2)  # ret1是exe2的返回值，取返回值的x字段下的y值作为exe3的参数
exe4()

# 等待exe3和exe4全部执行完，如果有异常则在后台标识任务状态，等待人工处理，处理完后继续往下执行，以上多个任务中，其中一个异常则算异常,如果其实一个有异常则在页面上会显示 重试 和 确认继续
waitProIfErr
# 简单的if条件判断，只支持判断字符串
if ret1.x == "true":
    exe1(op1)
    return ""  # 提前结束，返回值为空
else:
    exe4()

# 调用另一个编排
orc1(op1=op1)  # 在一个编排程序里嵌套调用另一个编排程序

# 调用系统内置函数
SystemExe()  # 系统内置通用的方法

# 编排结束后的返回值
return ret2.x  # 返回 ret2中的x值
```

### 详细设计

1. **编排脚本编辑器**
   - **功能**
     - 提供文本编辑界面。
     - 支持语法高亮和自动补全。
   - **实现**
     - 使用Ace或Monaco等代码编辑器组件。
     - 集成语法检查和自动补全功能。
     - 提供脚本保存和加载功能，允许用户保存和修改编排脚本。

2. **参数管理**
   - **功能**
     - 定义全局和局部参数。
     - 设置参数的默认值。
   - **实现**
     - 在脚本编辑器中提供参数定义区域。
     - 支持参数模板和快速插入。
     - 在执行编排时，通过UI输入界面获取和设置参数值。

3. **脚本解析器**
   - **功能**
     - 解析用户编写的自定义脚本。
     - 提取参数定义、EXE调用和逻辑控制语句。
   - **实现**
     - 编写自定义的脚本解析器，将脚本解析为标准的数据格式（如JSON）。
     - 解析器支持提取参数、识别EXE调用、处理返回值和执行控制语句（如if和return）。

4. **任务调度与执行器**
   - **功能**
     - 负责调度和执行解析后的任务。
     - 支持并行和串行执行任务。
     - 处理EXE的同步和异步调用，获取返回值。
   - **实现**
     - 任务调度器负责根据解析后的任务计划，依次调度任务执行。
     - EXE执行器负责实际调用EXE程序，处理参数传递和返回值获取。
     - 任务调度器与数据库交互，保存任务状态和返回值，支持中途继跑和继续运行功能。

5. **中途继跑功能**
   - **功能**
     - 支持从指定任务开始重新运行。
   - **实现**
     - 用户在界面上选择从哪一步重新开始运行，系统根据选择从数据库中加载任务状态并继续执行。

6. **继续运行功能**
   - **功能**
     - 在服务器重启后继续从上次断点运行。
   - **实现**
     - 系统在执行任务时，定期将任务状态和执行进度保存到数据库。
     - 服务器重启后，从数据库中恢复任务状态，继续执行未完成的任务。

7. **返回值处理**
   - **功能**
     - 获取和处理EXE的返回值。
   - **实现**
     - 返回值解析支持JSON格式，允许使用路径表达式（如`ret.x.y`）获取特定值。
     - 将返回值存储在数据库中，供后续任务使用。

8. **内置系统函数支持**
   - **功能**
     - 支持在脚本中调用系统内置函数。
   - **实现**
     - 在脚本解析器中预定义系统内置函数的调用规则。
     - 在执行器中实现这些内置函数的实际逻辑。

9. **日志和监控**
   - **功能**
     - 提供任务执行日志和状态监控。
   - **实现**
     - 在任务执行过程中记录日志，保存到数据库。
     - 提供用户界面查看任务执行状态和历史记录。

#### 数据流程

1. **脚本解析**
   - 用户在脚本编辑器中编写编排脚本，定义参数和EXE调用逻辑。
   - 系统解析脚本，提取参数和EXE调用信息，并将其转换为标准的数据格式（如JSON）。
   - 将解析后的数据存储到数据库中。

2. **任务调度**
   - 系统根据解析后的数据生成任务执行计划。
   - 调度服务根据任务计划，依次调度任务执行，处理异步和同步调用。
   - 调度过程中，定期将任务状态和进度保存到数据库。

3. **任务执行**
   - 调度服务调用EXE执行器执行任务，传递参数并获取返回值。
   - 返回值通过EXE执行器传递回调度服务，并存储到数据库中。
   - 调度服务根据脚本逻辑，继续执行后续任务，处理返回值和控制语句。

4. **中途继跑和继续运行**
   - 用户在界面上选择从哪一步重新开始运行，系统从数据库中加载任务状态并继续执行。
   - 服务器重启后，系统从数据库中恢复任务状态，继续执行未完成的任务。

5. **返回值处理和内置系统函数**
   - 返回值解析支持JSON格式，允许使用路径表达式获取特定值。
   - 在脚本解析器中预定义系统内置函数的调用规则，并在执行器中实现这些函数的实际逻辑。


### 1. 脚本解析流程图

```mermaid
graph TD
    A[开始] --> B[读取脚本内容]
    B --> C[按行分割脚本]
    C --> D{是否是EXE调用行?}
    D -->|是| E[解析EXE名称和参数]
    E --> F[添加到任务列表]
    D -->|否| G{是否是waitAll指令?}
    G -->|是| H[添加waitAll到任务列表]
    G -->|否| I[忽略其他行]
    H --> F
    I --> F
    F --> J{是否有更多行?}
    J -->|是| D
    J -->|否| K[解析完成]
    K --> L[结束]
```

### 2. 任务调度流程图

```mermaid
graph TD
    A[开始] --> B[初始化任务列表]
    B --> C[循环遍历任务列表]
    C --> D{当前任务是waitAll指令?}
    D -->|是| E[等待所有并行任务完成]
    D -->|否| F[并行执行EXE任务]
    E --> G[检查下一个任务]
    F --> G
    G --> H{是否有更多任务?}
    H -->|是| C
    H -->|否| I[所有任务执行完成]
    I --> J[结束]
```

### 3. 时序图

#### EXE编排模块时序图

```mermaid
sequenceDiagram
    participant 编排模块
    participant 脚本解析器
    participant 任务调度器
    participant EXE执行器

    编排模块->>脚本解析器: 提交脚本
    脚本解析器->>脚本解析器: 解析脚本
    脚本解析器->>编排模块: 返回解析

    编排模块->>任务调度器: 提交任务
    任务调度器->>EXE执行器: 执行EXE任务
    EXE执行器->>任务调度器: 返回执行结果
    任务调度器->>EXE执行器: 执行下一个任务
    EXE执行器->>任务调度器: 返回执行结果
    任务调度器->>编排模块: 返回任务执行

    任务调度器->>EXE执行器: 遇到waitAll指令
    EXE执行器->>任务调度器: 等待所有任务完成
    任务调度器->>编排模块: 返回任务执行
```

### 4. 并行和串行任务执行流程图

```mermaid
graph TD
    A[开始] --> B[初始化任务列表]
    B --> C[循环遍历任务列表]
    C --> D{当前任务是并行任务?}
    D -->|是| E[添加到并行任务列表]
    D -->|否| F[串行执行任务]
    F --> G[检查下一个任务]
    E --> G
    G --> H{是否有更多任务?}
    H -->|是| C
    H -->|否| I{是否有并行任务需要执行?}
    I -->|是| J[并行执行所有任务]
    I -->|否| K[所有任务执行完成]
    J --> L[等待并行任务完成]
    L --> K
    K --> M[结束]
```

### 5. 数据存储和恢复流程图

```mermaid
graph TD
    A[开始] --> B[初始化任务列表]
    B --> C[读取数据库中的任务状态]
    C --> D{是否有未完成的任务?}
    D -->|是| E[从上次断点继续执行]
    D -->|否| F[从头开始执行任务]
    E --> G[恢复任务状态并继续执行]
    F --> H[正常执行任务]
    G --> I[保存当前任务状态]
    H --> I
    I --> J{是否所有任务完成?}
    J -->|是| K[所有任务执行完成]
    J -->|否| L[等待下一个任务执行]
    L --> I
    K --> M[结束]
```

