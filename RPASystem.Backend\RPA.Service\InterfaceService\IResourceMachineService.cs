using RPASystem.Model;

namespace RPASystem.Service
{
    public interface IResourceMachineService
    {
        Task RegisterOrUpdateMachineAsync(string machineName, string clientVersion = null, bool isLatestVersion = false);
        Task UpdateMachineStatusAsync(string machineName, TaskStatusEnum status);
        Task<List<ResourceMachine>> GetIdleAsync();
        Task<List<ResourceMachine>> GetAllResourceMachinesAsync();
        Task<List<ResourceMachine>> GetResourceMachinesAsync(string? searchTerm = null);
        Task<List<ResourceMachine>> GetResourceMachinesAsync(string? searchTerm = null, bool? offlineOverSevenDays = null);
        Task UpdateResourceInfoAsync(ResourceInfoDto resourceInfo);
        Task<bool> DeleteResourceMachineAsync(long id);
        Task InitializeResourceMachineStatusAsync();
        Task UpdateResourceMachineTaskStatusAsync(string machineName, List<long> runningTaskIds);
        Task SetAllOnlineMachinesToRunningAsync();
        Task SetAllMachinesToNonLatestVersionAsync();
        Task<bool> UpdateMachineTypeAsync(long id, MachineTypeEnum machineType);
    }
} 