namespace RPASystem.Model
{
    public class ViewJobTaskExeProgram
    {
        // JobTask属性
        public long JobTaskId { get; set; }
        public long ParentTaskID { get; set; }
        public TaskType TaskType { get; set; } = TaskType.Normal;
        public string JobTaskName { get; set; }
        public int Priority { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string? AssignedResourceMachine { get; set; }
        public string? InputParameters { get; set; }
        public string? OutputResults { get; set; }
        public string? Status { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string? Notes { get; set; }
        public string TopJobTaskName { get; set; }

        // ExeProgram属性
        public long ExeProgramId { get; set; }
        public string ExeProgramName { get; set; }
        public string Version { get; set; }
        public string ExeInputParameters { get; set; }
        public bool IsExclusive { get; set; }
        public ProgramTypeEnum ProgramType { get; set; }
        public DateTime? CreatedAtExe { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string Remarks { get; set; }
        public string ResourceSelection { get; set; }
    }
}
