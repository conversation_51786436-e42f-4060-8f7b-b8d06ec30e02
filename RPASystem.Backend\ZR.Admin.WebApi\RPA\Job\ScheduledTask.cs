﻿using Microsoft.AspNetCore.SignalR;
using RPASystem.Service;
using RPASystem.Model;
using RPASystem.WebApi.Hubs;
using RPASystem.Model;
using System.Text.Json;
using System.Xml.Linq;

public class ScheduledTask : IHostedService, IDisposable
{
    private readonly ILogger<ScheduledTask> logger;
    private readonly IServiceProvider serviceProvider;
    private Timer timer;
    private IHubContext<ResourceMachineHub> resourceMachineHubContext;
    private static readonly object taskLock = new object();
    private static int isRunningFlag = 0;

    public ScheduledTask(ILogger<ScheduledTask> logger, IServiceProvider serviceProvider, IHubContext<ResourceMachineHub> resourceMachineHubContext)
    {
        this.logger = logger;
        this.serviceProvider = serviceProvider;
        this.resourceMachineHubContext = resourceMachineHubContext;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Scheduled Task is starting.");
        timer = new Timer(GetTaskAndPush, null, TimeSpan.Zero, TimeSpan.FromSeconds(3));
        return Task.CompletedTask;
    }

    private async void GetTaskAndPush(object state)
    {
        if (Interlocked.CompareExchange(ref isRunningFlag, 1, 0) != 0)
        {
            logger.LogInformation("上一次任务还在执行中，跳过本次执行");
            return;
        }

        try
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var jobTaskService = scope.ServiceProvider.GetRequiredService<IJobTaskService>();
                var resourceMachineService = scope.ServiceProvider.GetRequiredService<IResourceMachineService>();

                // 1. 获取空闲资源机
                var idleResourceMachines = await resourceMachineService.GetIdleAsync();
                if (idleResourceMachines.Count == 0)
                {
                    return;
                }

                // 2. 获取待执行任务
                var pendingTasks = await jobTaskService.GetPendingTasksWithProgramAsync();
                if (pendingTasks.Count == 0)
                {
                    return;
                }

                // 3. 为每个任务找到合适的资源机
                var dispatchTasks = new List<Task>();
                foreach (var task in pendingTasks)
                {
                    ResourceMachine selectedMachine = null;

                    if (!string.IsNullOrEmpty(task.ResourceSelection))
                    {
                        // 获取指定的可用资源机列表
                        var availableMachines = await jobTaskService.GetAvailableResourceMachinesAsync(task.ResourceSelection);
                        // 在空闲资源机中查找指定的资源机
                        selectedMachine = idleResourceMachines.FirstOrDefault(m => availableMachines.Contains(m.MachineName));
                    }
                    else
                    {
                        // 如果没有指定资源机，只能使用在线执行机
                        selectedMachine = idleResourceMachines.FirstOrDefault(m => m.MachineType == MachineTypeEnum.在线执行机);
                    }

                    if (selectedMachine != null)
                    {
                        if (ResourceMachineHub.machineConnections.TryGetValue(selectedMachine.MachineName, out string connectionId))
                        {
                            // 不管下发任务是否成功，更新任务状态
                            await resourceMachineService.UpdateMachineStatusAsync(selectedMachine.MachineName, TaskStatusEnum.任务运行中);
                            // 移出List的空闲资源机
                            idleResourceMachines.Remove(selectedMachine);
                            // 使用Task.Run创建新线程执行任务分发
                            var dispatchTask = Task.Run(() => DispatchTaskToMachineAsync(task, selectedMachine, connectionId, jobTaskService));
                            dispatchTasks.Add(dispatchTask);
                        }
                        else
                        {
                            logger.LogWarning($"资源机 {selectedMachine.MachineName} 不在连接字典中，可能已离线");
                        }
                    }
                }

                // 等待所有任务分发完成
                if (dispatchTasks.Any())
                {
                    await Task.WhenAll(dispatchTasks);
                    await resourceMachineHubContext.Clients.Group("WebClients").SendAsync("RefreshJobTasks");
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "GetTaskAndPush执行出错");
        }
        finally
        {
            Interlocked.Exchange(ref isRunningFlag, 0);
        }
    }

    private async Task DispatchTaskToMachineAsync(ViewJobTaskExeProgram task, ResourceMachine selectedMachine, string connectionId, IJobTaskService jobTaskService)
    {
        try
        {
            // 1. 获取顶层任务名称
            string topTaskName = string.Empty;
            if (task.ParentTaskID != 0) { topTaskName = await jobTaskService.GetTopTaskNameAsync(task.JobTaskId); }

            // 2. 构建任务模型
            var jobModel = new JobModel { Id = task.JobTaskId, JobTaskName = task.JobTaskName, TopJobTaskName = topTaskName, ProgramType = task.ProgramType, Parameter = task.InputParameters, ProgramName = task.ExeProgramName, ProgramVersion = task.Version };

            // 添加超时控制
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            try
            {
                bool received = await resourceMachineHubContext.Clients.Client(connectionId).InvokeAsync<bool>("RunTask", jobModel, cts.Token);
                if (received)
                {
                    await jobTaskService.UpdateJobTaskStatusAsync(jobModel.Id, JobTaskStatusEnum.Running, selectedMachine.MachineName);
                }
            }
            catch (OperationCanceledException)
            {
                logger.LogError($"向资源机 {selectedMachine.MachineName} 发送任务超时");
                // await jobTaskService.UpdateTaskWithErrorAsync(jobModel.Id, "任务分发超时");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"向资源机 {selectedMachine.MachineName} 发送任务失败");
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Scheduled Task is stopping.");
        timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        timer?.Dispose();
    }
}
