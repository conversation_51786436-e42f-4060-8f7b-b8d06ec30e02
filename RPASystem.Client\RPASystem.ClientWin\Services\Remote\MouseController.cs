using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Forms;
using NLog;

namespace RPASystem.ClientWin.Services.Remote
{
    /// <summary>
    /// 鼠标控制器，用于远程控制本地鼠标
    /// </summary>
    public class MouseController
    {
        private static readonly ILogger logger = LogManager.GetCurrentClassLogger();

        // Windows API 常量
        private const int MOUSEEVENTF_LEFTDOWN = 0x02;
        private const int MOUSEEVENTF_LEFTUP = 0x04;
        private const int MOUSEEVENTF_RIGHTDOWN = 0x08;
        private const int MOUSEEVENTF_RIGHTUP = 0x10;
        private const int MOUSEEVENTF_WHEEL = 0x0800;
        private const int WHEEL_DELTA = 120; // 标准滚轮增量值

        // 导入 Windows API
        [DllImport("user32.dll")]
        static extern void mouse_event(int dwFlags, int dx, int dy, int dwData, int dwExtraInfo);

        [DllImport("user32.dll")]
        static extern bool SetCursorPos(int x, int y);

        /// <summary>
        /// 执行鼠标操作
        /// </summary>
        /// <param name="button">鼠标按钮 ("left" 或 "right")</param>
        /// <param name="xPercent">相对X坐标(0-100)</param>
        /// <param name="yPercent">相对Y坐标(0-100)</param>
        /// <returns>操作结果，包含是否成功及消息</returns>
        public static async Task<(bool success, string message)> ExecuteMouseCommandAsync(string button, int xPercent, int yPercent)
        {
            try
            {
                // 将百分比坐标转换为屏幕坐标
                Rectangle screenBounds = Screen.PrimaryScreen.Bounds;
                int screenX = (int)(screenBounds.Width * xPercent / 100.0);
                int screenY = (int)(screenBounds.Height * yPercent / 100.0);

                // 确保坐标在屏幕范围内
                screenX = Math.Max(0, Math.Min(screenX, screenBounds.Width - 1));
                screenY = Math.Max(0, Math.Min(screenY, screenBounds.Height - 1));

                // 在UI线程上执行鼠标操作
                return await Task.Run(() =>
                {
                    try
                    {
                        // 移动鼠标到指定位置
                        SetCursorPos(screenX, screenY);

                        // 执行鼠标点击操作
                        if (button.ToLower() == "left")
                        {
                            // 左键点击
                            mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
                            System.Threading.Thread.Sleep(50);
                            mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
                            logger.Info($"执行左键点击: X={screenX}, Y={screenY}");
                        }
                        else if (button.ToLower() == "right")
                        {
                            // 右键点击
                            mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0);
                            System.Threading.Thread.Sleep(50);
                            mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0);
                            logger.Info($"执行右键点击: X={screenX}, Y={screenY}");
                        }
                        else
                        {
                            return (false, $"不支持的鼠标按钮类型: {button}");
                        }

                        return (true, "操作执行成功");
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex, "执行鼠标操作失败");
                        return (false, $"执行鼠标操作失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                logger.Error(ex, "鼠标控制器出错");
                return (false, $"鼠标控制器出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行鼠标滚轮操作
        /// </summary>
        /// <param name="scrollAmount">滚动量，正数向下滚动，负数向上滚动</param>
        /// <param name="xPercent">相对X坐标(0-100)</param>
        /// <param name="yPercent">相对Y坐标(0-100)</param>
        /// <returns>操作结果，包含是否成功及消息</returns>
        public static async Task<(bool success, string message)> ExecuteMouseWheelCommandAsync(int scrollAmount, int xPercent, int yPercent)
        {
            try
            {
                // 将百分比坐标转换为屏幕坐标
                Rectangle screenBounds = Screen.PrimaryScreen.Bounds;
                int screenX = (int)(screenBounds.Width * xPercent / 100.0);
                int screenY = (int)(screenBounds.Height * yPercent / 100.0);

                // 确保坐标在屏幕范围内
                screenX = Math.Max(0, Math.Min(screenX, screenBounds.Width - 1));
                screenY = Math.Max(0, Math.Min(screenY, screenBounds.Height - 1));

                // 计算滚轮增量值
                int wheelDelta = scrollAmount * WHEEL_DELTA;

                // 在UI线程上执行鼠标操作
                return await Task.Run(() =>
                {
                    try
                    {
                        // 移动鼠标到指定位置
                        SetCursorPos(screenX, screenY);

                        // 执行鼠标滚轮操作
                        mouse_event(MOUSEEVENTF_WHEEL, 0, 0, wheelDelta, 0);
                        logger.Info($"执行滚轮操作: 滚动量={scrollAmount}, X={screenX}, Y={screenY}");

                        return (true, "滚轮操作执行成功");
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex, "执行鼠标滚轮操作失败");
                        return (false, $"执行鼠标滚轮操作失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                logger.Error(ex, "鼠标滚轮控制器出错");
                return (false, $"鼠标滚轮控制器出错: {ex.Message}");
            }
        }
    }
} 