using Microsoft.AspNetCore.Mvc;
using RPASystem.Service;
using RPASystem.Model;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class RpaCredentialController : ControllerBase
    {
        private readonly IRpaCredentialService credentialService;

        public RpaCredentialController(IRpaCredentialService credentialService)
        {
            this.credentialService = credentialService;
        }

        [HttpGet]
        public async Task<ActionResult<List<RpaCredential>>> GetAll()
        {
            var credentials = await credentialService.GetAllCredentials();
            return Ok(credentials);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<RpaCredential>> GetById(long id)
        {
            var credential = await credentialService.GetCredentialById(id);
            if (credential == null) return NotFound();
            return Ok(credential);
        }

        [HttpPost]
        public async Task<ActionResult<bool>> Create([FromBody] RpaCredential credential)
        {
            var result = await credentialService.CreateCredential(credential);
            return Ok(result);
        }

        [HttpPut]
        public async Task<ActionResult<bool>> Update([FromBody] RpaCredential credential)
        {
            var result = await credentialService.UpdateCredential(credential);
            return Ok(result);
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> Delete(long id)
        {
            var result = await credentialService.DeleteCredential(id);
            return Ok(result);
        }

        /// <summary>
        /// 通过用户名获取密文
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>密文信息</returns>
        [HttpGet("password/{username}")]
        public async Task<ActionResult<string>> GetPasswordByUsername(string username)
        {
            if (string.IsNullOrEmpty(username))
            {
                return BadRequest("用户名不能为空");
            }

            try
            {
                var password = await credentialService.GetPasswordByUsername(username);
                if (string.IsNullOrEmpty(password))
                {
                    return NotFound($"未找到用户名为 {username} 的凭证信息");
                }
                return Ok(password);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"获取密文失败: {ex.Message}");
            }
        }
    }
}