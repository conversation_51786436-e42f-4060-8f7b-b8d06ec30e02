{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=RPASystem;User=root;Password=******;"
  },
  "dbConfigs": [
    {
      "Conn": "Data Source=localhost;port=3306;User ID=root;Password=******;Database=ZrAdmin;CharSet=utf8;sslmode=none;",
      "DbType": 0, 
      "ConfigId": "0", 
      "IsAutoCloseConnection": true
    }
    //...下面添加更多的数据库源
  ],
  //代码生成数据库配置
  "CodeGenDbConfig": {
    //代码生成连接字符串，注意{dbName}为固定格式，不要填写数据库名
    "Conn": "Data Source=localhost;port=3306;User ID=root;Password=******;Database={dbName};CharSet=utf8;sslmode=none;",
    "DbType": 0,
    "IsAutoCloseConnection": true,
    "DbName": "ZrAdmin" //代码生成默认连接数据库,Oracle库是实例的名称
  },
  "urls": "http://localhost:8888", //项目启动url，如果改动端口前端对应devServer也需要进行修改
  "corsUrls": [ "http://localhost:8887", "http://localhost:8886" ], //跨域地址（前端启动项目，前后端分离单独部署需要设置），多个用","隔开
  "JwtSettings": {
    "Issuer": "ZRAdmin.NET", //即token的签发者。
    "Audience": "ZRAdmin.NET", //指该token是服务于哪个群体的（群体范围）
    "SecretKey": "SecretKey-ZRADMIN.NET-202311281883838",
    "Expire": 1440, //jwt登录过期时间（分）
    "RefreshTokenTime": 30, //分钟
    "TokenType": "Bearer"
  },
  "InjectClass": [ "ZR.Repository", "ZR.Service", "ZR.Tasks", "ZR.ServiceCore", "RPA.Service" ], //自动注入类
  "ShowDbLog": true, //是否打印db日志
  "InitDb": false, //是否初始化db
  "DemoMode": false, //是否演示模式
  "SingleLogin": false, //是否允许多设备/浏览器登录
  "workId": 1, //雪花id唯一数字
  "sqlExecutionTime": 5, //Sql执行时间超过多少秒记录日志并警报
  "Upload": {
    "uploadUrl": "http://localhost:8888", //本地存储资源访问路径
    "localSavePath": "", //本地上传默认文件存储目录 wwwroot
    "maxSize": 15, //上传文件大小限制 15M
    "notAllowedExt": [ ".bat", ".exe", ".jar", ".js" ],
    "requestLimitSize": 50 //请求body大小限制
  },
  //阿里云存储配置
  "ALIYUN_OSS": {
    "REGIONID": "", //eg：cn-hangzhou
    "KEY": "XX",
    "SECRET": "XX",
    "bucketName": "bucketName",
    "domainUrl": "http://xxx.xxx.com", //访问资源域名
    "maxSize": 100 //上传文件大小限制 100M
  },
  //企业微信通知配置
  "WxCorp": {
    "AgentID": "",
    "CorpID": "",
    "CorpSecret": "",
    "SendUser": "@all"
  },
  //微信公众号设置
  "WxOpen": {
    "AppID": "",
    "AppSecret": ""
  },
  //邮箱配置信息
  "MailOptions": [
    {
      //发件人名称(请保证数组里面唯一)
      "FromName": "system",
      //发送人邮箱
      "FromEmail": "", //eg：<EMAIL>
      //发送人邮箱密码
      "Password": "",
      //协议
      "Smtp": "smtp.qq.com",
      "Port": 587,
      "Signature": "系统邮件，请勿回复！",
      "UseSsl": true
    }
  ],
  //redis服务配置
  "RedisServer": {
    "open": 0, //是否启用redis
    "dbCache": false, //数据库是否使用Redis缓存，如果启用open要为1
    "Cache": "127.0.0.1:6379,defaultDatabase=0,poolsize=50,ssl=false,writeBuffer=10240,prefix=cache:",
    "Session": "127.0.0.1:6379,defaultDatabase=0,poolsize=50,ssl=false,writeBuffer=10240,prefix=session:"
  },
  //验证码配置
  "CaptchaOptions": {
    "IgnoreCase": true // 比较时是否忽略大小写
  },
  //代码生成配置
  "CodeGen": {
    //uniapp 版本号2/3(vue版本号)
    "uniappVersion": 3,
    //是否显示移动端代码生成
    "showApp": true,
    //自动去除表前缀
    "autoPre": true,
    //默认生成业务模块名
    "moduleName": "business",
    "author": "admin",
    "tablePrefix": "sys_", //"表前缀（生成类名不会包含表前缀，多个用逗号分隔）",
    "vuePath": "", //前端代码存储路径eg：D:\Work\ZRAdmin-Vue3
    "uniappPath": "D:\\Work" //h5前端代码存储路径
  }
}
