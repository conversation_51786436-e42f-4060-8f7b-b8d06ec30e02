<template>
    <el-select
      v-model="selectedResources"
      multiple
      filterable
      placeholder="请选择资源"
      class="resource-selector"
      @change="handleChange"
    >
      <el-option-group label="资源池">
        <el-option
          v-for="pool in resourcePools"
          :key="'pool_' + pool.id"
          :label="pool.poolName"
          :value="pool.poolName"
        />
      </el-option-group>
      <el-option-group label="资源机">
        <el-option
          v-for="machine in resourceMachines"
          :key="'machine_' + machine.id"
          :label="machine.machineName"
          :value="machine.machineName"
        />
      </el-option-group>
    </el-select>
  </template>
  
  <script>
  import { ref, onMounted, watch } from 'vue';
  import axios from 'axios';
  
  export default {
    name: 'ResourceSelector',
    props: {
      modelValue: {
        type: String,
        default: ''
      }
    },
    emits: ['update:modelValue'],
    
    setup(props, { emit }) {
      const resourcePools = ref([]);
      const resourceMachines = ref([]);
      const selectedResources = ref([]);
  
      // 监听 props.modelValue 的变化
      watch(() => props.modelValue, (newValue) => {
        if (newValue) {
          selectedResources.value = newValue.split('|');
        } else {
          selectedResources.value = [];
        }
      }, { immediate: true });
  
      // 获取资源列表
      const fetchResources = async () => {
        try {
          const response = await axios.get('/api/exeprogram/resources');
          resourcePools.value = response.data.resourcePools;
          resourceMachines.value = response.data.resourceMachines;
        } catch (error) {
          console.error('获取资源列表失败:', error);
        }
      };
  
      // 处理选择变化
      const handleChange = (value) => {
        emit('update:modelValue', value.join('|'));
      };
  
      onMounted(() => {
        fetchResources();
      });
  
      return {
        resourcePools,
        resourceMachines,
        selectedResources,
        handleChange
      };
    }
  };
  </script>
  
  <style scoped>
  .resource-selector {
    width: 100%;
  }
  </style>