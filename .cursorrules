## 使用的开发技术
- 数据库：Mysql （8.0.26 版本）
- WEB后端：ASP.NET Core WEB API （基本的三层架构）,开发语言：C# （.NET 8.0 版本）
- WEB前端：Vue3 + Element Plus
- ORM框架：EntityFrameworkCore
- 客户端桌面应用：C#语言，客户端：Winform，UI界面：SunnyUI，框架：.NETFramework 4.6.2
- 客户端与服务器通讯：SignalR

## 项目的目录结构：
Winform客户端：
Web前端页面：
Web后端服务：
ASP.NET Core WebAPI 接口：
模型：
DTO模型：
业务逻辑：
EF的DbContext：
SQL脚本：

## 所有代码规范
- 所有代码变量不要"_"下滑线开头命名，临时变量内部变量用骆驼命名法。
- C#代码尽量写在一行，这样更美观易读。除非它真的有必要多行。调用方法的参数写成一行，除非它有委托。定义的方法参数写成一行。

## 数据库
- 任何的表主键名称都叫ID，比如Users表的ID叫ID，作为外键叫UserID。外键命名：表名+ID
- 所有ID都用bigint
- sql脚本关键字用小写

## 前端代码风格
- 一个元素里的属性尽量不要换行，保持层次结构
- 尽可能写多点代码注释
- 管理页面默认风格：主页面显示表格数据，点击新增或修改弹出窗口编辑，修改和删除在每行的最后显示。点击删除需要确认才能删除。

## 修改代码规范
- 不能改变无关的代码的注释，如：修改了第三行代码，与第一第二行代码无关，不能改变它们的注释和代码。