﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace RPASystem.Client
{
    public class PackagesMan
    {
        private readonly string programPath;
        private readonly string version;
        private readonly string versionFilePath;
        private string programName;

        public PackagesMan(string programName, string programPath, string version)
        {
            this.programName = programName;
            this.programPath = programPath;
            this.version = version;
            this.versionFilePath = Path.Combine(programPath, "Version.dat");
        }

        /// <summary>
        /// 检查程序包版本,不同则从服务端拉取
        /// </summary>
        public async Task CheckAndUpdateAsync()
        {
            string localVersion = GetLocalVersionAsync();
            // 版本不同或本地版本不存在,需要更新
            if (localVersion != version)
            {
                // 从服务端拉取最新程序包
                await PullLatestPackageAsync();

                // 更新版本号文件
                File.WriteAllText(versionFilePath, version);
            }
        }

        /// <summary>
        /// 获取本地版本号
        /// </summary>
        private string GetLocalVersionAsync()
        {
            if (!File.Exists(versionFilePath))
            {
                return string.Empty;
            }

            return File.ReadAllText(versionFilePath).Trim();
        }

        /// <summary>
        /// 从服务端拉取最新程序包
        /// </summary>
        private async Task PullLatestPackageAsync()
        {
            using (var httpClient = new HttpClient())
            {
                // 构建请求URL
                string requestUrl = $"{MachineMan.BaseUrl}/api/ExeProgram/download/{programName}";

                // 发送GET请求获取程序包
                var response = await httpClient.GetAsync(requestUrl);

                // 检查响应状态
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"从服务器获取程序包失败: {response.StatusCode}");
                }

                // 读取响应内容为字节数组并直接解压
                using (var zipStream = new MemoryStream(await response.Content.ReadAsByteArrayAsync()))
                {
                    // 如果存在则先删除
                    if (Directory.Exists(programPath))
                    {
                        RetryH.Execute(() => Directory.Delete(programPath, true));
                    }
                    // 确保目标目录存在
                    RetryH.Execute(() => Directory.CreateDirectory(programPath));

                    // 直接从内存流解压
                    using (var archive = new ZipArchive(zipStream))
                    {
                        archive.ExtractToDirectory(programPath);
                    }
                }
            }
        }
    }
}

