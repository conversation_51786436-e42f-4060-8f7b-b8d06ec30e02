﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RPASystem.Client
{
    internal static class MachineMan
    {
        public static string BaseUrl => $"http://{ServerIP}:{ServerPort}";
        //public static string HostName => GetHostname();
        public static string MainExeName => "RPASystem.Client";

        public static string ServerIP
        {
            get => ConfigurationManager.AppSettings["ServerIP"];
            set => UpdateAppSetting("ServerIP", value);
        }

        public static string ServerPort
        {
            get => ConfigurationManager.AppSettings["ServerPort"];
            set => UpdateAppSetting("ServerPort", value);
        }

        //const string RegisterKeyName = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";  // 这个不启动
        const string RegisterKeyName = @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run";
        public static bool AutoRun
        {
            get => Registry.GetValue("HKEY_LOCAL_MACHINE\\" + RegisterKeyName, MainExeName, null)?.ToString() != null;
            set { SetAutoStart(value); }
        }

        static void SetAutoStart(bool isAutoStart = true)
        {
            var exePath = Process.GetCurrentProcess().MainModule.FileName;
            if (isAutoStart)
                Registry.SetValue("HKEY_LOCAL_MACHINE\\" + RegisterKeyName, MainExeName, $"{exePath} -hide");
            else
                try { Registry.LocalMachine.OpenSubKey(RegisterKeyName, true)?.DeleteValue(MainExeName, false); } catch { }
        }

        public static string Version { get; set; }

        private static void UpdateAppSetting(string key, string value)
        {
            var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
            var settings = config.AppSettings.Settings;
            if (settings[key] != null)
                settings[key].Value = value;
            else
                settings.Add(key, value);
            config.Save(ConfigurationSaveMode.Modified);
            ConfigurationManager.RefreshSection("appSettings");
        }

        public static string GetHostname()
        {
            try
            {
                using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
                {
                    using (var key = baseKey.OpenSubKey(@"SOFTWARE\WOW6432Node\HUAWEI\V"))
                    {
                        if (key != null)
                        {
                            var value = key.GetValue("WAssName");
                            if (value != null)
                            {
                                return value.ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 获取主机名时发生错误: {ex.Message}");
            }
            return Environment.MachineName;
        }

        public static bool SetHostname(string name)
        {
            try
            {
                // 使用 RegistryKey.OpenBaseKey 方法来指定注册表视图
                using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
                {
                    using (var key = baseKey.CreateSubKey(@"SOFTWARE\WOW6432Node\HUAWEI\V", true))
                    {
                        if (key != null)
                        {
                            key.SetValue("WAssName", name, RegistryValueKind.String);
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 设置主机名时发生错误: {ex.Message}");
            }
            return false;
        }
    }
}
