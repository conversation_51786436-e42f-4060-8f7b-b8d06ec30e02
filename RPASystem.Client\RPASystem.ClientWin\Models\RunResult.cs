using Newtonsoft.Json;

public class RunResult
{
    public bool IsSucceed { get; set; }
    public string ReturnResult { get; set; }
    public string ErrorMessage { get; set; }
    public int? Status { get; set; } // 0: 异常失败, 1: 成功, 2: 警告

    public string ToJson()
    {
        return JsonConvert.SerializeObject(this, new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore
        });
    }

    public static RunResult FromJson(string json)
    {
        return JsonConvert.DeserializeObject<RunResult>(json);
    }
}
