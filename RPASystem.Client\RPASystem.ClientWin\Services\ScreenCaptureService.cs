using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RPASystem.ClientWin.Services
{
    public class ScreenCaptureService : IDisposable
    {
        // 全局配置参数
        public static class Config
        {
            /// <summary>
            /// JPEG压缩质量 (1-100)
            /// </summary>
            public static long JpegQuality { get; set; } = 80;

            /// <summary>
            /// 图像缩放比例 (0-100)，100表示原始大小
            /// </summary>
            public static int ScalePercent { get; set; } = 100;

            /// <summary>
            /// 最小截图间隔（毫秒）
            /// </summary>
            public static int MinCaptureInterval { get; set; } = 500;

            /// <summary>
            /// 自动停止时间（毫秒）
            /// </summary>
            public static int AutoStopTimeout { get; set; } = 10 * 60 * 1000; // 10分钟

            /// <summary>
            /// 图像渲染质量
            /// </summary>
            public static System.Drawing.Drawing2D.CompositingQuality RenderQuality { get; set; } = System.Drawing.Drawing2D.CompositingQuality.HighQuality;

            /// <summary>
            /// 图像插值模式
            /// </summary>
            public static System.Drawing.Drawing2D.InterpolationMode InterpolationMode { get; set; } = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;

            /// <summary>
            /// 图像平滑模式
            /// </summary>
            public static System.Drawing.Drawing2D.SmoothingMode SmoothingMode { get; set; } = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
        }

        private CancellationTokenSource cancellationTokenSource;
        private volatile bool isCapturing = false;
        private readonly Action<byte[]> onImageCaptured;
        private readonly ImageCodecInfo jpegEncoder;
        private readonly EncoderParameters encoderParams;
        private DateTime lastCaptureTime = DateTime.MinValue;
        private Task captureTask;
        private System.Threading.Timer autoStopTimer;

        public ScreenCaptureService(Action<byte[]> onImageCaptured)
        {
            this.onImageCaptured = onImageCaptured;

            // 初始化JPEG编码器和参数
            jpegEncoder = GetEncoder(ImageFormat.Jpeg);
            encoderParams = new EncoderParameters(1);
            encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, Config.JpegQuality);
        }

        public void StartCapture()
        {
            if (isCapturing) return;

            isCapturing = true;
            cancellationTokenSource = new CancellationTokenSource();

            // 启动自动停止定时器
            autoStopTimer = new System.Threading.Timer(
                _ => StopCapture(),
                null,
                Config.AutoStopTimeout,
                Timeout.Infinite
            );

            // 在新的Task中启动截图循环
            captureTask = Task.Run(async () =>
            {
                while (isCapturing && !cancellationTokenSource.Token.IsCancellationRequested)
                {
                    try
                    {
                        var elapsed = DateTime.Now - lastCaptureTime;
                        if (elapsed.TotalMilliseconds < Config.MinCaptureInterval)
                        {
                            await Task.Delay((int)(Config.MinCaptureInterval - elapsed.TotalMilliseconds), cancellationTokenSource.Token);
                            continue;
                        }

                        byte[] imageData = CaptureScreenToBytes();
                        if (imageData != null && imageData.Length > 0)
                        {
                            await Task.Run(() => onImageCaptured?.Invoke(imageData));
                            lastCaptureTime = DateTime.Now;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"截图错误: {ex.Message}");
                        await Task.Delay(1000, cancellationTokenSource.Token);
                    }
                }
            }, cancellationTokenSource.Token);
        }

        private byte[] CaptureScreenToBytes()
        {
            try
            {
                var screen = Screen.PrimaryScreen;
                var width = (screen.Bounds.Width * Config.ScalePercent) / 100;
                var height = (screen.Bounds.Height * Config.ScalePercent) / 100;
                
                using (var bitmap = new Bitmap(screen.Bounds.Width, screen.Bounds.Height))
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.CompositingQuality = Config.RenderQuality;
                    graphics.InterpolationMode = Config.InterpolationMode;
                    graphics.SmoothingMode = Config.SmoothingMode;
                    graphics.CopyFromScreen(0, 0, 0, 0, screen.Bounds.Size);

                    // 濡傛灉闇€瑕佺缉鏀�
                    if (Config.ScalePercent != 100)
                    {
                        using (var resizedBitmap = new Bitmap(width, height))
                        using (var resizeGraphics = Graphics.FromImage(resizedBitmap))
                        {
                            resizeGraphics.CompositingQuality = Config.RenderQuality;
                            resizeGraphics.InterpolationMode = Config.InterpolationMode;
                            resizeGraphics.SmoothingMode = Config.SmoothingMode;
                            resizeGraphics.DrawImage(bitmap, 0, 0, width, height);

                            using (var ms = new MemoryStream())
                            {
                                // 鏇存柊鍘嬬缉璐ㄩ噺
                                encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, Config.JpegQuality);
                                resizedBitmap.Save(ms, jpegEncoder, encoderParams);
                                return ms.ToArray();
                            }
                        }
                    }
                    else
                    {
                        using (var ms = new MemoryStream())
                        {
                            // 鏇存柊鍘嬬缉璐ㄩ噺
                            encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, Config.JpegQuality);
                            bitmap.Save(ms, jpegEncoder, encoderParams);
                            return ms.ToArray();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"鎴浘杩囩▼鍑洪敊: {ex.Message}");
                return null;
            }
        }

        private ImageCodecInfo GetEncoder(ImageFormat format)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageEncoders();
            return Array.Find(codecs, codec => codec.FormatID == format.Guid);
        }

        public void StopCapture()
        {
            try
            {
                isCapturing = false;
                cancellationTokenSource?.Cancel();
                autoStopTimer?.Dispose();
                captureTask?.Wait(1000); // 等待截图任务完成，但最多等待1秒
            }
            catch (Exception ex)
            {
                Console.WriteLine($"停止截图错误: {ex.Message}");
            }
        }

        public void Dispose()
        {
            StopCapture();
            cancellationTokenSource?.Dispose();
            encoderParams?.Dispose();
            autoStopTimer?.Dispose();
        }
    }
}