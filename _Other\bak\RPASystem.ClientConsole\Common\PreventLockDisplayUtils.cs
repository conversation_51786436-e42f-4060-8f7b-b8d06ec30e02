﻿using System;
using System.Runtime.InteropServices;


/// <summary>
/// 阻止屏幕锁屏
/// </summary>
public class PreventLockDisplayUtils
{
    [Flags]
    private enum ExecutionFlag : uint
    {
        /// <summary>
        /// System
        /// </summary>
        System = 0x00000001,

        /// <summary>
        /// Display
        /// </summary>
        Display = 0x00000002,

        /// <summary>
        /// Continus
        /// </summary>
        Continus = 0x80000000,
    }

    /// <summary>
    /// 阻止系统休眠，直到线程结束恢复休眠策略
    /// </summary>
    /// <param name="includeDisplay">是否阻止关闭显示器</param>
    public static void PreventSleep(bool includeDisplay = false)
    {
        if (includeDisplay)
            SetThreadExecutionState(ExecutionFlag.System | ExecutionFlag.Display | ExecutionFlag.Continus);
        else
            SetThreadExecutionState(ExecutionFlag.System | ExecutionFlag.Continus);
    }

    /// <summary>
    /// 恢复系统休眠策略
    /// </summary>
    public static void ResotreSleep()
    {
        SetThreadExecutionState(ExecutionFlag.Continus);
    }

    /// <summary>
    /// 重置系统休眠计时器
    /// </summary>
    /// <param name="includeDisplay">是否阻止关闭显示器</param>
    public static void ResetSleepTimer(bool includeDisplay = false)
    {
        if (includeDisplay)
            SetThreadExecutionState(ExecutionFlag.System | ExecutionFlag.Display);
        else
            SetThreadExecutionState(ExecutionFlag.System);
    }

    #region Windows API  
    /**
     * 它就一个flags参数，这个参数有三个选项，可以组合使用。主要说明如下：
     * 只使用Continus参数时，则是恢复系统休眠策略。
     * 不使用Continus参数时，实现阻止系统休眠或显示器关闭一次
     * 组合使用Continus参数时，实现阻止系统休眠或显示器关闭至线程终止
     */

    [DllImport("kernel32.dll")]
    private static extern uint SetThreadExecutionState(ExecutionFlag flags);
    #endregion
}
