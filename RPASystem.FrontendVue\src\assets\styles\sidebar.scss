.el-menu-item,
.el-sub-menu {
  .svg-icon + span {
    margin-left: 5px;
  }
}
#app {
  .sidebar {
    position: relative;
    overflow-y: hidden;
    z-index: 1001;
    transition: width 0.28s ease;
    background-color: var(--base-menu-background);
    height: 100%;
    display: flex;
    flex-direction: column;
    -webkit-box-shadow: 2px 0 14px rgb(0 21 41 / 10%);
    box-shadow: 2px 0 14px rgb(0 21 41 / 10%);

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }
    // 去掉el-menu边框
    .el-menu {
      border: none;
    }

    // [class^='el-icon'] {
    //   width: 1em;
    //   height: 1em;
    //   font-size: unset;
    // }
  }

  // 展开sidebar状态设置svg-icon边距
  .openSidebar {
    .sidebar {
      transform: translate(0);
    }
  }
  // 隐藏侧边栏样式
  .hideSidebar {
    .el-aside {
      --el-aside-width: 60px;
    }
    // 隐藏箭头
    .el-sub-menu {
      overflow: hidden;

      & > .el-sub-menu__title {
        .el-sub-menu__icon-arrow {
          display: none;
        }
      }
    }

    // 折叠状态下
    .el-menu--collapse {
      .menu-icon {
        margin-right: 0;
      }
    }
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar {
      transition: transform 0.28s;
      position: fixed;
      // background: var(--base-menu-background, #fff);
    }

    &.hideSidebar {
      .sidebar {
        pointer-events: none;
        transform: translate3d(-220px, 0, 0);
        transition-duration: 0.3s;
      }
    }
  }
}
