using Microsoft.EntityFrameworkCore;
using RPASystem.Model;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.IO;
using RPASystem.Service;
using Infrastructure.Attribute;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace RPASystem.Service
{
    [AppService(ServiceType = typeof(IResourceMachineService), ServiceLifetime = LifeTime.Scoped)]
    public class ResourceMachineService : IResourceMachineService
    {
        private readonly RPASystemDbContext context;
        private readonly ILogger<ResourceMachineService> logger;

        public ResourceMachineService(RPASystemDbContext context, ILogger<ResourceMachineService> logger)
        {
            this.context = context;
            this.logger = logger;
        }

        public async Task RegisterOrUpdateMachineAsync(string machineName, string clientVersion = null, bool isLatestVersion = false)
        {
            var machine = await context.ResourceMachines.SingleOrDefaultAsync(m => m.MachineName == machineName);
            if (machine == null)
            {
                machine = new ResourceMachine
                {
                    MachineName = machineName,
                    LastActivityTime = DateTime.Now,
                    TaskStatus = TaskStatusEnum.空闲,
                    ClientVersion = clientVersion,
                    IsLatestVersion = isLatestVersion
                };
                context.ResourceMachines.Add(machine);
            }
            else
            {
                machine.LastActivityTime = DateTime.Now;
                if (!string.IsNullOrEmpty(clientVersion))
                {
                    machine.ClientVersion = clientVersion;
                    machine.IsLatestVersion = isLatestVersion;
                }
            }

            await context.SaveChangesAsync();
        }

        public async Task UpdateMachineStatusAsync(string machineName, TaskStatusEnum status)
        {
            var machine = await context.ResourceMachines
                .FirstOrDefaultAsync(m => m.MachineName == machineName);
            if (machine != null)
            {
                machine.TaskStatus = status;
                machine.LastActivityTime = DateTime.Now;
                await context.SaveChangesAsync();
            }
        }

        public async Task<List<ResourceMachine>> GetIdleAsync()
        {
            return await context.ResourceMachines
                .Where(t => t.TaskStatus == TaskStatusEnum.空闲 && 
                           t.IsLatestVersion && 
                           t.MachineType != MachineTypeEnum.普通机)
                .ToListAsync();
        }

        public async Task<List<ResourceMachine>> GetAllResourceMachinesAsync()
        {
            return await context.ResourceMachines.ToListAsync();
        }

        public async Task<List<ResourceMachine>> GetResourceMachinesAsync(string? searchTerm = null)
        {
            var query = context.ResourceMachines.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(m => m.MachineName.Contains(searchTerm));
            }

            var machines = await query.ToListAsync();

            return machines;
        }

        public async Task<List<ResourceMachine>> GetResourceMachinesAsync(string? searchTerm = null, bool? offlineOverSevenDays = null)
        {
            var query = context.ResourceMachines.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(m => m.MachineName.Contains(searchTerm));
            }

            if (offlineOverSevenDays == true)
            {
                var sevenDaysAgo = DateTime.Now.AddDays(-7);
                query = query.Where(m => m.TaskStatus == TaskStatusEnum.离线 && m.LastActivityTime <= sevenDaysAgo);
            }

            var machines = await query.ToListAsync();
            return machines;
        }

        public async Task UpdateResourceInfoAsync(ResourceInfoDto resourceInfo)
        {
            var machine = await context.ResourceMachines.FirstOrDefaultAsync(m => m.MachineName == resourceInfo.MachineName);
            if (machine != null)
            {
                machine.ComputerName = resourceInfo.ComputerName;
                machine.CpuUsage = resourceInfo.CpuUsage;
                machine.MemoryUsage = resourceInfo.MemoryUsage;
                machine.DiskUsage = JsonSerializer.Serialize(resourceInfo.DiskUsage);
                machine.LastActivityTime = DateTime.Now;
                machine.IpAddress = resourceInfo.IpAddress;
                await context.SaveChangesAsync();
            }
            else
            {
                logger.LogWarning($"Attempted to update resource info for non-existent machine: {resourceInfo.MachineName}");
            }
        }

        public async Task<bool> DeleteResourceMachineAsync(long id)
        {
            try
            {
                var machine = await context.ResourceMachines.FindAsync(id);
                if (machine == null)
                {
                    logger.LogWarning($"尝试删除不存在的资源机器，ID: {id}");
                    return false;
                }

                context.ResourceMachines.Remove(machine);
                await context.SaveChangesAsync();
                // logger.LogInformation($"成功删除资源机器，ID: {id}, 机器名: {machine.MachineName}");
                return true;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"删除资源机器时发生错误，ID: {id}");
                return false;
            }
        }

        public async Task InitializeResourceMachineStatusAsync()
        {
            try
            {
                var nonOfflineMachines = await context.ResourceMachines
                    .Where(m => m.TaskStatus != TaskStatusEnum.离线)
                    .ToListAsync();

                foreach (var machine in nonOfflineMachines)
                {
                    machine.TaskStatus = TaskStatusEnum.离线;
                    machine.LastActivityTime = DateTime.Now;
                    machine.RunningExeNames = null;
                }

                await context.SaveChangesAsync();
                logger.LogInformation($"已将 {nonOfflineMachines.Count} 台非离线资源机状态重置为离线");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "初始化资源机状态时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 任务名称（多个任务用"|"分隔）。资源机更新逻辑：任务名为空则资源机状态为"空闲"，任务名不为空则资源机状态为"运行中"
        /// 任务列表更新逻辑：查找所有正在运行的任务且资源机名与machineName一致，列表中不包含taskIds中的任务，则更新任务状态为"失败"
        /// </summary>
        public async Task UpdateResourceMachineTaskStatusAsync(string machineName, List<long> runningTaskIds)
        {
            var machine = await context.ResourceMachines.FirstOrDefaultAsync(m => m.MachineName == machineName);
            if (machine != null)
            {
                // 更新资源机状态
                if (runningTaskIds.Count == 0)
                {
                    machine.TaskStatus = TaskStatusEnum.空闲;
                }
                else
                {
                    machine.TaskStatus = TaskStatusEnum.任务运行中;
                }
                // 更新任务列表
                var tasks = await context.JobTasks.Where(t => t.AssignedResourceMachine == machineName && t.Status == JobTaskStatusEnum.Running).ToListAsync();
                foreach (var task in tasks)
                {
                    if (!runningTaskIds.Contains(task.ID))
                    {
                        task.Status = JobTaskStatusEnum.Failed;
                        task.Notes += "任务丢失";
                        task.EndTime = DateTime.Now;
                    }
                }
                await context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 将所有在线的资源机状态设置为"任务运行中"
        /// </summary>
        public async Task SetAllOnlineMachinesToRunningAsync()
        {
            var onlineMachines = await context.ResourceMachines
                .Where(m => m.TaskStatus != TaskStatusEnum.离线)
                .ToListAsync();

            foreach (var machine in onlineMachines)
            {
                machine.TaskStatus = TaskStatusEnum.任务运行中;
            }

            await context.SaveChangesAsync();
        }

        /// <summary>
        /// 将所有资源机标记为非最新版
        /// </summary>
        public async Task SetAllMachinesToNonLatestVersionAsync()
        {
            var machines = await context.ResourceMachines.ToListAsync();
            foreach (var machine in machines)
            {
                machine.IsLatestVersion = false;
            }
            await context.SaveChangesAsync();
        }

        public async Task<bool> UpdateMachineTypeAsync(long id, MachineTypeEnum machineType)
        {
            try
            {
                var machine = await context.ResourceMachines.FindAsync(id);
                if (machine == null)
                {
                    return false;
                }

                machine.MachineType = machineType;
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"更新资源机类型时发生错误，ID: {id}");
                return false;
            }
        }
    }
}
