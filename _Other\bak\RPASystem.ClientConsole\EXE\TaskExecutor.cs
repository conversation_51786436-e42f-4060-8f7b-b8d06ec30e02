﻿using Microsoft.AspNetCore.SignalR.Client;
using RPASystem.WebApi.Models;
using System;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Text.Json;
using System.Threading.Tasks;

namespace RPASystem.ClientConsole
{
    public class TaskExecutor
    {
        public async Task ExecuteTaskAsync(HubConnection connection, JobModel jobModel)
        {
            int exitCode = 0;
            try
            {
                string timestamp = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                string tempFolder = Path.Combine(Path.GetTempPath(), timestamp);
                Directory.CreateDirectory(tempFolder);

                string zipPath = Path.Combine(tempFolder, "job.zip");
                File.WriteAllBytes(zipPath, jobModel.ZipFile);

                ZipFile.ExtractToDirectory(zipPath, tempFolder);
                File.Delete(zipPath);

                string exePath = Path.Combine(tempFolder, "Main.exe");
                string arguments = jobModel.Parameter;

                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = exePath,
                    Arguments = arguments,
                    WorkingDirectory = tempFolder,
                    CreateNoWindow = false
                });
                process.WaitForExit();
                exitCode = process.ExitCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing task: {ex.Message}");
                exitCode = -1;
            }
            finally
            {
                await connection.SendAsync("TaskCompleted", jobModel.Id, exitCode == 0 ? JobTaskStatusEnum.Success : JobTaskStatusEnum.Failed, exitCode.ToString()).ConfigureAwait(false);
            }
        }
    }
}
