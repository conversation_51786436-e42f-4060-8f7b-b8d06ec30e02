
namespace ZR.Model.Business
{
    /// <summary>
    /// 功能名只是演示
    /// </summary>
    [SugarTable("justdemo")]
    public class Justdemo
    {
        /// <summary>
        /// Id 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = false)]
        public long Id { get; set; }

        /// <summary>
        /// DemoName 
        /// </summary>
        public string DemoName { get; set; }

        /// <summary>
        /// DemoRemark 
        /// </summary>
        public string DemoRemark { get; set; }

    }
}