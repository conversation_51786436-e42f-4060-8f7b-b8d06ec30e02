<template>
    <div>
      <!-- 顶部操作栏 -->
      <div class="operation-bar">
        <div class="left-section">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="任务名称">
              <el-input v-model="searchForm.taskName" placeholder="请输入任务名称" clearable style="width: 150px;"/>
            </el-form-item>
            <el-form-item label="程序名称">
              <el-input v-model="searchForm.programName" placeholder="请输入程序名称" clearable style="width: 150px;"/>
            </el-form-item>
            <el-form-item label="资源机">
              <el-input v-model="searchForm.resourceMachine" placeholder="请输入资源机" clearable style="width: 150px;"/>
            </el-form-item>
            <el-form-item label="任务类型">
              <el-select v-model="searchForm.taskType" placeholder="请选择任务类型" clearable style="width: 120px;">
                <el-option label="普通任务" :value="0" />
                <el-option label="编排任务" :value="1" />
                <el-option label="系统编排拆分任务" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="right-section">
          <el-button type="primary" @click="showAddDialog">新增任务</el-button>
        </div>
      </div>

      <el-table :data="treeData" row-key="jobTaskId" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" style="width: 100%" :row-class-name="tableRowClassName">
        <el-table-column prop="jobTaskId" label="任务ID" width="100" align="right">
          <template #default="scope">
            <span :class="{ 'child-task': scope.row.parentTaskID !== 0 }">{{ scope.row.jobTaskId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="jobTaskName" label="任务名称" min-width="90" show-overflow-tooltip>
          <template #default="scope">
            <span class="clickable-text" @click="showEditDialog(scope.row)">{{ scope.row.jobTaskName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="exeProgramName" label="程序名称" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <span 
              v-if="scope.row.taskType === 2" 
              class="clickable-text"
              @click="showSubTasks(scope.row)"
            >
              {{ scope.row.exeProgramName }}
            </span>
            <span v-else>{{ scope.row.exeProgramName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="taskType" label="任务类型" width="120" show-overflow-tooltip >
          <template #default="scope">
            <el-tag 
              :type="getTaskTypeStyle(scope.row.taskType)"
              effect="plain"
            >
              {{ 
                scope.row.taskType === 0 ? '普通任务' : 
                scope.row.taskType === 1 ? '编排任务' : 
                scope.row.taskType === 2 ? '系统拆分任务' : 
                '未知类型'
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="70" align="center" />
        <el-table-column prop="createdAt" label="创建时间" width="85"  show-overflow-tooltip />
        <el-table-column prop="startTime" label="开始时间" width="85" show-overflow-tooltip/>
        <el-table-column label="运行时长" width="130" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip 
              v-if="scope.row.endTime"
              :content="`结束时间: ${scope.row.endTime}`"
              placement="top"
              effect="light"
            >
              <span>{{ calculateDuration(scope.row.startTime, scope.row.endTime) }}</span>
            </el-tooltip>
            <span v-else>{{ calculateDuration(scope.row.startTime, scope.row.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="resourceSelection" label="资源选择" width="100" show-overflow-tooltip />
        <el-table-column prop="assignedResourceMachine" label="资源机" width="120" show-overflow-tooltip>
          <template #default="scope">
            <span 
              v-if="scope.row.status === 'Running' && scope.row.assignedResourceMachine" 
              class="clickable-text"
              @click="handleRemoteDesktop(scope.row.assignedResourceMachine)"
            >
              {{ scope.row.assignedResourceMachine }}
            </span>
            <span v-else>{{ scope.row.assignedResourceMachine }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="inputParameters" label="输入参数" width="100" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip 
              v-if="hasInputFile(scope.row.inputParameters)"
              :content="scope.row.inputParameters"
              placement="top"
              effect="light"
            >
              <span 
                class="clickable-text"
                @click="handleInputFileDownload(scope.row.inputParameters)"
              >
                下载输入件
              </span>
            </el-tooltip>
            <span v-else>{{ scope.row.inputParameters }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="outputResults" label="输出结果" width="80" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip 
              v-if="hasReturnResult(scope.row.outputResults)"
              :content="scope.row.outputResults"
              placement="top"
              effect="light"
            >
              <span 
                class="clickable-text"
                @click="copyReturnResult(scope.row.outputResults)"
              >
                复制结果
              </span>
            </el-tooltip>
            <span v-else>{{ scope.row.outputResults }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <!-- 普通任务显示普通状态标签 -->
            <template v-if="scope.row.taskType === 0">
              <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
            </template>
            
            <!-- 系统编排拆分任务显示进度条 -->
            <template v-else-if="scope.row.taskType === 2">
              <el-tooltip placement="top" :show-after="100">
                <template #content>
                  <div style="text-align: left; white-space: pre-wrap;">
                    总状态：{{ scope.row.status }}<br>
                    总任务数：{{ scope.row.childTaskStats?.total || 0 }}<br>
                    成功：{{ scope.row.childTaskStats?.success || 0 }}<br>
                    运行中：{{ scope.row.childTaskStats?.running || 0 }}<br>
                    失败：{{ scope.row.childTaskStats?.failed || 0 }}<br>
                    等待中：{{ scope.row.childTaskStats?.pending || 0 }}<br>
                    已取消：{{ scope.row.childTaskStats?.cancelled || 0 }}
                  </div>
                </template>
                <div class="progress-container">
                  <div class="progress-bar">
                    <div class="progress-segment success" :style="{ width: getProgressWidth(scope.row, 'Success') }"></div>
                    <div class="progress-segment running" :style="{ width: getProgressWidth(scope.row, 'Running') }"></div>
                    <div class="progress-segment failed" :style="{ width: getProgressWidth(scope.row, 'Failed') }"></div>
                    <div class="progress-segment pending" :style="{ width: getProgressWidth(scope.row, 'Pending') }"></div>
                    <div class="progress-segment cancelled" :style="{ width: getProgressWidth(scope.row, 'Cancelled') }"></div>
                  </div>
                </div>
              </el-tooltip>
            </template>

            <!-- 编排任务显示普通状态标签 -->
            <template v-else>
              <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" min-width="100" show-overflow-tooltip />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <!-- 重试按钮 -->
            <el-tooltip content="重试" placement="top" effect="light" >
              <el-button v-if="scope.row.status !== 'Pending' && scope.row.status !== 'Running'" type="primary" :icon="RefreshRight" circle size="small" @click="retryJobTask(scope.row.jobTaskId)" />
            </el-tooltip>

            <!-- 停止按钮 -->
            <el-tooltip content="停止" placement="top" effect="light" >
              <el-button v-if="scope.row.status === 'Running' || scope.row.status === 'Pending'" type="warning" :icon="VideoPause" circle size="small" @click="stopJobTask(scope.row.jobTaskId)"/>
            </el-tooltip>

            <!-- 删除按钮 -->
            <el-tooltip content="删除" placement="top" effect="light">
              <el-button type="danger" :icon="Delete" circle size="small" @click="handleDelete(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNumber"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          :background="true"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>

      <el-dialog v-model="dialogVisible" title="新增任务">
        <el-form :model="newJobTask" label-width="120px">
          <el-form-item label="程序列表">
            <el-select 
              v-model="newJobTask.exeProgramId" 
              placeholder="请选择程序" 
              @change="handleProgramChange" 
              filterable
            >
              <el-option 
                v-for="program in exePrograms" 
                :key="program.id" 
                :label="program.programName" 
                :value="program.id" 
              />
            </el-select>
          </el-form-item>

          <el-form-item label="优先级">
            <el-input-number v-model="newJobTask.priority" :min="1" :max="100" :step="1" />
          </el-form-item>

          <el-form-item label="程序参数">
            <job-task-parameters-editor
              :program-input-parameters="selectedProgramInputParameters"
              :initial-parameters="newJobTask.inputParameters"
              :task-type="newJobTask.taskType"
              @update:parameters="updateInputParameters"
              @file-type-error="handleFileTypeError"
            />
          </el-form-item>

          <!-- 只有当选择RPA类型程序时才显示任务类型 -->
          <template v-if="isRPAProgram">
            <el-form-item label="任务类型">
              <el-radio-group v-model="newJobTask.taskType">
                <el-radio-button :label="0">普通任务</el-radio-button>
                <el-radio-button :label="2">系统编排拆分任务</el-radio-button>
              </el-radio-group>
            </el-form-item>

            <!-- 系统编排拆分任务的额外参数 -->
            <template v-if="newJobTask.taskType === 2">
              <el-form-item label="每份拆分数量">
                <el-input-number 
                  v-model="splitTaskConfig.excelPerSplitNum" 
                  :min="1" 
                  :step="1" 
                  placeholder="请输入拆分数量"
                />
              </el-form-item>
              
              <el-form-item label="合并类型">
                <el-select v-model="splitTaskConfig.mergeType" placeholder="请选择合并类型" clearable>
                  <el-option label="不覆盖文件" value="0" />
                  <el-option label="覆盖文件" value="1" />
                  <el-option label="不覆盖文件合并相同Excel" value="2" />
                  <el-option label="覆盖文件合并相同Excel" value="3" />
                </el-select>
              </el-form-item>
            </template>
          </template>

          <el-form-item label="资源选择" v-if="showResourceSelection">
            <resource-selector 
              :model-value="newJobTask.resourceSelection"
              @update:model-value="val => newJobTask.resourceSelection = val"
            />
          </el-form-item>

          <!-- 在资源选择后面添加备注字段 -->
          <el-form-item label="备注">
            <el-input 
              v-model="newJobTask.notes" 
              placeholder="请输入备注信息"
            />
          </el-form-item>

        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false" :disabled="isSaving">取消</el-button>
            <el-button 
              type="primary" 
              @click="createJobTask" 
              :loading="isSaving"
              :disabled="isSaving"
            >
              {{ isSaving ? '保存中...' : '保存' }}
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加全屏加载 -->
      <el-loading 
        v-model:visible="fullscreenLoading" 
        fullscreen 
        element-loading-text="正在创建任务..."
        element-loading-background="rgba(0, 0, 0, 0.8)"
      />

      <!-- 添加编辑对话框 -->
      <el-dialog v-model="editDialogVisible" title="编辑任务">
        <el-form :model="editJobTask" label-width="120px">
          <el-form-item label="优先级">
            <el-input-number 
              v-model="editJobTask.priority" 
              :min="1" 
              :max="100" 
              :step="1"
              placeholder="不修改留空"
              clearable
            />
          </el-form-item>

          <el-form-item label="资源选择">
            <resource-selector 
              :model-value="editJobTask.resourceSelection"
              @update:model-value="val => editJobTask.resourceSelection = val"
            />
          </el-form-item>

          <el-form-item label="状态">
            <el-select v-model="editJobTask.status" placeholder="不修改请留空" clearable>
              <el-option label="待运行" :value="0" />
              <el-option label="运行中" :value="1" />
              <el-option label="成功" :value="2" />
              <el-option label="失败" :value="3" />
              <el-option label="已取消" :value="4" />
            </el-select>
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="editJobTask.notes" placeholder="不修改请留空" clearable />
          </el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleEditSubmit" :loading="isSaving">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加子任务弹窗组件 -->
      <sub-task-dialog
        v-if="subTaskDialog.visible"
        v-model:visible="subTaskDialog.visible"
        :parent-task-id="subTaskDialog.parentTaskId"
        :parent-task-name="subTaskDialog.parentTaskName"
      />

      <!-- 远程桌面组件 -->
      <remote-desktop
        v-if="remoteDesktop.visible"
        v-model:visible="remoteDesktop.visible"
        :machine-name="remoteDesktop.machineName"
      />
    </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
import axios from 'axios';
import JobTaskParametersEditor from '../components/JobTaskParametersEditor.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Download, RefreshRight, VideoPause, Delete } from '@element-plus/icons-vue'
import * as signalR from '@microsoft/signalr';
import { BaseUrl } from '@/main';
import ResourceSelector from '../components/ResourceSelector.vue';
import { copyToClip } from '@/utils/clipboard'
import SubTaskDialog from '../components/SubTaskDialog.vue'
import RemoteDesktop from '@/components/RemoteDesktop.vue'

export default {
  components: {
    JobTaskParametersEditor,
    Download,
    ResourceSelector,
    SubTaskDialog,
    RemoteDesktop
  },
  setup() {
    const jobTasks = ref([]);
    const exePrograms = ref([]);
    const dialogVisible = ref(false);
    const newJobTask = ref({
      priority: 10,
      exeProgramId: null,
      inputParameters: '',
      taskType: 0,
      resourceSelection: '',
      notes: ''
    });

    // 定时器引用
    let timer = null;

    // 修改树形结构转换逻辑
    const treeData = computed(() => {
      const tree = [];
      const map = {};

      jobTasks.value.forEach(task => {
        // 创建对象，避免直接修改原始数据
        const newTask = { ...task, children: [] };
        map[task.jobTaskId] = newTask;
        
        if (task.parentTaskID === 0) {
          tree.push(newTask);
        } else {
          if (!map[task.parentTaskID]) {
            // 如果父任务不存在，将其作顶级任务
            tree.push(newTask);
          } else {
            map[task.parentTaskID].children.push(newTask);
          }
        }
      });

      return tree;
    });

    const searchForm = ref({
      taskName: '',
      programName: '',
      resourceMachine: '',
      taskType: null
    });

    const pagination = ref({
      pageNumber: 1,
      pageSize: 10,
      total: 0
    });

    // 修改获取任务列表的方法
    const fetchJobTasks = async () => {
      try {
        const params = {
          ...searchForm.value,
          pageNumber: pagination.value.pageNumber,
          pageSize: pagination.value.pageSize
        };
        
        const response = await axios.get('/api/jobtask/search', { params });
        jobTasks.value = response.data.items;
        pagination.value.total = response.data.totalCount;
      } catch (error) {
        console.error('获取任务列表失败:', error);
        ElMessage.error('获取任务列表失败');
      }
    };

    const handleSearch = () => {
      pagination.value.pageNumber = 1;
      fetchJobTasks();
    };

    const resetSearch = () => {
      searchForm.value = {
        taskName: '',
        programName: '',
        resourceMachine: '',
        taskType: null
      };
      handleSearch();
    };

    const handlePageChange = (page) => {
      pagination.value.pageNumber = page;
      fetchJobTasks();
    };

    const fetchExePrograms = async () => {
      try {
        // 修改为使用新的接口路径
        const response = await axios.get('/api/exeprogram/GetAllExeProgramsForList');
        exePrograms.value = response.data;
      } catch (error) {
        console.error('获取程序列表失败:', error);
      }
    };

    // 重置表单方法
    const resetForm = () => {
      newJobTask.value = {
        priority: 10,
        exeProgramId: '',
        inputParameters: '{}',
        taskType: 0,
        notes: ''
      };
      splitTaskConfig.value = {
        excelPerSplitNum: 10,
        mergeType: ''
      };
      selectedProgramInputParameters.value = '[]';
    };

    const showAddDialog = () => {
      resetForm();
      dialogVisible.value = true;
    };

    const isSaving = ref(false);
    const fullscreenLoading = ref(false);

    const createJobTask = async () => {
      if (isSaving.value) return;

      try {
        isSaving.value = true;
        fullscreenLoading.value = true;

        // 如果是系统编排拆分任务，需要合并splitTaskConfig到输入参数中
        if (newJobTask.value.taskType === 2) {
          const currentParams = JSON.parse(newJobTask.value.inputParameters || '{}');
          const mergedParams = {
            ...currentParams,
            ExcelPerSplitNum: splitTaskConfig.value.excelPerSplitNum.toString(),
          };
          
          // 只有当选择了合并类型时才添加 MergeType 字段
          if (splitTaskConfig.value.mergeType !== '') {
            mergedParams.MergeType = splitTaskConfig.value.mergeType;
          }
          
          newJobTask.value.inputParameters = JSON.stringify(mergedParams);
        }

        // 验证参数
        if (!validateSplitTaskParameters(newJobTask.value.inputParameters)) {
          isSaving.value = false;
          fullscreenLoading.value = false;
          return;
        }

        // 构造OrchestrationTaskDto
        const orchestrationTask = {
          programName: exePrograms.value.find(p => p.id === newJobTask.value.exeProgramId)?.programName,
          inputParameters: newJobTask.value.inputParameters,
          taskPriority: newJobTask.value.priority,
          taskType: newJobTask.value.taskType,
          resourceSelection: newJobTask.value.resourceSelection,
          notes: newJobTask.value.notes
        };

        // 使用新的接口创建任务
        const response = await axios.post('/api/orchestration/createOrcJobTask', orchestrationTask);
        
        dialogVisible.value = false;
        ElMessage.success('创建任务成功');
        resetForm();
        await fetchJobTasks();
      } catch (error) {
        console.error('创建任务失败:', error);
        ElMessage.error('创建任务失败: ' + (error.response?.data?.error || error.message));
      } finally {
        isSaving.value = false;
        fullscreenLoading.value = false;
      }
    };

    const handleDelete = (row) => {
      return ElMessageBox.confirm(
        '此操作将永久删除该任务及其所有子任务，是否继续？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          axios.delete(`/api/jobtask/${row.jobTaskId}`).then(() => {
            ElMessage.success('删除成功');
            fetchJobTasks();
          });
        })
        .catch(() => {
          // 用户取消时不显示提示
        });
    };

    const retryJobTask = async (id) => {
      try {
        await ElMessageBox.confirm(
          '确定要重试该任务吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        await axios.post(`/api/jobtask/retry/${id}`);
        ElMessage.success('已开始重试任务');
        await fetchJobTasks();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重试任务失败:', error);
          ElMessage.error('重试任务失败');
        }
      }
    };

    const stopJobTask = async (id) => {
      try {
        await ElMessageBox.confirm(
          '确定要停止该任务吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        await axios.post(`/api/jobtask/stop/${id}`);
        ElMessage.success('已发送停止指令');
        await fetchJobTasks();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('停止任务失败:', error);
          ElMessage.error('停止任务失败');
        }
      }
    };

    const selectedProgramInputParameters = ref('[]');

    const handleProgramChange = async (programId) => {
      // 获取选中的程序
      const selectedProgram = exePrograms.value.find(p => p.id === programId);
      if (!selectedProgram) return;

      // 设置程序类型
      isRPAProgram.value = selectedProgram.programType === 0;
      
      try {
        const response = await axios.get(`/api/exeprogram/${programId}`);
        selectedProgramInputParameters.value = response.data.inputParameters;
        
        // 自动填充资源选择 - 使用nextTick确保DOM更新
        await nextTick();
        if (selectedProgram.resourceSelection) {
          // 使用新的引用更新对象
          newJobTask.value = {
            ...newJobTask.value,
            resourceSelection: selectedProgram.resourceSelection
          };
        } else {
          newJobTask.value = {
            ...newJobTask.value,
            resourceSelection: ''
          };
        }
      } catch (error) {
        console.error('获取程序参数失败:', error);
        ElMessage.error('获取程序参数失败');
      }
    };

    const updateInputParameters = (newParameters) => {
      newJobTask.value.inputParameters = newParameters;
    };

    const validateSplitTaskParameters = (parameters) => {
      if (newJobTask.value.taskType === 2) {
        try {
          const params = JSON.parse(parameters);
          if (!params.InputFile || params.InputFile === '') {
            ElMessage.error('系统编排拆分任务必须上传InputFile文件');
            return false;
          }
        } catch (error) {
          ElMessage.error('参数格式错误');
          return false;
        }
      }
      return true;
    };

    const handleFileTypeError = (message) => {
      ElMessage.error(message);
    };

    const splitTaskConfig = ref({
      excelPerSplitNum: 10,
      mergeType: ''
    });

    // 修改 isRPAProgram 的判断逻辑
    const isRPAProgram = computed(() => {
      if (!newJobTask.value.exeProgramId) return false;
      const selectedProgram = exePrograms.value.find(p => p.id === newJobTask.value.exeProgramId);
      // 修改判断条件：type === 0 表示RPA程序
      return selectedProgram?.programType === 0;
    });

    // 修改 tableRowClassName 方法
    const tableRowClassName = ({ row }) => {
      // 只保留子任务样式
      return row.parentTaskID !== 0 ? 'child-task-row' : '';
    }

    // 添加状态标签样式方法
    const getStatusType = (status) => {
      const statusMap = {
        'Pending': 'info',
        'Running': 'primary',
        'Success': 'success',
        'Failed': 'danger',
        'Cancelled': 'warning'
      };
      return statusMap[status] || 'info';
    };

    // 检查是否有InputFile
    const hasInputFile = (inputParameters) => {
      try {
        const params = JSON.parse(inputParameters || '{}');
        return params.InputFile && params.InputFile !== '';
      } catch {
        return false;
      }
    };

    // 处理输入文件下载
    const handleInputFileDownload = async (inputParameters) => {
      try {
        const params = JSON.parse(inputParameters || '{}');
        if (params.InputFile) {
          const fileId = params.InputFile;
          
          // 下载文件
          const response = await axios.get(`/api/filestorage/${fileId}`, { 
            responseType: 'blob',
            headers: { 'Accept': 'application/octet-stream' }
          });
          
          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement('a');
          link.href = url;
          
          // 生成日期时间格式的文件名
          const now = new Date();
          const fileName = now.getFullYear() +
            String(now.getMonth() + 1).padStart(2, '0') +
            String(now.getDate()).padStart(2, '0') +
            String(now.getHours()).padStart(2, '0') +
            String(now.getMinutes()).padStart(2, '0') +
            String(now.getSeconds()).padStart(2, '0') +
            '.xlsx';
          
          link.setAttribute('download', fileName);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }
      } catch (error) {
        console.error('下载文件失败:', error);
        ElMessage.error('下载文件失败，请重试');
      }
    };

    // 检查是否有ReturnResult
    const hasReturnResult = (outputResults) => {
      try {
        const result = JSON.parse(outputResults || '{}');
        return result.ReturnResult !== undefined;
      } catch {
        return false;
      }
    };

    // 修改 copyReturnResult 方法
    const copyReturnResult = (outputResults) => {
      try {
        const result = JSON.parse(outputResults || '{}')
        if (result.ReturnResult !== undefined) {
          copyToClip(result.ReturnResult)
        }
      } catch (error) {
        console.error('解析输出结果失败:', error)
        ElMessage.error('解析输出结果失败')
      }
    };

    // 添加处理页面大小变化的方法
    const handleSizeChange = (size) => {
      pagination.value.pageSize = size;
      pagination.value.pageNumber = 1; // 切换页面大小时重置为第一页
      fetchJobTasks();
    };

    let connection = null;

    // 添加 SignalR 连接初始化
    const initSignalRConnection = async () => {
      try {
        connection = new signalR.HubConnectionBuilder()
          .withUrl(`${BaseUrl}/resourceMachineHub`)
          .build();

        connection.on('RefreshJobTasks', () => {
          console.log('RefreshJobTasks signal received');
          fetchJobTasks();
        });

        await connection.start();
        console.log('SignalR Connected');
      } catch (err) {
        console.error('SignalR Connection Error: ', err);
        ElMessage.error('实时连接失败，页面更新可能不及时');
      }
    };

    // 添加计算属性来控制资源选择的显示
    const showResourceSelection = computed(() => {
      if (!newJobTask.value.exeProgramId) return false;
      const selectedProgram = exePrograms.value.find(p => p.id === newJobTask.value.exeProgramId);
      // 如果是编排任务类型（programType === 1），则不显示资源选择
      return selectedProgram?.programType !== 2;
    });

    const getChildTasksTotal = (row) => {
      return row.children?.length || 0;
    };

    const getChildTasksByStatus = (row, status) => {
      return row.children?.filter(task => task.status === status)?.length || 0;
    };

    const getOtherStatusCount = (row) => {
      const total = getChildTasksTotal(row);
      const counted = getChildTasksByStatus(row, 'Success') +
        getChildTasksByStatus(row, 'Running') +
        getChildTasksByStatus(row, 'Failed') +
        getChildTasksByStatus(row, 'Pending');
      return total - counted;
    };

    const getProgressWidth = (row, status) => {
      if (!row.childTaskStats?.total) return '0%';
      const total = row.childTaskStats.total;

      let count;
      switch (status) {
        case 'Success':
          count = row.childTaskStats.success;
          break;
        case 'Running':
          count = row.childTaskStats.running;
          break;
        case 'Failed':
          count = row.childTaskStats.failed;
          break;
        case 'Pending':
          count = row.childTaskStats.pending;
          break;
        case 'Cancelled':
          count = row.childTaskStats.cancelled;
          break;
        default:
          count = 0;
      }

      return `${(count / total * 100).toFixed(1)}%`;
    };

    const editDialogVisible = ref(false);
    const editJobTask = ref({
      id: null,
      priority: null,
      resourceSelection: '',
      status: '',
      notes: ''
    });

    const showEditDialog = (row) => {
      editJobTask.value = {
        id: row.jobTaskId,
        priority: null,
        resourceSelection: row.resourceSelection || '',
        status: '',
        notes: row.notes || ''
      };
      editDialogVisible.value = true;
    };

    const handleEditSubmit = async () => {
      try {
        isSaving.value = true;
        const updateData = {};
        
        // 只包含已修改的字段
        if (editJobTask.value.priority !== null) {
          updateData.priority = editJobTask.value.priority;
        }
        if (editJobTask.value.resourceSelection) {
          updateData.resourceSelection = editJobTask.value.resourceSelection;
        }
        if (editJobTask.value.status !== '') {
          updateData.status = editJobTask.value.status;
        }
        if (editJobTask.value.notes) {
          updateData.notes = editJobTask.value.notes;
        }

        await axios.put(`/api/JobTask/update/${editJobTask.value.id}`, updateData);
        
        ElMessage.success('更新成功');
        editDialogVisible.value = false;
        await fetchJobTasks();
      } catch (error) {
        ElMessage.error(`更新失败: ${error.message}`);
      } finally {
        isSaving.value = false;
      }
    };

    const calculateDuration = (startTime, endTime) => {
      if (!startTime || !endTime) return '';

      const start = new Date(startTime);
      const end = new Date(endTime);
      const diffInSeconds = Math.floor((end - start) / 1000);
      
      if (diffInSeconds < 0) return '';
      
      const days = Math.floor(diffInSeconds / (24 * 3600));
      const hours = Math.floor((diffInSeconds % (24 * 3600)) / 3600);
      const minutes = Math.floor((diffInSeconds % 3600) / 60);
      const seconds = diffInSeconds % 60;
      
      if (days === 0 && hours === 0 && minutes === 0) {
        return `${seconds}秒`;
      }
      
      if (days === 0 && hours === 0) {
        return minutes === 0 ? `${seconds}秒` : `${minutes}分${seconds}秒`;
      }
      
      if (days === 0) {
        return `${hours}时${minutes}分${seconds}秒`;
      }
      
      return `${days}天${hours}时${minutes}分${seconds}秒`;
    };

    // 添加任务类型样式方法
    const getTaskTypeStyle = (taskType) => {
      const typeMap = {
        0: 'info',    // 普通任务
        1: 'warning', // 编排任务
        2: 'success'  // 系统编排拆分任务
      };
      return typeMap[taskType] || 'info';
    };

    const subTaskDialog = ref({
      visible: false,
      parentTaskId: null,
      parentTaskName: ''
    })

    const showSubTasks = (row) => {
      subTaskDialog.value = {
        visible: true,
        parentTaskId: row.jobTaskId,
        parentTaskName: row.jobTaskName
      }
    }

    // 远程桌面状态
    const remoteDesktop = ref({
      visible: false,
      machineName: ''
    })

    // 处理远程桌面
    const handleRemoteDesktop = (machineName) => {
      remoteDesktop.value = {
        visible: true,
        machineName
      }
    }

    onMounted(async () => {
      await fetchJobTasks();
      await fetchExePrograms();
      await initSignalRConnection();
    });

    onUnmounted(() => {
      if (connection) {
        connection.stop();
      }
    });

    return {
      treeData,
      exePrograms,
      dialogVisible,
      newJobTask,
      showAddDialog,
      createJobTask,
      handleDelete,
      retryJobTask,
      stopJobTask,
      selectedProgramInputParameters,
      handleProgramChange,
      updateInputParameters,
      splitTaskConfig,
      handleFileTypeError,
      isRPAProgram,
      tableRowClassName,
      getStatusType,
      isSaving,
      fullscreenLoading,
      hasInputFile,
      handleInputFileDownload,
      hasReturnResult,
      copyReturnResult,
      searchForm,
      pagination,
      handleSearch,
      resetSearch,
      handlePageChange,
      handleSizeChange,
      showResourceSelection,
      RefreshRight,
      VideoPause,
      Delete,
      getChildTasksTotal,
      getChildTasksByStatus,
      getOtherStatusCount,
      getProgressWidth,
      editDialogVisible,
      editJobTask,
      showEditDialog,
      handleEditSubmit,
      calculateDuration,
      getTaskTypeStyle,
      subTaskDialog,
      showSubTasks,
      remoteDesktop,
      handleRemoteDesktop
    };
  }
};
</script>

<style scoped>
.text-danger {
  color: #f56c6c;
}

/* 子任务式 */
.child-task {
  padding-right: 8px;
}

/* 统一行高 */
:deep(.el-table__row) {
  height: 50px;
}

/* 子任务样式 */
:deep(.child-task-row) {
  background-color: #f5f7fa;
}

/* 状态标签样式 */
:deep(.el-tag) {
  min-width: 80px;
  text-align: center;
}

/* 表格内容垂直居中 */
:deep(.el-table .cell) {
  display: flex;
  align-items: center;
}

/* 禁用表格行hover效果 */
:deep(.el-table__row):hover > td.el-table__cell {
  background-color: inherit !important;
}

:deep(.el-table__row--striped):hover > td.el-table__cell {
  background-color: var(--el-table-row-hover-bg-color) !important;
}

/* 添加加载状态的按钮样式 */
:deep(.el-button.is-loading) {
  pointer-events: none;
}

/* 全屏加载样式 */
:deep(.el-loading-mask) {
  z-index: 9999;
}

/* 可以添加图标按钮的样式 */
.el-button [class*='el-icon'] + span {
  margin-left: 6px;
}

/* 添加可点击文本的样式 */
.clickable-text {
  color: #409EFF;
  cursor: pointer;
}

.clickable-text:hover {
  text-decoration: underline;
}

/* 添加新的样式 */
.search-form {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 添加分页器的中文样式覆盖 */
:deep(.el-pagination) {
  --el-pagination-button-disabled-bg-color: var(--el-disabled-bg-color);
  --el-pagination-button-disabled-color: var(--el-disabled-text-color);
  --el-pagination-button-bg-color: var(--el-bg-color);
  --el-pagination-button-color: var(--el-text-color-primary);
  --el-pagination-button-size: 32px;
  --el-pagination-button-width: 32px;

  .el-pagination__total {
    &::before {
      content: "共 ";
    }
    &::after {
      content: " 条";
    }
  }

  .el-pagination__sizes {
    .el-select .el-input {
      &::before {
        content: "每页 ";
      }
      &::after {
        content: " 条";
      }
    }
  }

  .btn-prev {
    .el-icon {
      &::before {
        content: "上一页";
      }
    }
  }

  .btn-next {
    .el-icon {
      &::before {
        content: "下一页";
      }
    }
  }

  .el-pager li:not(.is-active):hover {
    color: var(--el-color-primary);
  }

  .el-pagination__jump {
    .el-pagination__goto {
      &::before {
        content: "前往";
      }
      &::after {
        content: "页";
      }
    }
  }
}

/* 添加工具提示的自定义样式 */
:deep(.el-tooltip__popper) {
  max-width: 300px;
  word-break: break-all;
}

/* 添加按钮间距样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 优化图标按钮大小 */
:deep(.el-button.is-circle) {
  padding: 6px;
}

/* 确保图标垂直居中 */
:deep(.el-button .el-icon) {
  vertical-align: middle;
}

/* 添加鼠标悬停效果 */
.el-button:hover {
  transform: scale(1.1);
  transition: transform 0.2s;
}

.progress-container {
  width: 100%;
  padding: 2px 0;
}

.progress-bar {
  height: 20px;
  width: 100%;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
}

.progress-segment {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-segment.success {
  background-color: #67c23a;
}

.progress-segment.running {
  background-color: #409eff;
}

.progress-segment.failed {
  background-color: #f56c6c;
}

.progress-segment.pending {
  background-color: #909399;
}

.progress-segment.cancelled {
  background-color: #e6a23c;
}

/* 确保tooltip内容左对齐且换行 */
:deep(.el-tooltip__popper) {
  text-align: left;
}

/* 系统编排拆分任务样式 */
:deep(.split-task-row) {
  background-color: #fff7e6 !important;  /* 浅橙色背景 */
}

/* 确保子任务在系统编排拆分任务下的样式 */
:deep(.split-task-row.child-task-row) {
  background-color: #f0f9ff !important;  /* 更浅的背景色 */
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.left-section {
  display: flex;
  align-items: center;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-form {
  margin: 0;
}

:deep(.el-form--inline .el-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

:deep(.el-form--inline .el-form-item:last-child) {
  margin-right: 0;
}

</style>

