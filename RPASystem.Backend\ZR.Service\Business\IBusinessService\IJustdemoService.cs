using ZR.Model.Business.Dto;
using ZR.Model.Business;

namespace ZR.Service.Business.IBusinessService
{
    /// <summary>
    /// 功能名只是演示service接口
    /// </summary>
    public interface IJustdemoService : IBaseService<Justdemo>
    {
        PagedInfo<JustdemoDto> GetList(JustdemoQueryDto parm);

        Justdemo GetInfo(long Id);


        Justdemo AddJustdemo(Justdemo parm);
        int UpdateJustdemo(Justdemo parm);


        PagedInfo<JustdemoDto> ExportList(JustdemoQueryDto parm);
    }
}
