using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using NLog;

namespace RPASystem.ClientWin.Services.Remote
{
    public class KeyboardController
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // Windows API 常量
        private const int KEYEVENTF_KEYDOWN = 0x0000;
        private const int KEYEVENTF_KEYUP = 0x0002;
        private const int KEYEVENTF_EXTENDEDKEY = 0x0001;

        // 特殊键映射字典
        private static readonly Dictionary<string, ushort> SpecialKeys = new Dictionary<string, ushort>
        {
            {"Enter", 0x0D},
            {"Tab", 0x09},
            {"Escape", 0x1B},
            {"Backspace", 0x08},
            {"Delete", 0x2E},
            {"ArrowUp", 0x26},
            {"ArrowDown", 0x28},
            {"ArrowLeft", 0x25},
            {"ArrowRight", 0x27},
            {"Home", 0x24},
            {"End", 0x23},
            {"PageUp", 0x21},
            {"PageDown", 0x22},
            {"Insert", 0x2D},
            {"F1", 0x70},
            {"F2", 0x71},
            {"F3", 0x72},
            {"F4", 0x73},
            {"F5", 0x74},
            {"F6", 0x75},
            {"F7", 0x76},
            {"F8", 0x77},
            {"F9", 0x78},
            {"F10", 0x79},
            {"F11", 0x7A},
            {"F12", 0x7B},
            {"Control", 0x11},
            {"Alt", 0x12},
            {"Shift", 0x10},
            {"CapsLock", 0x14},
            {"NumLock", 0x90},
            {"ScrollLock", 0x91},
            {"PrintScreen", 0x2C},
            {"Pause", 0x13},
            {"Meta", 0x5B}, // Windows 键
        };

        // Windows API
        [DllImport("user32.dll")]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, IntPtr dwExtraInfo);

        [DllImport("user32.dll")]
        private static extern byte VkKeyScan(char ch);

        /// <summary>
        /// 执行键盘命令
        /// </summary>
        /// <param name="eventType">事件类型(keydown/keyup)</param>
        /// <param name="key">按键值</param>
        /// <param name="keyCombination">组合键数组</param>
        /// <param name="keyCode">键码</param>
        /// <returns>执行结果</returns>
        public async Task<(bool success, string message)> ExecuteKeyboardCommandAsync(string eventType, string key, string[] keyCombination, int keyCode)
        {
            try
            {
                logger.Info($"执行键盘命令: {eventType} 键: {key} 键码: {keyCode}");

                // 单键处理
                if (keyCombination.Length <= 1)
                {
                    if (eventType == "keydown")
                    {
                        await PressKeyAsync(key, keyCode);
                    }
                    else if (eventType == "keyup")
                    {
                        await ReleaseKeyAsync(key, keyCode);
                    }
                }
                // 组合键处理（如Ctrl+C, Alt+F4等）
                else
                {
                    if (eventType == "keydown")
                    {
                        await PressKeyCombinationAsync(keyCombination);
                    }
                }

                return (true, $"键盘命令 {eventType} {key} 执行成功");
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"执行键盘命令失败: {ex.Message}");
                return (false, $"执行键盘命令失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 按下单个键
        /// </summary>
        private async Task PressKeyAsync(string key, int keyCode)
        {
            byte vkCode = GetVirtualKeyCode(key, keyCode);
            keybd_event(vkCode, 0, KEYEVENTF_KEYDOWN, IntPtr.Zero);
            logger.Debug($"按下键: {key}, VK: {vkCode}");
            
            // 非修饰键自动释放
            if (!IsModifierKey(key))
            {
                await Task.Delay(50); // 短暂延迟
                keybd_event(vkCode, 0, KEYEVENTF_KEYUP, IntPtr.Zero);
                logger.Debug($"释放键: {key}, VK: {vkCode}");
            }
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// 释放单个键
        /// </summary>
        private async Task ReleaseKeyAsync(string key, int keyCode)
        {
            byte vkCode = GetVirtualKeyCode(key, keyCode);
            keybd_event(vkCode, 0, KEYEVENTF_KEYUP, IntPtr.Zero);
            logger.Debug($"释放键: {key}, VK: {vkCode}");
            await Task.CompletedTask;
        }

        /// <summary>
        /// 按下组合键
        /// </summary>
        private async Task PressKeyCombinationAsync(string[] keys)
        {
            try
            {
                // 按顺序按下所有键
                List<byte> pressedKeys = new List<byte>();
                
                foreach (string key in keys)
                {
                    byte vkCode = GetVirtualKeyCode(key, 0);
                    keybd_event(vkCode, 0, KEYEVENTF_KEYDOWN, IntPtr.Zero);
                    pressedKeys.Add(vkCode);
                    logger.Debug($"按下组合键: {key}, VK: {vkCode}");
                    await Task.Delay(50); // 短暂延迟确保按键次序
                }

                await Task.Delay(100); // 等待命令执行

                // 按相反顺序释放所有键
                for (int i = pressedKeys.Count - 1; i >= 0; i--)
                {
                    keybd_event(pressedKeys[i], 0, KEYEVENTF_KEYUP, IntPtr.Zero);
                    logger.Debug($"释放组合键: VK: {pressedKeys[i]}");
                    await Task.Delay(50);
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"执行组合键失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取虚拟键码
        /// </summary>
        private byte GetVirtualKeyCode(string key, int keyCode)
        {
            // 处理特殊键
            if (SpecialKeys.TryGetValue(key, out ushort specialKeyCode))
            {
                return (byte)specialKeyCode;
            }
            
            // 处理单个字符
            if (key.Length == 1)
            {
                return VkKeyScan(key[0]);
            }
            
            // 使用传入的keyCode
            return (byte)keyCode;
        }

        /// <summary>
        /// 判断是否为修饰键(Ctrl, Alt, Shift等)
        /// </summary>
        private bool IsModifierKey(string key)
        {
            return key == "Control" || key == "Alt" || key == "Shift" || key == "Meta";
        }
    }
} 