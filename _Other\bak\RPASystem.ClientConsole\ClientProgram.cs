using System;
using System.Threading.Tasks;
using Microsoft.Win32;
using NLog;
using System.Configuration;

namespace RPASystem.ClientConsole
{
    class ClientProgram
    {
        public static string BaseUrl;
        static async Task Main(string[] args)
        {
            PreventLockDisplayUtils.PreventSleep(true);
            //LogManager.LoadConfiguration("nlog.config");
            LogManager.Setup().LoadConfigurationFromFile("nlog.config");
            string machineName = GetHostname() ?? Environment.MachineName;
            Console.WriteLine("本机资源名：" + machineName);
            BaseUrl = ConfigurationManager.AppSettings["BaseUrl"];
            Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 正在连接服务器：" + BaseUrl);
            var connectionManager = new ConnectionManager();
            await connectionManager.StartConnectionAsync(BaseUrl, machineName);
            ResourceReporter.Instance.ReportResourceInfo(connectionManager.GetConnection(), machineName);
            
            while (true)
            {
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 输入exit退出，输入1设置资源名");
                var s = Console.ReadLine();
                if (s == "exit")
                {
                    break;
                }
                else if (s == "1")
                {
                    Console.WriteLine("请输入资源名：");
                    if (SetHostname(Console.ReadLine()))
                    {
                        Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 设置成功！重启客户端生效！");
                        machineName = GetHostname();
                    }
                    else
                        Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 设置失败！");
                }
            }
        }

        public static string GetHostname()
        {
            try
            {
                using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
                {
                    using (var key = baseKey.OpenSubKey(@"SOFTWARE\WOW6432Node\HUAWEI\V"))
                    {
                        if (key != null)
                        {
                            var value = key.GetValue("WAssName");
                            if (value != null)
                            {
                                return value.ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 获取主机名时发生错误: {ex.Message}");
            }
            return null;
        }

        public static bool SetHostname(string name)
        {
            try
            {
                // 使用 RegistryKey.OpenBaseKey 方法来指定注册表视图
                using (var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64))
                {
                    using (var key = baseKey.CreateSubKey(@"SOFTWARE\WOW6432Node\HUAWEI\V", true))
                    {
                        if (key != null)
                        {
                            key.SetValue("WAssName", name, RegistryValueKind.String);
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} 设置主机名时发生错误: {ex.Message}");
            }
            return false;
        }
    }
}
