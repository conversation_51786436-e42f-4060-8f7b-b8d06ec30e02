﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>1701;1702;1591;1570</NoWarn>
		<GeneratePackageOnBuild>True</GeneratePackageOnBuild>
		<PackageOutputPath>..\Lib</PackageOutputPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="NETCore.Encrypt" Version="2.1.1" />
		<PackageReference Include="NLog" Version="5.3.4" />
		<PackageReference Include="ZR.Common" Version="1.0.0" />
		<PackageReference Include="ZR.Repository" Version="1.0.0" />
	</ItemGroup>

</Project>
