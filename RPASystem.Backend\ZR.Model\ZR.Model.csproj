﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>1701;1702;1591;1570</NoWarn>
		<Nullable>disable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<GeneratePackageOnBuild>True</GeneratePackageOnBuild>
		<PackageOutputPath>..\Lib</PackageOutputPath>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="RPA\**" />
	  <EmbeddedResource Remove="RPA\**" />
	  <None Remove="RPA\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="MiniExcel" Version="1.34.2" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="SqlSugarCoreNoDrive" Version="5.1.4.169" />
		<PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
	</ItemGroup>
</Project>
