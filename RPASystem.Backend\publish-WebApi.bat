@echo off
echo 开始发布项目...

REM 设置项目路径
set PROJECT_PATH=ZR.Admin.WebApi\ZR.Admin.WebApi.csproj
set PUBLISH_PATH=Z:\VV35\RPASystem.BackendV3-Publish


:: REM 清理发布目录
:: if exist "%PUBLISH_PATH%" (
::     echo 清理发布目录...
::     rmdir /s /q "%PUBLISH_PATH%"
:: )

REM 发布项目
echo 正在发布项目...
dotnet publish "%PROJECT_PATH%" -c Release -f net8.0 -r win-x64 --self-contained false -o "%PUBLISH_PATH%"

if %ERRORLEVEL% EQU 0 (
    echo 发布成功！
    echo 发布目录: %PUBLISH_PATH%
) else (
    echo 发布失败！
    pause
    exit /b 1
)

echo 按任意键退出...
pause 