using System;
using System.Text.Json.Serialization;
using System.Threading;
using RPASystem.Model;


public class JobInfoModel : JobModel
{
    /// <summary>
    /// 任务开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 任务结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 任务状态
    /// </summary>
    public JobTaskStatusEnum Status { get; set; }

    /// <summary>
    /// 任务的取消令牌源
    /// </summary>
    [JsonIgnore]
    public CancellationTokenSource CancellationTokenSource { get; set; }
}
