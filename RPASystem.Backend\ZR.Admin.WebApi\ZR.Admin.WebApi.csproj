﻿<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<UserSecretsId>ab105cb4-b03c-419a-bd44-303b90114268</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<NoWarn>1701;1702;1591,8603,8602,8604,8600,8618</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\RPA.Model\RPA.Model.csproj" />
		<ProjectReference Include="..\RPA.Service\RPA.Service.csproj" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Lazy.Captcha.Core" Version="2.0.9" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.12" />
		<PackageReference Include="NLog.Web.AspNetCore" Version="5.3.13" />
		<PackageReference Include="Mapster" Version="7.4.0" />
		<PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.88.9" />
		<PackageReference Include="ZR.CodeGenerator" Version="1.0.0" />
		<PackageReference Include="ZR.Service" Version="1.0.0" />
		<PackageReference Include="ZR.Tasks" Version="1.0.0" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Properties\PublishProfiles\" />
	</ItemGroup>

	<ItemGroup>
		<None Update="ip2region.xdb">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<Content Update="wwwroot\Generatecode\**\*">
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</Content>
		<Content Update="wwwroot\export\**\*">
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</Content>
	</ItemGroup>
</Project>
