﻿$if(genTable.TplCategory.Contains("subNav") && genTable.SubTable != null)
using ${subTableOptions.ModelsNamespace}.${subTableOptions.SubNamespace};
$end

namespace ${options.ModelsNamespace}.${options.SubNamespace}
{
    /// <summary>
    /// ${genTable.FunctionName}
    /// </summary>
    [SugarTable("${genTable.TableName}")]
    public class ${replaceDto.ModelTypeName}
    {
${foreach(item in genTable.Columns)}
        /// <summary>
        /// ${item.ColumnComment} ${item.Remark}
        /// </summary>
$if(item.IsPk || item.IsIncrement)
        [SugarColumn(IsPrimaryKey = ${item.IsPk.ToString().ToLower()}, IsIdentity = ${item.IsIncrement.ToString().ToLower()}$if(item.CsharpField.ToLower() != item.ColumnName.ToLower()), ColumnName = "$item.ColumnName"$end)]
$elseif(item.CsharpField.ToLower() != item.ColumnName.ToLower())
        [SugarColumn(ColumnName = "$item.ColumnName")]
$elseif(item.AutoFillType == 1 && item.CsharpType == "DateTime")
        [SugarColumn(InsertServerTime = true, IsOnlyIgnoreUpdate = true)]
$elseif(item.AutoFillType == 2 && item.CsharpType == "DateTime")
        [SugarColumn(UpdateServerTime = true, IsOnlyIgnoreInsert = true)]
$elseif(item.AutoFillType == 3 && item.CsharpType == "DateTime")
        [SugarColumn(InsertServerTime = true, UpdateServerTime = true)]
$elseif(item.AutoFillType == 1)
        [SugarColumn(IsOnlyIgnoreUpdate = true)]
$end
        public $item.CsharpType$item.RequiredStr $item.CsharpField { get; set; }

${end}
$if(genTable.TplCategory == "tree")
        [SugarColumn(IsIgnore = true)]
        public List<${replaceDto.ModelTypeName}> Children { get; set; }
$end
$if((genTable.TplCategory == "subNav" || genTable.TplCategory == "sub") && genTable.SubTable != null)
        [Navigate(NavigateType.OneToOne, nameof(${replaceDto.PKName}))] //自定义关系映射
        public ${genTable.SubTable.ClassName} ${genTable.SubTable.ClassName}Nav { get; set; }
$end
$if(genTable.TplCategory == "subNavMore" && genTable.SubTable != null)
        [Navigate(NavigateType.OneToMany, nameof(${genTable.SubTable.ClassName}.${genTable.SubTableFkName}), nameof(${replaceDto.PKName}))] //自定义关系映射
        public List<${genTable.SubTable.ClassName}> ${genTable.SubTable.ClassName}Nav { get; set; }
$end
    }
}