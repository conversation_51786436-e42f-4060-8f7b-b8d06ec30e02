<!--
 * @Descripttion: (${genTable.functionName}/${genTable.remark})
 * @Author: (${replaceDto.Author})
 * @Date: (${replaceDto.AddTime})
-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" size="small" label-position="right" inline ref="queryForm" :label-width="labelWidth" v-show="showSearch" 
      @submit.native.prevent>
      ${replaceDto.vueQueryFormHtml}
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" v-hasPermi="['${replaceDto.PermissionPrefix}:add']" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
       <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" :disabled="multiple" v-hasPermi="['${replaceDto.PermissionPrefix}:delete']" plain icon="el-icon-delete" size="mini" @click="handleDelete">删除</el-button>
      </el-col>
$if(replaceDto.ShowBtnExport)
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['${replaceDto.PermissionPrefix}:export']">导出</el-button>
      </el-col>
$end
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据区域 -->
    <el-table v-if="refreshTable" :data="dataList" v-loading="loading" ref="table" border highlight-current-row @selection-change="handleSelectionChange"
      :default-expand-all="isExpandAll" row-key="${tool.FirstLowerCase(genTable.Options.TreeCode)}" :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <el-table-column type="selection" width="50" align="center"/>
${replaceDto.VueViewListHtml}
      <el-table-column label="操作" align="center" width="140">
        <template slot-scope="scope">
          <el-button size="mini" v-hasPermi="['${replaceDto.PermissionPrefix}:edit']" type="success" icon="el-icon-edit" title="编辑" 
            @click="handleUpdate(scope.row)"></el-button>
          <el-button size="mini" v-hasPermi="['${replaceDto.PermissionPrefix}:delete']" type="danger" icon="el-icon-delete" title="删除" 
            @click="handleDelete(scope.row)"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination class="mt10" background :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改${genTable.functionName}对话框 -->
    <el-dialog :title="title" :lock-scroll="false" :visible.sync="open" >
      <el-form ref="form" :model="form" :rules="rules" :label-width="formLabelWidth">
        <el-row :gutter="20">
        ${replaceDto.VueViewFormHtml}
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { 
  treelist${genTable.BusinessName},
  list${genTable.BusinessName},
  add${genTable.BusinessName},
  del${genTable.BusinessName},
  update${genTable.BusinessName},
  get${genTable.BusinessName},
$if(replaceDto.ShowBtnExport)
  export${genTable.BusinessName},
$end
$if(showCustomInput)
  changeSort
$end
} from '@/api/${tool.FirstLowerCase(genTable.ModuleName)}/${tool.FirstLowerCase(genTable.BusinessName)}.js';
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "${genTable.BusinessName.ToLower()}",
  components: { Treeselect },
  data() {
    return {
      labelWidth: "100px",
      formLabelWidth:"100px",
      // 选中${replaceDto.FistLowerPk}数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
$foreach(item in genTable.Columns)
$if(item.IsQuery == true)
        ${item.CsharpFieldFl}: undefined,
$end
$end
      },
      // 弹出层标题
      title: "",
      // 操作类型 1、add 2、edit
      opertype: 0,
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 表单参数
      form: {},
      columns: [
$set(index = 0)
$foreach(column in genTable.Columns)
        { index: $index, key: '${column.CsharpFieldFl}', label: `${column.ColumnComment}`, checked: $if(index < 9) true $else false $end },
$set(index = index + 1)
$end
      ],
$foreach(item in genTable.Columns)
$if((item.HtmlType == "radio" || item.HtmlType == "select" || item.HtmlType == "checkbox"))
      // ${item.ColumnComment}选项列表
      $if(item.DictType != "")${item.DictType}$else${item.CsharpFieldFl}Options$end: [],
$elseif(item.HtmlType == "datetime" && item.IsQuery == true)
      //${item.ColumnComment}时间范围
      dateRange${item.CsharpField}: [],
$elseif(item.HtmlType == "customInput")
      editIndex: -1,
$end
$end
      dataList: [],
      total: 0,
      rules: {
$foreach(column in genTable.Columns)
$if(column.IsRequired && column.IsIncrement == false)
        ${column.CsharpFieldFl}: [
          { required: true, message: "${column.ColumnComment}不能为空", trigger: $if(column.htmlType == "select")"change"$else"blur"$end
$if(column.CsharpType == "int" || column.CsharpType == "long"), type: "number"$end }
        ],
$end
$end
      },
    };
  },
  created() {
    // 列表数据查询
    this.getList();

    $set(index = 0)
    var dictParams = [
$foreach(item in dicts)
$if(item.DictType != "")
      "${item.DictType}",
$set(index = index + 1)
$end
$end
    ];
$if(index > 0)
    this.getDicts(dictParams).then((response) => {
      response.data.forEach((element) => {
        this[element.dictType] = element.list;
      });
    });
$end
  },
  methods: {
    // 查询数据
    getList() {
$foreach(item in genTable.Columns)
$if(item.HtmlType == "datetime" && item.IsQuery == true)
      this.addDateRange(this.queryParams, this.dateRange${item.CsharpField}, '${item.CsharpField}');
$end
$end
      this.loading = true;
      treelist${genTable.BusinessName}(this.queryParams).then(res => {
         if (res.code == 200) {
           this.dataList = res.data;
           this.loading = false;
         }
       })
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.${tool.FirstLowerCase(genTable.Options.TreeCode)},
        label: node.${tool.FirstLowerCase(genTable.Options.TreeName)},
        children: node.children,
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 重置数据表单
    reset() {
      this.form = {
$foreach(item in genTable.Columns)
$if((item.HtmlType == "checkbox"))
        ${item.CsharpFieldFl}Checked: [],
$else
        $item.CsharpFieldFl: undefined,
$end
$end
      };
      this.resetForm("form");
    },
    /** 重置查询操作 */
    resetQuery() {
      this.timeRange = [];
$foreach(item in genTable.Columns)
$if(item.HtmlType == "datetime" && item.IsQuery == true)
      //${item.ColumnComment}时间范围
      this.dateRange${item.CsharpField}= [];
$end
$end
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.${replaceDto.FistLowerPk});
      this.single = selection.length != 1
      this.multiple = !selection.length;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
      this.opertype = 1;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const Ids = row.${replaceDto.FistLowerPk} || this.ids;

      this.${confirm}confirm('是否确认删除参数编号为"' + Ids + '"的数据项？')
        .then(function () {
          return del${genTable.BusinessName}(Ids);
        })
        .then(() => {
          this.handleQuery();
          this.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.${replaceDto.FistLowerPk} || this.ids;
      get${genTable.BusinessName}(id).then((res) => {
        const { code, data } = res;
        if (code == 200) {
          this.open = true;
          this.title = "修改数据";
          this.opertype = 2;

          this.form = {
            ...data,
$foreach(item in genTable.Columns)
$if(item.HtmlType == "checkbox")
            ${item.CsharpFieldFl}Checked: data.${item.CsharpFieldFl} ? data.${item.CsharpFieldFl}.split(',') : [],
$end
$end
          };
        }
      });
    },
$if(replaceDto.UploadFile == 1)
    //图片上传成功方法
    handleUploadSuccess(column, filelist) {
      this.form[column] = filelist;
    },
$end
$foreach(item in genTable.Columns)
$if(item.HtmlType == "customInput")
    // 显示编辑排序
    editCurrRow(rowId, str) {
      this.editIndex = rowId;
      let id = rowId + str;
      
      setTimeout(() => {
        document.getElementById(id).focus();
      }, 100);
    },
    // 保存排序
    handleChangeSort(info) {
      this.editIndex = -1;
      changeSort({ value: info.${item.CsharpFieldFl}, id: info.${replaceDto.FistLowerPk} }).then(
        (response) => {
          this.msgSuccess("修改成功");
          this.getList();
        }
      );
    },
$end
$end
    /** 提交按钮 */
    submitForm: function () {
      this.${refs}refs["form"].validate((valid) => {
        if (valid) {
$foreach(item in genTable.Columns)
$if(item.HtmlType == "checkbox")
          this.form.${item.CsharpFieldFl} = this.form.${item.CsharpFieldFl}Checked.toString();
$end
$end
          if (this.form.${replaceDto.FistLowerPk} != undefined && this.opertype === 2) {
            update${genTable.BusinessName}(this.form)
              .then((res) => {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
            })
          } else {
            add${genTable.BusinessName}(this.form)
              .then((res) => {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
            })
          }
        }
      });
    },
$if(replaceDto.ShowBtnExport)
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.${confirm}confirm("是否确认导出所有${genTable.functionName}数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
          await export${genTable.BusinessName}(queryParams);
      })
    },
$end
    //展开/折叠操作
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.${nextTick}nextTick(() => {
        this.refreshTable = true;
      });
    },
  },
};
</script>