using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;

namespace RPASystem.Client
{
    public class TaskHistoryManager
    {
        private const string HistoryFileName = "taskHistory.json";
        private const int MaxHistoryCount = 100;
        private static readonly string HistoryFilePath = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory, 
            "History", 
            HistoryFileName
        );

        public static void SaveTaskHistory(JobInfoModel task)
        {
            var history = LoadHistory();
            history.Insert(0, task);
            
            if (history.Count > MaxHistoryCount)
            {
                history = history.Take(MaxHistoryCount).ToList();
            }

            Directory.CreateDirectory(Path.GetDirectoryName(HistoryFilePath));
            File.WriteAllText(HistoryFilePath, JsonConvert.SerializeObject(history));
        }

        public static List<JobInfoModel> LoadHistory()
        {
            if (!File.Exists(HistoryFilePath))
            {
                return new List<JobInfoModel>();
            }

            try
            {
                var json = File.ReadAllText(HistoryFilePath);
                return JsonConvert.DeserializeObject<List<JobInfoModel>>(json) ?? new List<JobInfoModel>();
            }
            catch
            {
                return new List<JobInfoModel>();
            }
        }
    }
} 