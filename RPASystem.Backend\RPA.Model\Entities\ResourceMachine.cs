using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;

namespace RPASystem.Model
{


    public class ResourceMachine
    {
        public long Id { get; set; }
        public string MachineName { get; set; }
        public string? ComputerName { get; set; }
        public DateTime LastActivityTime { get; set; }
        public string? RunningExeNames { get; set; }
        [Column(TypeName = "ENUM('空闲', '任务运行中', '离线')")]
        public TaskStatusEnum TaskStatus { get; set; } // 空闲, 任务运行中, 离线
        public float? CpuUsage { get; set; }
        public float? MemoryUsage { get; set; }
        public string? DiskUsage { get; set; }
        [Column(TypeName = "ENUM('在线执行机', '服务机', '普通机')")]
        public MachineTypeEnum MachineType { get; set; } = MachineTypeEnum.在线执行机;
        public string? IpAddress { get; set; }
        public string? ClientVersion { get; set; }
        public bool IsLatestVersion { get; set; }
    }

    public enum MachineTypeEnum
    {
        在线执行机,
        服务机,
        普通机
    }
}
