import { ElMessage } from 'element-plus'

/**
 * 通用复制文本到剪贴板方法
 * @param {string} text 要复制的文本
 * @returns {boolean} 是否复制成功
 */
export const copyToClip = (text) => {
  const aux = document.createElement("input")
  try {
    aux.setAttribute("value", text)
    document.body.appendChild(aux)
    aux.select()
    document.execCommand("copy")
    ElMessage.success('已复制到剪贴板')
    return true
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请重试')
    return false
  } finally {
    document.body.removeChild(aux)
  }
} 