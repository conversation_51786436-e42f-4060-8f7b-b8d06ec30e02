﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


public class ISRPALog
{
    private const string RpaLogPrefix = "RPARuntimeInfo";

    private const int RPALogFileMaxByteSize = 50000000;

    private static readonly Lazy<ISRPALog> Instance = new Lazy<ISRPALog>(() => new ISRPALog());

    private ISRPALog()
    {
    }

    public static ISRPALog GetInstance => Instance.Value;



    public async Task<bool> IsRpaCorrectRunCompletedAsync(string rpaName)
    {
        var logDirePath = RPACaseUtils.GetPath(rpaName, RPAFolder.Log);

        var dirInfo = new DirectoryInfo(logDirePath);
        if (!dirInfo.Exists)
        {
            dirInfo.Create();
        }

        var logFiles = dirInfo.GetFiles($"{RpaLogPrefix}*.log");

        // 是否正常运行完成
        bool isCorrectRunCompleted = false;

        var byteLength = 1024 * 10;

        var logtxt = new StringBuilder();

        foreach (var logFile in logFiles)
        {
            logtxt.Clear();

            using (var fs = new FileStream(logFile.FullName, FileMode.Open, FileAccess.Read, FileShare.None, 4096, FileOptions.Asynchronous))
            {
                var length = (int)(fs.Length < byteLength ? fs.Length : byteLength);
                var buffer = new byte[length];
                fs.Seek(-buffer.Length, SeekOrigin.End);
                await fs.ReadAsync(buffer, 0, buffer.Length);
                logtxt.Append(Encoding.UTF8.GetString(buffer));
                fs.Close();
                fs.Dispose();
            }

            isCorrectRunCompleted = logtxt.ToString().IndexOf("RPA程序执行结束") > -1;

            if (isCorrectRunCompleted)
            {
                break;
            }
        }

        // 正常运行结束，不做任何处理
        if (isCorrectRunCompleted)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 生成RPA非正常运行结束.txt
    /// </summary>
    /// <param name="context">上下文</param>
    public void RpaCorrectRunCompletedRecord(string rpaName)
    {
        //LogUtils.Error("RPA非正常运行结束");
        var logDirePath = RPACaseUtils.GetPath(rpaName, RPAFolder.Log);
        // 非正常运行结束，向RPA的Log文件夹写入错误
        var errorFileName = Path.Combine(logDirePath, "RPA非正常运行结束.txt");
        File.WriteAllText(errorFileName, DateTime.Now.ToString());
    }


    /// <summary>
    /// 写日志到RPA目录Log文件夹下
    /// </summary>
    /// <param name="model">启动RPA信息</param>
    /// <param name="logTxt">日志文本</param>
    public void WriteToLogFile(string rpaName, string logTxt)
    {
        var logDirPath = RPACaseUtils.GetPath(rpaName, RPAFolder.Log);

        var direInfo = new DirectoryInfo(logDirPath);
        if (!direInfo.Exists)
        {
            direInfo.Create();
        }

        var lognum = 1;

        string logFileName;

        while (true)
        {
            logFileName = $"RPARuntimeInfo{lognum}.log";

            var logFileInfo = new FileInfo(Path.Combine(logDirPath, logFileName));
            if (!logFileInfo.Exists)
            {
                break;
            }

            if (logFileInfo.Length < RPALogFileMaxByteSize)
            {
                break;
            }
            lognum++;
        }

        var logFilePath = Path.Combine(logDirPath, logFileName);
        File.AppendAllText(logFilePath, logTxt + "\r\n");
    }


    public async Task<string> GetRpaRetStringAsync(string logDir)
    {
        try
        {
            var retString = string.Empty;

            foreach (var logFile in Directory.EnumerateFiles(logDir, $"{RpaLogPrefix}*.log"))
            {
                retString = await ReadRetStringAsync(logFile);
                if (!string.IsNullOrWhiteSpace(retString))
                {
                    break;
                }
            }
            Console.WriteLine("获取RPA返回值：" + retString);
            return retString;
        }
        catch
        {
        }
        return string.Empty;
    }

    private async Task<string> ReadRetStringAsync(string logFilePath)
    {
        var tag = "Ret==>";
        string upLine = string.Empty;

        // 打开文件
        using (StreamReader reader = new StreamReader(logFilePath))
        {
            // 逐行读取并输出
            string line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                var index = line.IndexOf(tag);
                var hasText = upLine.IndexOf("[检测编排上传UploadLog]") > -1;
                if (index > -1 && hasText)
                {
                    return line.Substring(index + tag.Length);
                }
                upLine = line;
            }
        }

        return string.Empty;
    }
}

