using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using OfficeOpenXml;

public class ExcelSplitter
{
    private static readonly Lazy<ExcelSplitter> instance = new Lazy<ExcelSplitter>(() => new ExcelSplitter());
    private static readonly SemaphoreSlim splitLock = new SemaphoreSlim(1, 1);
    private const int MaxParallelTasks = 3; // 限制并行处理的任务数

    public static ExcelSplitter Instance => instance.Value;

    private ExcelSplitter()
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    /// <summary>
    /// 将 Excel 的第一个工作表的数据拆分成多个 Excel，按每个 Excel 的行数拆分。
    /// 一个 Excel 有多个工作表，只拆分第一个工作表，拆分后的其他工作表完整保留。
    /// </summary>
    /// <param name="inputExcel">Excel 的流</param>
    /// <param name="excelPerSplitNum">每个拆分的 Excel 包含的行数</param>
    /// <param name="action">在每个拆分的 Excel 处理完后执行的 Action，参数为序号和流</param>
    /// <returns>是否成功完成拆分</returns>
    public  async Task<bool> SplitExcelByNumAsync(Stream inputExcel, int excelPerSplitNum, Action<int, Stream> action)
    {
        try
        {
            await splitLock.WaitAsync();

            ValidateInput(inputExcel, excelPerSplitNum);

            using var package = new ExcelPackage(inputExcel);
            var workbook = package.Workbook;
            
            if (workbook.Worksheets.Count == 0)
                throw new InvalidOperationException("Excel文件中没有工作表");

            var firstSheet = workbook.Worksheets[0];
            RemoveEmptyRows(firstSheet);

            int totalRows = firstSheet.Dimension?.End.Row ?? 0;
            if (totalRows == 0) return true; // 空文件直接返回

            if (totalRows > 500000)
                throw new InvalidOperationException("Excel文件行数超过50万，无法处理");

            if (totalRows <= excelPerSplitNum)
            {
                // 小文件直接处理
                using var stream = new MemoryStream();
                package.SaveAs(stream);
                stream.Position = 0;
                action?.Invoke(1, stream);
                return true;
            }

            int totalColumns = firstSheet.Dimension.End.Column;
            int splitCount = (int)Math.Ceiling((double)(totalRows - 1) / excelPerSplitNum);
            
            // 使用并行处理，但限制并行度
            var partitioner = Partitioner.Create(0, splitCount);
            var options = new ParallelOptions { MaxDegreeOfParallelism = MaxParallelTasks };

            await Task.Run(() => Parallel.ForEach(partitioner, options, (range, loopState) =>
            {
                for (int i = range.Item1; i < range.Item2; i++)
                {
                    ProcessSplit(i, package, firstSheet, totalColumns, excelPerSplitNum, totalRows, action);
                }
            }));

            return true;
        }
        finally
        {
            splitLock.Release();
        }
    }

    private void ProcessSplit(int splitIndex, ExcelPackage sourcePackage, ExcelWorksheet firstSheet, 
        int totalColumns, int excelPerSplitNum, int totalRows, Action<int, Stream> action)
    {
        using var splitPackage = new ExcelPackage();
        var newSheet = splitPackage.Workbook.Worksheets.Add(firstSheet.Name);

        // 复制列标题
        for (int col = 1; col <= totalColumns; col++)
        {
            newSheet.Cells[1, col].Value = firstSheet.Cells[1, col].Value;
        }

        // 计算本次拆分的行范围
        int startRow = splitIndex * excelPerSplitNum + 2;
        int endRow = Math.Min((splitIndex + 1) * excelPerSplitNum + 1, totalRows);

        // 复制数据行
        for (int row = startRow; row <= endRow; row++)
        {
            for (int col = 1; col <= totalColumns; col++)
            {
                newSheet.Cells[row - startRow + 2, col].Value = firstSheet.Cells[row, col].Value;
            }
        }

        // 保存并处理拆分后的文件
        using var stream = new MemoryStream();
        splitPackage.SaveAs(stream);
        stream.Position = 0;
        action?.Invoke(splitIndex + 1, stream);
    }

    private void ValidateInput(Stream inputExcel, int excelPerSplitNum)
    {
        if (inputExcel == null || !inputExcel.CanRead)
            throw new ArgumentException("输入的Excel流无效");

        if (excelPerSplitNum <= 0)
            throw new ArgumentException("每个拆分的Excel行数必须大于0");
    }

    private void RemoveEmptyRows(ExcelWorksheet worksheet)
    {
        if (worksheet.Dimension == null) return;

        for (int row = worksheet.Dimension.End.Row; row >= worksheet.Dimension.Start.Row; row--)
        {
            bool isEmpty = true;
            for (int col = worksheet.Dimension.Start.Column; col <= worksheet.Dimension.End.Column; col++)
            {
                if (worksheet.Cells[row, col].Value != null && 
                    !string.IsNullOrWhiteSpace(worksheet.Cells[row, col].Text))
                {
                    isEmpty = false;
                    break;
                }
            }

            if (isEmpty)
            {
                worksheet.DeleteRow(row);
            }
        }
    }

    /// <summary>
    /// 同步单线程模式拆分Excel
    /// </summary>
    /// <param name="inputExcel">Excel的流</param>
    /// <param name="excelPerSplitNum">每个拆分的Excel包含的行数</param>
    /// <param name="action">在每个拆分的Excel处理完后执行的Action，参数为序号和流</param>
    /// <returns>是否成功完成拆分</returns>
    public bool SplitExcelByNum(Stream inputExcel, int excelPerSplitNum, Action<int, Stream> action)
    {
        try
        {
            ValidateInput(inputExcel, excelPerSplitNum);

            using var sourcePackage = new ExcelPackage(inputExcel);
            var workbook = sourcePackage.Workbook;
            
            if (workbook.Worksheets.Count == 0)
                throw new InvalidOperationException("Excel文件中没有工作表");

            var firstSheet = workbook.Worksheets[0];
            RemoveEmptyRows(firstSheet);

            int totalRows = firstSheet.Dimension?.End.Row ?? 0;
            if (totalRows == 0) return true; // 空文件直接返回

            if (totalRows > 500000)
                throw new InvalidOperationException("Excel文件行数超过50万，无法处理");

            if (totalRows <= excelPerSplitNum)
            {
                // 小文件直接处理
                using var stream = new MemoryStream();
                sourcePackage.SaveAs(stream);
                stream.Position = 0;
                action?.Invoke(1, stream);
                return true;
            }

            int splitCount = (int)Math.Ceiling((double)(totalRows - 1) / excelPerSplitNum);

            // 逐个处理拆分
            for (int i = 0; i < splitCount; i++)
            {
                // 计算本次拆分的行范围
                int startRow = i * excelPerSplitNum + 2; // 从第二行开始（保留标题行）
                int endRow = Math.Min((i + 1) * excelPerSplitNum + 1, totalRows);

                // 复制整个Excel包
                using var splitPackage = new ExcelPackage(inputExcel);
                var splitSheet = splitPackage.Workbook.Worksheets[0];

                // 删除不需要的行
                // 先删除endRow之后的行
                if (endRow < totalRows)
                {
                    splitSheet.DeleteRow(endRow + 1, totalRows - endRow);
                }
                // 再删除startRow之前的行（除了标题行）
                if (startRow > 2)
                {
                    splitSheet.DeleteRow(2, startRow - 2);
                }

                // 保存并处理拆分后的文件
                using var stream = new MemoryStream();
                splitPackage.SaveAs(stream);
                stream.Position = 0;
                action?.Invoke(i + 1, stream);
            }

            return true;
        }
        catch (Exception)
        {
            throw;
        }
    }
}
