using Microsoft.AspNetCore.Mvc;
using ZR.Model.Business.Dto;
using ZR.Model.Business;
using ZR.Service.Business.IBusinessService;
using ZR.Admin.WebApi.Filters;
using MiniExcelLibs;

//创建时间：2024-12-23
namespace ZR.Admin.WebApi.Controllers.Business
{
    /// <summary>
    /// 只是测试
    /// </summary>
    [Verify]
    [Route("business/Testtable")]
    public class TesttableController : BaseController
    {
        /// <summary>
        /// 只是测试接口
        /// </summary>
        private readonly ITesttableService _TesttableService;

        public TesttableController(ITesttableService TesttableService)
        {
            _TesttableService = TesttableService;
        }

        /// <summary>
        /// 查询只是测试列表
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        [HttpGet("list")]
        [ActionPermissionFilter(Permission = "testtable:list")]
        public IActionResult QueryTesttable([FromQuery] TesttableQueryDto parm)
        {
            var response = _TesttableService.GetList(parm);
            return SUCCESS(response);
        }


        /// <summary>
        /// 查询只是测试详情
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet("{Id}")]
        [ActionPermissionFilter(Permission = "testtable:query")]
        public IActionResult GetTesttable(long Id)
        {
            var response = _TesttableService.GetInfo(Id);
            
            var info = response.Adapt<TesttableDto>();
            return SUCCESS(info);
        }

        /// <summary>
        /// 添加只是测试
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionPermissionFilter(Permission = "testtable:add")]
        [Log(Title = "只是测试", BusinessType = BusinessType.INSERT)]
        public IActionResult AddTesttable([FromBody] TesttableDto parm)
        {
            var modal = parm.Adapt<Testtable>().ToCreate(HttpContext);

            var response = _TesttableService.AddTesttable(modal);

            return SUCCESS(response);
        }

        /// <summary>
        /// 更新只是测试
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [ActionPermissionFilter(Permission = "testtable:edit")]
        [Log(Title = "只是测试", BusinessType = BusinessType.UPDATE)]
        public IActionResult UpdateTesttable([FromBody] TesttableDto parm)
        {
            var modal = parm.Adapt<Testtable>().ToUpdate(HttpContext);
            var response = _TesttableService.UpdateTesttable(modal);

            return ToResponse(response);
        }

        /// <summary>
        /// 删除只是测试
        /// </summary>
        /// <returns></returns>
        [HttpPost("delete/{ids}")]
        [ActionPermissionFilter(Permission = "testtable:delete")]
        [Log(Title = "只是测试", BusinessType = BusinessType.DELETE)]
        public IActionResult DeleteTesttable([FromRoute]string ids)
        {
            var idArr = Tools.SplitAndConvert<long>(ids);

            return ToResponse(_TesttableService.Delete(idArr));
        }

        /// <summary>
        /// 导出只是测试
        /// </summary>
        /// <returns></returns>
        [Log(Title = "只是测试", BusinessType = BusinessType.EXPORT, IsSaveResponseData = false)]
        [HttpGet("export")]
        [ActionPermissionFilter(Permission = "testtable:export")]
        public IActionResult Export([FromQuery] TesttableQueryDto parm)
        {
            parm.PageNum = 1;
            parm.PageSize = 100000;
            var list = _TesttableService.ExportList(parm).Result;
            if (list == null || list.Count <= 0)
            {
                return ToResponse(ResultCode.FAIL, "没有要导出的数据");
            }
            var result = ExportExcelMini(list, "只是测试", "只是测试");
            return ExportExcel(result.Item2, result.Item1);
        }

        /// <summary>
        /// 清空只是测试
        /// </summary>
        /// <returns></returns>
        [Log(Title = "只是测试", BusinessType = BusinessType.CLEAN)]
        [ActionPermissionFilter(Permission = "testtable:delete")]
        [HttpPost("clean")]
        public IActionResult Clear()
        {
            if (!HttpContextExtension.IsAdmin(HttpContext))
            {
                return ToResponse(ResultCode.FAIL, "操作失败");
            }
            return SUCCESS(_TesttableService.TruncateTesttable());
        }

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="formFile"></param>
        /// <returns></returns>
        [HttpPost("importData")]
        [Log(Title = "只是测试导入", BusinessType = BusinessType.IMPORT, IsSaveRequestData = false)]
        [ActionPermissionFilter(Permission = "testtable:import")]
        public IActionResult ImportData([FromForm(Name = "file")] IFormFile formFile)
        {
            List<TesttableDto> list = new();
            using (var stream = formFile.OpenReadStream())
            {
                list = stream.Query<TesttableDto>(startCell: "A1").ToList();
            }

            return SUCCESS(_TesttableService.ImportTesttable(list.Adapt<List<Testtable>>()));
        }

        /// <summary>
        /// 只是测试导入模板下载
        /// </summary>
        /// <returns></returns>
        [HttpGet("importTemplate")]
        [Log(Title = "只是测试模板", BusinessType = BusinessType.EXPORT, IsSaveResponseData = false)]
        [AllowAnonymous]
        public IActionResult ImportTemplateExcel()
        {
            var result = DownloadImportTemplate(new List<TesttableDto>() { }, "Testtable");
            return ExportExcel(result.Item2, result.Item1);
        }

    }
}