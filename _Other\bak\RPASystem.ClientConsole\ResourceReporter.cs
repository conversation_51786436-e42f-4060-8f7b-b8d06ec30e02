﻿using Microsoft.AspNetCore.SignalR.Client;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace RPASystem.ClientConsole
{
    public class ResourceReporter
    {
        // 静态单例实例
        public static readonly ResourceReporter Instance = new ResourceReporter();

        // 私有构造函数
        private ResourceReporter()
        {
        }

        public void ReportResourceInfo(HubConnection connection, string machineName)
        {
            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        //Console.WriteLine($"Reporting resource info for {machineName}");
                        var resourceInfo = await GetResourceInfoAsync(machineName);
                        await connection.SendAsync("UpdateResourceInfo", resourceInfo);
                        Console.WriteLine($"成功上传资源信息： {machineName}");
                        await Task.Delay(60000); // 每 60 秒上报一次
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error reporting resource info: {ex.Message}");
                        await Task.Delay(60000);
                    }
                }
            });
        }

        private async Task<ResourceInfoDto> GetResourceInfoAsync(string machineName)
        {
            return new ResourceInfoDto
            {
                MachineName = machineName,
                CpuUsage = (float)Math.Round(await GetCpuUsageAsync()),
                MemoryUsage = (float)Math.Round(await GetMemoryUsageAsync()),
                DiskUsage = await GetDiskUsageAsync()
            };
        }

        private Task<Dictionary<string, string>> GetDiskUsageAsync()
        {
            var diskUsage = new Dictionary<string, string>();

            foreach (var drive in DriveInfo.GetDrives())
            {
                if (drive.IsReady)
                {
                    float usedSpace = drive.TotalSize - drive.AvailableFreeSpace;
                    float usagePercentage = (usedSpace / drive.TotalSize) * 100;
                    diskUsage[drive.Name.TrimEnd('\\')] = $"{Math.Round(usagePercentage)}%";
                }
            }

            return Task.FromResult(diskUsage);
        }


        private async Task<float> GetCpuUsageAsync()
        {
            return await GetPerformanceCounterValueAsync("Processor", "% Processor Time", "_Total");
        }

        private async Task<float> GetMemoryUsageAsync()
        {
            return await GetPerformanceCounterValueAsync("Memory", "% Committed Bytes In Use");
        }

        //private async Task<Dictionary<string, string>> GetDiskUsageAsync()
        //{
        //    var diskUsage = new Dictionary<string, string>();
        //    foreach (var drive in DriveInfo.GetDrives())
        //    {
        //        if (drive.IsReady)
        //        {
        //            float usedSpace = drive.TotalSize - drive.AvailableFreeSpace;
        //            float usagePercentage = (usedSpace / drive.TotalSize) * 100;
        //            diskUsage[drive.Name.TrimEnd('\\')] = $"{Math.Round(usagePercentage)}%";
        //        }
        //    }
        //    return diskUsage;
        //}

        private async Task<float> GetPerformanceCounterValueAsync(string categoryName, string counterName, string instanceName = null)
        {
            using (PerformanceCounter counter = new PerformanceCounter(categoryName, counterName, instanceName))
            {
                counter.NextValue();
                await Task.Delay(1000); // 等待 1 秒以获取稳定值
                return counter.NextValue();
            }
        }
    }
}