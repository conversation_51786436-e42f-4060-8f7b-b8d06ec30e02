using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using RPASystem.Model;
using Infrastructure.Attribute;

public interface ITaskNameGenerator
{
    Task<string> GenerateMainTaskNameAsync();
    Task<string> GenerateSubTaskNameAsync(string parentTaskName);
}

[AppService(ServiceType = typeof(ITaskNameGenerator), ServiceLifetime = LifeTime.Scoped)]
public class TaskNameGenerator : ITaskNameGenerator
{
    private readonly RPASystemDbContext dbContext;
    
    public TaskNameGenerator(RPASystemDbContext dbContext)
    {
        this.dbContext = dbContext;
    }

    public async Task<string> GenerateMainTaskNameAsync()
    {
        string dateStr = DateTime.Now.ToString("yyyyMMdd");
        
        using var transaction = await dbContext.Database.BeginTransactionAsync();
        try
        {
            // 查询当天最大序号的主任务
            var maxTaskName = await dbContext.JobTasks
                .FromSqlRaw(@"
                    SELECT * 
                    FROM JobTasks 
                    WHERE TaskName LIKE {0} 
                    AND LENGTH(TaskName) = 11 
                    AND TaskName NOT LIKE '%-%'
                    ORDER BY TaskName DESC 
                    LIMIT 1 
                    FOR UPDATE", 
                    dateStr + "%")
                .Select(t => t.TaskName)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (maxTaskName != null)
            {
                if (int.TryParse(maxTaskName.Substring(8), out int currentSequence))
                {
                    sequence = currentSequence + 1;
                }
            }

            if (sequence > 999)
            {
                throw new Exception("当天任务序号已超过最大值(999)");
            }

            string newTaskName = $"{dateStr}{sequence:D3}";
            await transaction.CommitAsync();
            return newTaskName;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<string> GenerateSubTaskNameAsync(string parentTaskName)
    {
        using var transaction = await dbContext.Database.BeginTransactionAsync(System.Data.IsolationLevel.Serializable);
        try
        {
            // 获取所有直接子任务，使用悲观锁
            var subTasks = await dbContext.JobTasks
                .FromSqlRaw(@"
                    SELECT * 
                    FROM JobTasks 
                    WHERE TaskName REGEXP {0}
                    AND TaskName NOT REGEXP {1}
                    FOR UPDATE", 
                    $"^{parentTaskName}-[0-9]+$",  // 匹配直接子任务
                    $"^{parentTaskName}-[0-9]+-")  // 排除孙子任务
                .Select(t => t.TaskName)
                .ToListAsync();

            int sequence = 1;
            if (subTasks.Any())
            {
                // 提取所有数字后缀并找到最大值
                var maxSequence = subTasks
                    .Select(name => 
                    {
                        var lastPart = name.Split('-').Last();
                        return int.TryParse(lastPart, out int seq) ? seq : 0;
                    })
                    .Max();
                sequence = maxSequence + 1;
            }

            string newTaskName = $"{parentTaskName}-{sequence}";
            await transaction.CommitAsync();
            return newTaskName;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
