namespace RPASystem.Model
{
    public class OrchestrationTaskDto
    {
        public long? ParentTaskID { get; set; }
        public string ProgramName { get; set; }
        public string InputParameters { get; set; }
        public int? TaskPriority { get; set; } = 10;
        public string? ResourceSelection { get; set; }
        public string? TaskName { get; set;}
        public string? Notes { get; set; }
    }

    public class OrcDto
    {
        public long? pid { get; set; }
        public string n { get; set; }
        public string p { get; set; }
        public int? pn { get; set; }
        public string? rs { get; set; }
    }


    public class OrchestrationTaskStatusDto
    {
        public long TaskId { get; set; }
        public JobTaskStatusEnum Status { get; set; }
        public string OutputResults { get; set; }
    }

}