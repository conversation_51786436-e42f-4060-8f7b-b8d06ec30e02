
namespace ZR.Model.Business
{
    /// <summary>
    /// 只是测试
    /// </summary>
    [SugarTable("testtable")]
    public class Testtable
    {
        /// <summary>
        /// Id 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public long? Id { get; set; }

        /// <summary>
        /// Name 
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// MyType 
        /// </summary>
        public int? MyType { get; set; }

    }
}