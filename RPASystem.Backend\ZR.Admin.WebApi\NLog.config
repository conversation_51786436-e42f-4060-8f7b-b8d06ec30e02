<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="nlog-internal.log">

	<!--Nlog 文档参考地址：https://github.com/NLog/NLog/wiki/Getting-started-with-ASP.NET-Core-2-->
	<!--internalLogLevel Nlog内部日志记录为Off关闭。除非纠错，不可以设为Trace否则速度很慢，起码Debug以上-->
	<!--自定义变量-->
	<variable name="logDir" value="${basedir}/nlogs/${date:format=yyyyMMdd}/${level}.log"/>

	<!-- Load the ASP.NET Core plugin,enable asp.net core layout renderers-->
	<extensions>
		<add assembly="NLog.Web.AspNetCore" />
	</extensions>

	<!--define various log targets-->
	<targets async="true">
		<!--写入文件-->
		<!--滚动日志文件上限数，滚动日志文件数达到上限新的文件内容会覆盖旧文件内容 -->
		<!--archiveAboveSize每个日志文件大小的最大值（单位：字节），主日志文件超过大小超过该值时会将文件内容写入滚动日志并清空主日志文件内容-->
		<!--${basedir}表示当前应用程序域所在的根目录-->
		<target name="allfile" xsi:type="File"
				fileName="${basedir}/adminlogs/all.txt"
				archiveFileName="${basedir}/adminlogs/bak/all/all.{###}.txt"
				archiveEvery="Day"
				archiveNumbering="DateAndSequence"
				archiveAboveSize="20000000"
				maxArchiveFiles="30"
				keepFileOpen="true"
				layout="${longdate}|${event-properties:item=EventId:whenEmpty=0}|${aspnet-request-connection-id}|${uppercase:${level}}|${logger}|${aspnet-request-iP:CheckForwardedForHeader=true}|${event-properties:item=user:whenEmpty=-}|url: ${aspnet-request-url}|${message:whenEmpty=-}|${event-properties:item=requestParam}|${event-properties:item=jsonResult}"/>

		<!--错误日志-->
		<target name="errorfile" xsi:type="File"
				fileName="${basedir}/adminlogs/error.txt"
				archiveFileName="${basedir}/adminlogs/bak/error/error.{###}.txt"
				archiveEvery="Day"
				archiveNumbering="DateAndSequence"
				archiveAboveSize="20000000"
				maxArchiveFiles="30"
				keepFileOpen="true"
				layout="${longdate}|${event-properties:item=EventId:whenEmpty=0}|${aspnet-request-connection-id}|${uppercase:${level}}|${logger}${newline}用户IP：${aspnet-request-iP:CheckForwardedForHeader=true}|${event-properties:item=user}${newline}请求地址：${aspnet-request-url}${newline}错误消息：${message}${newline}请求参数：${event-properties:item=requestParam}${newline}请求结果：${event-properties:item=jsonResult}${newline}${onexception:${exception:format=tostring}"/>

		<!--SQL文件-->
		<target name="sqlfile" xsi:type="File"
			fileName="${basedir}/adminlogs/sql.txt"
			archiveFileName="${basedir}/adminlogs/bak/sql/sql{###}.txt"
			archiveEvery="Day"
			archiveNumbering="DateAndSequence"
			archiveAboveSize="20000000"
			maxArchiveFiles="30"
			keepFileOpen="false"
			layout="${longdate} | ${uppercase:${level}} | ${aspnet-request-connection-id} | ${aspnet-request-iP} | ${aspnet-request-headers:HeaderNames=userName} | ${aspnet-request-url} ${newline}${message}"/>

		<!--写入彩色控制台-->
		<target name="consoleSql" xsi:type="ColoredConsole" useDefaultRowHighlightingRules="false"
				layout="${date:format=MM-dd HH\:mm\:ss}|${aspnet-request-iP}|${aspnet-request-connection-id}|${aspnet-request-url}${newline}${message}">
			<highlight-row condition="level == LogLevel.Debug" foregroundColor="DarkGray" />
			<highlight-row condition="level == LogLevel.Info" foregroundColor="Gray" />
			<highlight-row condition="level == LogLevel.Warn" foregroundColor="Yellow" />
			<highlight-row condition="level == LogLevel.Error" foregroundColor="Red" />
			<highlight-row condition="level == LogLevel.Fatal" foregroundColor="Red" backgroundColor="White" />
			<highlight-word regex="SQL" foregroundColor="Blue" />
			<highlight-word regex="【" foregroundColor="Blue" />
			<highlight-word regex="】" foregroundColor="Blue" />
		</target>

		<!--写入黑洞-->
		<target name="blackhole" xsi:type="Null" />
	</targets>

	<rules>
		<!-- 除非调试需要，把 .NET Core 程序集的 Debug 输出都屏蔽 Trace -> Debug-> Info ->Warn-> Error-> Critical-->
		<!--跳过所有级别的Microsoft组件的日志记录-->
		<logger name="Microsoft.*" writeTo="blackhole" final="true" />
		<!-- 除非调试需要，把系统的 Debug 输出都屏蔽 -->
		<!--<logger name="System.*" writeTo="blackhole" final="true" />-->
		<!-- Quartz -->
		<logger name="Quartz*" minlevel="Trace" maxlevel="Info" final="true" />
		<logger name="*.SqlSugar.SqlsugarSetup" final="true" writeTo="consoleSql,sqlfile"/>
		<logger name="*" minLevel="Trace" writeTo="allfile" />
		<logger name="*.GlobalExceptionMiddleware" final="true" writeTo="consoleSql,errorfile"/>
		
		<!--Skip non-critical Microsoft logs and so log only own logs-->
		<logger name="Microsoft.*,Quartz.Core.QuartzSchedulerThread" maxlevel="Info" final="true" />
	</rules>
</nlog>
