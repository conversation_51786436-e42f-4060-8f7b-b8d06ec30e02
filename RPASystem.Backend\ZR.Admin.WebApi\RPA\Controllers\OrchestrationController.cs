using Microsoft.AspNetCore.Mvc;
using RPASystem.Model;
using RPASystem.Service;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.SignalR;
using RPASystem.WebApi.Hubs;
using System.Text.Json;

namespace Controllers
{

    [ApiController]
    [Route("api/[controller]")]
    public class OrchestrationController : ControllerBase
    {
        private readonly IOrchestrationService orchestrationService;
        private readonly IHubContext<ResourceMachineHub> resourceMachineHubContext;
        private readonly IJobTaskService jobTaskService;
        private readonly ILogger<OrchestrationController> logger;

        public OrchestrationController(IOrchestrationService orchestrationService, IHubContext<ResourceMachineHub> resourceMachineHubContext, IJobTaskService jobTaskService, ILogger<OrchestrationController> logger)
        {
            this.orchestrationService = orchestrationService;
            this.resourceMachineHubContext = resourceMachineHubContext;
            this.jobTaskService = jobTaskService;
            this.logger = logger;
        }

        [HttpPost("createOrcJobTask")]
        public async Task<IActionResult> CreateOrchestrationTask([FromBody] OrchestrationTaskDto dto)
        {
            try
            {
                var taskId = await orchestrationService.CreateOrchestrationTask(dto);
                return Ok(taskId);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        //简单版
        [HttpPost("create")]
        public async Task<IActionResult> Create()
        {
            try
            {
                using var reader = new StreamReader(Request.Body);
                string content = await reader.ReadToEndAsync();

                // 解析为 JObject
                JObject json = CommandLineParser.ParseToJObject(content);

                // 将 JObject 转换为目标对象
                OrcDto orcDto = CommandLineParser.ConvertToObject<OrcDto>(json);

                OrchestrationTaskDto dto = new OrchestrationTaskDto()
                {
                    ParentTaskID = orcDto.pid,
                    ProgramName = orcDto.n,
                    InputParameters = orcDto.p,
                    TaskPriority = orcDto.pn,
                    ResourceSelection = orcDto.rs
                };

                var taskId = await orchestrationService.CreateOrchestrationTask(dto);
                await resourceMachineHubContext.Clients.Group("WebClients").SendAsync("RefreshJobTasks");
                return Ok(taskId);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        [HttpGet("getOrcJobTaskStatus")]
        public async Task<IActionResult> GetOrchestrationTaskStatus(long taskId)
        {
            try
            {
                var status = await orchestrationService.GetOrchestrationTaskStatus(taskId);
                return Ok(status.Status);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        [HttpGet("getReturn")]
        public async Task<IActionResult> GetTaskReturnVal(long taskId)
        {
            try
            {
                var ret = await orchestrationService.GetOrchestrationTaskStatus(taskId);
                if (ret?.OutputResults != null)
                {
                    try
                    {
                        // 解析 OutputResults JSON 字符串
                        var outputResults = JsonSerializer.Deserialize<JsonElement>(ret.OutputResults);
                        if (outputResults.TryGetProperty("ReturnResult", out JsonElement returnResult))
                        {
                            // 直接返回 ReturnResult 的值
                            return Ok(returnResult.GetString());
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但不抛出
                        logger.LogError(ex, "解析 OutputResults 失败");
                    }
                }

                // 如果解析失败或没有找到 ReturnResult，返回原始的 OutputResults
                return Ok(ret?.OutputResults);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        [HttpGet("setDone")]
        public async Task<IActionResult> SetDone(long taskId)
        {
            try
            {
                var ret = await jobTaskService.SetStatusDone(taskId);
                await resourceMachineHubContext.Clients.Group("WebClients").SendAsync("RefreshJobTasks");
                return Ok(ret);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }
    }



}