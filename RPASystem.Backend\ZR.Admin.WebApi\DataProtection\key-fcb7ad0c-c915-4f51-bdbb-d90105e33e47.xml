﻿<?xml version="1.0" encoding="utf-8"?>
<key id="fcb7ad0c-c915-4f51-bdbb-d90105e33e47" version="1">
  <creationDate>2024-12-10T02:37:23.3929752Z</creationDate>
  <activationDate>2024-12-10T02:37:23.3880417Z</activationDate>
  <expirationDate>2025-03-10T02:37:23.3880417Z</expirationDate>
  <descriptor deserializerType="Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60">
    <descriptor>
      <encryption algorithm="AES_256_CBC" />
      <validation algorithm="HMACSHA256" />
      <masterKey p4:requiresEncryption="true" xmlns:p4="http://schemas.asp.net/2015/03/dataProtection">
        <!-- Warning: the key below is in an unencrypted form. -->
        <value>UWtyjAUL4ULOuC2/JYdzDq1rpydGGjJ2y6rk8Uep8f81N9SkDXXhsMAVcmZdckA1MMhx9L11fh8xF9S/NWl4ng==</value>
      </masterKey>
    </descriptor>
  </descriptor>
</key>