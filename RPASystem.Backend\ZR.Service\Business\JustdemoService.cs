using Infrastructure.Attribute;
using Infrastructure.Extensions;
using ZR.Model.Business.Dto;
using ZR.Model.Business;
using ZR.Repository;
using ZR.Service.Business.IBusinessService;

namespace ZR.Service.Business
{
    /// <summary>
    /// 功能名只是演示Service业务层处理
    /// </summary>
    [AppService(ServiceType = typeof(IJustdemoService), ServiceLifetime = LifeTime.Transient)]
    public class JustdemoService : BaseService<Justdemo>, IJustdemoService
    {
        /// <summary>
        /// 查询功能名只是演示列表
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        public PagedInfo<JustdemoDto> GetList(JustdemoQueryDto parm)
        {
            var predicate = QueryExp(parm);

            var response = Queryable()
                .Where(predicate.ToExpression())
                .ToPage<Justdemo, JustdemoDto>(parm);

            return response;
        }


        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public Justdemo GetInfo(long Id)
        {
            var response = Queryable()
                .Where(x => x.Id == Id)
                .First();

            return response;
        }

        /// <summary>
        /// 添加功能名只是演示
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Justdemo AddJustdemo(Justdemo model)
        {
            return Insertable(model).ExecuteReturnEntity();
        }

        /// <summary>
        /// 修改功能名只是演示
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateJustdemo(Justdemo model)
        {
            return Update(model, true);
        }

        /// <summary>
        /// 导出功能名只是演示
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        public PagedInfo<JustdemoDto> ExportList(JustdemoQueryDto parm)
        {
            var predicate = QueryExp(parm);

            var response = Queryable()
                .Where(predicate.ToExpression())
                .Select((it) => new JustdemoDto()
                {
                }, true)
                .ToPage(parm);

            return response;
        }

        /// <summary>
        /// 查询导出表达式
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        private static Expressionable<Justdemo> QueryExp(JustdemoQueryDto parm)
        {
            var predicate = Expressionable.Create<Justdemo>();

            return predicate;
        }
    }
}