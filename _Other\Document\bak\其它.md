
完成所有//> 后的功能需求，完全后还原成 // 注释  

读取"EXE后台管理系统模块分类及功能模块.md"文件，理解需求，不要编写代码


请编写"资源机管理"模块的功能代码，包括：
服务类及方法，需要详细的功能注释，方法体里不要写具体功能实现的代码逻辑，在方法体里需要写详细功能及过程的注释。
Model类
API接口Controler和接口方法，方法体里需要写具体代码逻辑
前端页面vue
数据库脚本输出，存放在"SqlScript"目录下


**EXE编排管理**
   - **功能描述**：
     - 多个EXE可用一个EXE编排重新组织成一个EXE编排。
     - 用户可以通过WEB界面编辑编排脚本（类似于Python代码），将多个EXE任务串连或并连起来运行。
     - 支持在EXE编排脚本中嵌套调用其他编排程序，实现复杂任务的编排。
     - 支持在EXE编排脚本中使用简单的IF语句，根据条件运行不同的任务。
   - **主要功能**：
     - 新建EXE编排
     - 串连和并连EXE任务
     - 嵌套调用其他EXE编排程序
     - 支持简单IF语句


**任务管理**
   - **主要用途**：
   - 创建新任务，并下发到资源机。
   - **功能描述**：
     - 创建EXE任务：用户可以选择EXE程序管理里的EXE程序名称，配置运行参数，并创建任务。
     - 创建编排任务：用户可以选择EXE编排管理里已创建的EXE编排程序，配置运行参数，并创建任务。
     - 任务状态监控：实时监控任务的运行状态，提供日志和结果反馈。
   - **主要功能**：
     - 创建单个EXE任务
     - 创建EXE编排任务
     - 配置任务参数
     - 任务状态监控
     - 日志和结果反馈
4. **资源池管理**
   - **功能描述**：
     - 一个资源池可以设置多个资源机，可设置资源机名称。
   - **主要功能**：
     - 创建资源池
     - 资源池管理（添加和移除资源机）




- 是否独站资源机（独占一台资源机运行）