using Microsoft.AspNetCore.Mvc;
using RPASystem.Service;
using RPASystem.Model;
using System.IO;
using System.Threading.Tasks;

namespace RPASystem.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FileStorageController : ControllerBase
    {
        private readonly IFileStorageService fileStorageService;

        public FileStorageController(IFileStorageService fileStorageService)
        {
            this.fileStorageService = fileStorageService;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(IFormFile file, [FromForm] string? remark)
        {
            if (file == null || file.Length == 0)
                return BadRequest("文件无效");

            using var memoryStream = new MemoryStream();
            await file.CopyToAsync(memoryStream);

            var fileStorage = new FileStorage
            {
                FileName = Path.GetFileNameWithoutExtension(file.FileName),
                FileExtension = Path.GetExtension(file.FileName),
                FileData = memoryStream.ToArray(),
                Remark = remark
            };

            var fileId = await fileStorageService.UploadFileAsync(fileStorage);
            return Ok(new { FileId = fileId });
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetFile(long id)
        {
            var file = await fileStorageService.GetFileAsync(id);
            if (file == null)
                return NotFound();

            return File(file.FileData, "application/octet-stream", file.FileName + file.FileExtension);
        }

        [HttpGet("list")]
        public async Task<IActionResult> GetFiles([FromQuery] int pageIndex = 1, 
            [FromQuery] int pageSize = 10,
            [FromQuery] string? searchFileName = null,
            [FromQuery] string? searchFileExtension = null)
        {
            var (files, total) = await fileStorageService.GetFilesByPageAsync(pageIndex, pageSize, searchFileName, searchFileExtension);
            
            return Ok(new
            {
                items = files.Select(f => new
                {
                    f.ID,
                    f.FileName,
                    f.FileExtension,
                    f.UploadTime,
                    f.ModifyTime,
                    f.Remark
                }),
                total
            });
        }

        [HttpPut("{id}/remark")]
        public async Task<IActionResult> UpdateFileRemark(long id, [FromBody] string? remark)
        {
            var result = await fileStorageService.UpdateFileRemarkAsync(id, remark);
            if (!result) return NotFound();
            return Ok();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateFile(long id, IFormFile file, [FromForm] string remark)
        {
            if (file == null || file.Length == 0)
                return BadRequest("文件无效");

            var existingFile = await fileStorageService.GetFileAsync(id);
            if (existingFile == null)
                return NotFound();

            using var memoryStream = new MemoryStream();
            await file.CopyToAsync(memoryStream);

            var fileName = Path.GetFileNameWithoutExtension(file.FileName);
            var fileExtension = Path.GetExtension(file.FileName);

            var result = await fileStorageService.UpdateFileAsync(id, memoryStream.ToArray(), fileName, fileExtension, remark);

            if (result)
                return Ok(new { Message = "文件更新成功" });
            else
                return NotFound();
        }

        [HttpPost("uploadForTask")]
        public async Task<IActionResult> UploadFileForTask(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return BadRequest("文件无效");

            using var memoryStream = new MemoryStream();
            await file.CopyToAsync(memoryStream);

            var fileStorage = new FileStorage
            {
                FileName = Path.GetFileNameWithoutExtension(file.FileName),
                FileExtension = Path.GetExtension(file.FileName),
                FileData = memoryStream.ToArray(),
                Remark = "Uploaded for task"
            };

            var fileId = await fileStorageService.UploadFileAsync(fileStorage);
            return Ok(new { FileId = fileId, FileName = file.FileName });
        }

        [HttpPost("batchdelete")]
        public async Task<IActionResult> BatchDeleteFiles([FromBody] List<long> ids)
        {
            if (ids == null || !ids.Any())
                return BadRequest("未选择要删除的文件");

            var result = await fileStorageService.BatchDeleteFilesAsync(ids);
            if (!result)
                return NotFound("未找到要删除的文件");

            return Ok(new { Message = "批量删除成功" });
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFile(long id)
        {
            var result = await fileStorageService.BatchDeleteFilesAsync(new List<long> { id });
            if (!result)
                return NotFound();
                
            return Ok(new { Message = "删除成功" });
        }
    }
}
