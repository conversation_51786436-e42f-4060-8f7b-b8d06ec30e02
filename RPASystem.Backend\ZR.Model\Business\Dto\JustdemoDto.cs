
namespace ZR.Model.Business.Dto
{
    /// <summary>
    /// 功能名只是演示查询对象
    /// </summary>
    public class JustdemoQueryDto : PagerInfo 
    {
    }

    /// <summary>
    /// 功能名只是演示输入输出对象
    /// </summary>
    public class JustdemoDto
    {
        [Required(ErrorMessage = "Id不能为空")]
        [ExcelColumn(Name = "Id")]
        [ExcelColumnName("Id")]
        public long Id { get; set; }

        [ExcelColumn(Name = "DemoName")]
        [ExcelColumnName("DemoName")]
        public string DemoName { get; set; }

        [ExcelColumn(Name = "DemoRemark")]
        [ExcelColumnName("DemoRemark")]
        public string DemoRemark { get; set; }



    }
}