@font-face {
  font-family: "iconfont"; /* Project id 4017520 */
  src: url('iconfont.woff2?t=1695878634619') format('woff2'),
       url('iconfont.woff?t=1695878634619') format('woff'),
       url('iconfont.ttf?t=1695878634619') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-exit-fullscreen:before {
  content: "\e66d";
}

.icon-validCode:before {
  content: "\e621";
}

.icon-index:before {
  content: "\e61f";
}

.icon-tree-table:before {
  content: "\e637";
}

.icon-unlock:before {
  content: "\e654";
}

.icon-zujian:before {
  content: "\e655";
}

.icon-theme:before {
  content: "\e659";
}

.icon-swagger:before {
  content: "\e65c";
}

.icon-star:before {
  content: "\e661";
}

.icon-time:before {
  content: "\e662";
}

.icon-upload:before {
  content: "\e663";
}

.icon-weixin:before {
  content: "\e664";
}

.icon-system:before {
  content: "\e667";
}

.icon-web:before {
  content: "\e668";
}

.icon-tree:before {
  content: "\e669";
}

.icon-user:before {
  content: "\e66a";
}

.icon-tool:before {
  content: "\e66b";
}

.icon-zip:before {
  content: "\e66c";
}

.icon-link:before {
  content: "\e636";
}

.icon-list:before {
  content: "\e638";
}

.icon-international:before {
  content: "\e639";
}

.icon-language:before {
  content: "\e63a";
}

.icon-ipvisits:before {
  content: "\e63c";
}

.icon-money:before {
  content: "\e63d";
}

.icon-message:before {
  content: "\e63e";
}

.icon-login:before {
  content: "\e63f";
}

.icon-lock:before {
  content: "\e640";
}

.icon-menu:before {
  content: "\e641";
}

.icon-log:before {
  content: "\e643";
}

.icon-logininfor:before {
  content: "\e644";
}

.icon-mnt:before {
  content: "\e645";
}

.icon-password:before {
  content: "\e646";
}

.icon-peoples:before {
  content: "\e647";
}

.icon-post:before {
  content: "\e648";
}

.icon-permission:before {
  content: "\e649";
}

.icon-phone:before {
  content: "\e64a";
}

.icon-people:before {
  content: "\e64b";
}

.icon-online:before {
  content: "\e64d";
}

.icon-pdf:before {
  content: "\e64f";
}

.icon-redis:before {
  content: "\e650";
}

.icon-size:before {
  content: "\e651";
}

.icon-search:before {
  content: "\e652";
}

.icon-server:before {
  content: "\e653";
}

.icon-select:before {
  content: "\e656";
}

.icon-question:before {
  content: "\e657";
}

.icon-rate:before {
  content: "\e658";
}

.icon-monitor:before {
  content: "\e65a";
}

.icon-source:before {
  content: "\e65b";
}

.icon-role:before {
  content: "\e65d";
}

.icon-shopping:before {
  content: "\e65e";
}

.icon-skill:before {
  content: "\e65f";
}

.icon-number:before {
  content: "\e660";
}

.icon-a-404:before {
  content: "\e622";
}

.icon-email:before {
  content: "\e623";
}

.icon-example:before {
  content: "\e624";
}

.icon-error:before {
  content: "\e625";
}

.icon-excel:before {
  content: "\e626";
}

.icon-education:before {
  content: "\e627";
}

.icon-eye-open:before {
  content: "\e628";
}

.icon-eye:before {
  content: "\e629";
}

.icon-github:before {
  content: "\e62b";
}

.icon-guide:before {
  content: "\e62c";
}

.icon-gonggao:before {
  content: "\e62d";
}

.icon-icon1:before {
  content: "\e62e";
}

.icon-fullscreen:before {
  content: "\e62f";
}

.icon-icon:before {
  content: "\e630";
}

.icon-image:before {
  content: "\e631";
}

.icon-form:before {
  content: "\e632";
}

.icon-job:before {
  content: "\e635";
}

.icon-cascader:before {
  content: "\e603";
}

.icon-alipay:before {
  content: "\e604";
}

.icon-anq:before {
  content: "\e605";
}

.icon-backup:before {
  content: "\e606";
}

.icon-bug:before {
  content: "\e607";
}

.icon-button:before {
  content: "\e609";
}

.icon-chain:before {
  content: "\e60b";
}

.icon-chart:before {
  content: "\e60c";
}

.icon-checkbox:before {
  content: "\e60d";
}

.icon-clipboard:before {
  content: "\e60e";
}

.icon-codeConsole:before {
  content: "\e60f";
}

.icon-code:before {
  content: "\e610";
}

.icon-color:before {
  content: "\e611";
}

.icon-database:before {
  content: "\e612";
}

.icon-component:before {
  content: "\e613";
}

.icon-dashboard:before {
  content: "\e614";
}

.icon-date:before {
  content: "\e615";
}

.icon-deploy:before {
  content: "\e616";
}

.icon-develop:before {
  content: "\e617";
}

.icon-dept:before {
  content: "\e619";
}

.icon-dictionary:before {
  content: "\e61a";
}

.icon-documentation:before {
  content: "\e61b";
}

.icon-doc:before {
  content: "\e61c";
}

.icon-download:before {
  content: "\e61d";
}

.icon-dict:before {
  content: "\e61e";
}

.icon-edit:before {
  content: "\e620";
}

.icon-app:before {
  content: "\e602";
}

