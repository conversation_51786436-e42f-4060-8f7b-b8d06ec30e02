/*
 Navicat Premium Dump SQL

 Source Server         : VG
 Source Server Type    : MySQL
 Source Server Version : 80026 (8.0.26)
 Source Host           : localhost:3306
 Source Schema         : zradmin

 Target Server Type    : MySQL
 Target Server Version : 80026 (8.0.26)
 File Encoding         : 65001

 Date: 14/02/2025 15:07:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for article
-- ----------------------------
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article`  (
  `Cid` bigint NOT NULL AUTO_INCREMENT COMMENT '文章id',
  `Title` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文章标题',
  `CreateTime` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `UpdateTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '文章内容',
  `AuthorName` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '作者名',
  `UserId` bigint NOT NULL COMMENT '发布者用户id',
  `Status` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文章状态 1、发布 2、草稿',
  `editorType` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编辑器类型markdown,html',
  `Tags` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文章标签',
  `Hits` int NULL DEFAULT 0 COMMENT '点击量',
  `category_Id` int NULL DEFAULT NULL COMMENT '目录id',
  `CoverUrl` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '封面地址',
  `IsPublic` int NULL DEFAULT 0 COMMENT '是否公开 1、公开 0、不公开',
  `IsTop` int NULL DEFAULT NULL COMMENT '是否置顶',
  `ArticleType` int NULL DEFAULT 0 COMMENT '内容类型0、文章 1、随笔 2、动态',
  `AbstractText` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '摘要',
  `CommentNum` int NULL DEFAULT NULL COMMENT '评论数',
  `PraiseNum` int NULL DEFAULT NULL COMMENT '点赞数',
  `UserIP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户IP',
  `Location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地理位置',
  `TopicId` bigint NULL DEFAULT NULL COMMENT '话题ID',
  `CommentSwitch` int NULL DEFAULT NULL COMMENT '评论开关 0、所有人可评论 1、仅粉丝 2、仅自己',
  `AuditStatus` int NULL DEFAULT NULL COMMENT '审核状态 0、待审核 1、通过 2、拒绝',
  PRIMARY KEY (`Cid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '内容管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article
-- ----------------------------

-- ----------------------------
-- Table structure for article_browsing_log
-- ----------------------------
DROP TABLE IF EXISTS `article_browsing_log`;
CREATE TABLE `article_browsing_log`  (
  `LogId` bigint NOT NULL,
  `Location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `UserIP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AddTime` datetime NULL DEFAULT NULL,
  `ArticleId` bigint NULL DEFAULT NULL COMMENT '文章ID',
  `UserId` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`LogId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '内容浏览记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article_browsing_log
-- ----------------------------

-- ----------------------------
-- Table structure for article_comment
-- ----------------------------
DROP TABLE IF EXISTS `article_comment`;
CREATE TABLE `article_comment`  (
  `CommentId` bigint NOT NULL COMMENT '评论ID',
  `UserId` bigint NULL DEFAULT NULL COMMENT '用户id',
  `Content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '内容',
  `ParentId` bigint NULL DEFAULT NULL COMMENT '最顶级留言id',
  `AddTime` datetime NULL DEFAULT NULL COMMENT '评论时间',
  `ReplyUserId` bigint NULL DEFAULT NULL COMMENT '回复用户id',
  `ReplyId` bigint NULL DEFAULT NULL COMMENT '回复留言id',
  `UserIP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户ip',
  `Location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地理位置',
  `PraiseNum` int NULL DEFAULT NULL COMMENT '喜欢次数',
  `ReplyNum` int NULL DEFAULT NULL COMMENT '评论次数',
  `AuditStatus` int NULL DEFAULT NULL COMMENT '审核状态 0、待审核 1、通过 -1、未通过',
  `IsDelete` int NULL DEFAULT NULL COMMENT '描述 :是否删除 1、删除 0、正常\n            空值 : true',
  `ChatImg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '聊天图片',
  `ClassifyId` int NULL DEFAULT NULL COMMENT '分类id（可以对应表)',
  `TargetId` bigint NULL DEFAULT NULL COMMENT '目标id(内容id)',
  `Top` bigint NULL DEFAULT NULL COMMENT '是否置顶',
  `AuthorReply` int NULL DEFAULT NULL COMMENT '作者回复过',
  PRIMARY KEY (`CommentId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article_comment
-- ----------------------------

-- ----------------------------
-- Table structure for article_praise
-- ----------------------------
DROP TABLE IF EXISTS `article_praise`;
CREATE TABLE `article_praise`  (
  `PId` bigint NOT NULL,
  `UserId` bigint NULL DEFAULT NULL,
  `ArticleId` bigint NULL DEFAULT NULL,
  `UserIP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ToUserId` bigint NULL DEFAULT NULL,
  `IsDelete` int NULL DEFAULT NULL,
  `AddTime` datetime NULL DEFAULT NULL,
  `Location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`PId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '内容点赞记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article_praise
-- ----------------------------

-- ----------------------------
-- Table structure for article_topic
-- ----------------------------
DROP TABLE IF EXISTS `article_topic`;
CREATE TABLE `article_topic`  (
  `TopicId` bigint NOT NULL AUTO_INCREMENT COMMENT '话题ID',
  `TopicName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '话题名',
  `TopicDescription` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '话题描述',
  `JoinNum` int NULL DEFAULT NULL COMMENT '参与/发起次数',
  `ViewNum` int NULL DEFAULT NULL COMMENT '浏览次数',
  `AddTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `TopicType` int NULL DEFAULT NULL COMMENT '话题分类',
  PRIMARY KEY (`TopicId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文章话题' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article_topic
-- ----------------------------
INSERT INTO `article_topic` VALUES (1, '美食', NULL, 0, 0, NULL, NULL);
INSERT INTO `article_topic` VALUES (2, '健身', NULL, 0, 0, NULL, NULL);
INSERT INTO `article_topic` VALUES (3, '吃什么', NULL, 0, 0, NULL, NULL);
INSERT INTO `article_topic` VALUES (4, '交友', NULL, 0, 0, NULL, NULL);
INSERT INTO `article_topic` VALUES (5, '宠物', NULL, 0, 0, NULL, NULL);

-- ----------------------------
-- Table structure for articlecategory
-- ----------------------------
DROP TABLE IF EXISTS `articlecategory`;
CREATE TABLE `articlecategory`  (
  `category_id` int NOT NULL AUTO_INCREMENT COMMENT '目录id',
  `Name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '目录名',
  `Icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图标',
  `OrderNum` int NULL DEFAULT NULL COMMENT '排序id',
  `ParentId` int NULL DEFAULT NULL,
  `BgImg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '背景图',
  `Introduce` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '介绍',
  `CategoryType` int NULL DEFAULT NULL COMMENT '分类类型 0、文章  1、圈子',
  `ArticleNum` int NULL DEFAULT NULL COMMENT '文章数',
  `JoinNum` int NULL DEFAULT NULL COMMENT '加入人数',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文章目录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of articlecategory
-- ----------------------------
INSERT INTO `articlecategory` VALUES (1, '前端', 'web', 0, 0, NULL, NULL, 0, 0, 0, '1900-01-01 00:00:00');
INSERT INTO `articlecategory` VALUES (2, '后端', 'monitor', 0, 0, NULL, NULL, 0, 0, 0, '1900-01-01 00:00:00');
INSERT INTO `articlecategory` VALUES (3, '开发工具', 'tool', 0, 0, NULL, NULL, 0, 0, 0, '1900-01-01 00:00:00');
INSERT INTO `articlecategory` VALUES (4, '其他', 'menu', 0, 0, NULL, NULL, 0, 0, 0, '1900-01-01 00:00:00');
INSERT INTO `articlecategory` VALUES (5, '官方圈子', NULL, 0, 0, NULL, NULL, 1, 0, 0, '1900-01-01 00:00:00');

-- ----------------------------
-- Table structure for banner_config
-- ----------------------------
DROP TABLE IF EXISTS `banner_config`;
CREATE TABLE `banner_config`  (
  `Id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `Title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Title',
  `Content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '说明',
  `Link` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '链接',
  `ImgUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
  `JumpType` int NULL DEFAULT NULL COMMENT '跳转类型 0、不跳转 1、外链 2、内部跳转',
  `AddTime` datetime NULL DEFAULT NULL COMMENT '添加时间',
  `ClicksNumber` int NULL DEFAULT NULL COMMENT '点击次数',
  `ShowStatus` int NULL DEFAULT NULL COMMENT '是否显示',
  `AdType` int NULL DEFAULT NULL COMMENT '广告类型',
  `BeginTime` datetime NULL DEFAULT NULL COMMENT 'BeginTime',
  `EndTime` datetime NULL DEFAULT NULL COMMENT 'EndTime',
  `SortId` int NULL DEFAULT NULL COMMENT '排序id',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '广告管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banner_config
-- ----------------------------

-- ----------------------------
-- Table structure for email_log
-- ----------------------------
DROP TABLE IF EXISTS `email_log`;
CREATE TABLE `email_log`  (
  `Id` bigint NOT NULL,
  `FromName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送人名称',
  `FromEmail` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送邮箱',
  `Subject` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮件主题',
  `ToEmails` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '接收邮箱',
  `EmailContent` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '邮件内容',
  `AddTime` datetime NULL DEFAULT NULL COMMENT '发送时间',
  `IsSend` int NULL DEFAULT NULL COMMENT '是否已发送',
  `SendResult` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送结果',
  `FileUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件地址',
  `SendTime` datetime NULL DEFAULT NULL COMMENT '发送时间',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '邮件发送记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of email_log
-- ----------------------------

-- ----------------------------
-- Table structure for emailtpl
-- ----------------------------
DROP TABLE IF EXISTS `emailtpl`;
CREATE TABLE `emailtpl`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '模板内容',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '邮件发送模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of emailtpl
-- ----------------------------

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `TableId` bigint NOT NULL AUTO_INCREMENT COMMENT '表id',
  `DbName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据库名',
  `TableName` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表名',
  `TableComment` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表描述',
  `SubTableName` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联父表的表名',
  `SubTableFkName` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '本表关联父表的外键名',
  `ClassName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'csharp类名',
  `TplCategory` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作 sub主子表操作）',
  `BaseNameSpace` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基本命名空间前缀',
  `ModuleName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `BusinessName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `FunctionName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `FunctionAuthor` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成作者名',
  `GenType` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `GenPath` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '/' COMMENT '代码生成保存路径',
  `Options` varchar(4000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '其他生成选项',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`TableId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代码生成表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table
-- ----------------------------
INSERT INTO `gen_table` VALUES (1, 'zradmin', 'banner_config', '广告管理', NULL, NULL, 'BannerConfig', 'crud', 'ZR.', 'business', 'BannerConfig', '广告管理', 'admin', '0', '/', '{\"ParentMenuId\":0,\"SortType\":\"asc\",\"SortField\":\"\",\"TreeCode\":\"\",\"TreeName\":\"\",\"TreeParentCode\":\"\",\"PermissionPrefix\":\"bannerconfig\",\"CheckedBtn\":[1,2,3],\"ColNum\":12,\"GenerateRepo\":0,\"GenerateMenu\":false,\"OperBtnStyle\":1,\"UseSnowflakeId\":false,\"EnableLog\":false,\"FrontTpl\":2}', 'admin', '2024-12-10 14:08:54', 'admin', '2024-12-11 16:12:01', NULL);
INSERT INTO `gen_table` VALUES (3, 'zradmin', 'justdemo', '演示表', NULL, NULL, 'Justdemo', 'crud', 'ZR.', 'business', 'Justdemo', '功能名只是演示', 'admin', '0', '/', '{\"ParentMenuId\":0,\"SortType\":\"asc\",\"SortField\":\"\",\"TreeCode\":\"\",\"TreeName\":\"\",\"TreeParentCode\":\"\",\"PermissionPrefix\":\"justdemo\",\"CheckedBtn\":[1,2,3,4,5,7],\"ColNum\":12,\"GenerateRepo\":0,\"GenerateMenu\":false,\"OperBtnStyle\":1,\"UseSnowflakeId\":false,\"EnableLog\":false,\"FrontTpl\":2}', 'admin', '2024-12-11 16:50:51', 'admin', '2024-12-11 16:56:08', NULL);
INSERT INTO `gen_table` VALUES (4, 'rpasystem', 'testtable', '测试表', NULL, NULL, 'Testtable', 'crud', 'ZR.', 'business', 'Testtable', '只是测试', 'admin', '0', '/', '{\"ParentMenuId\":0,\"SortType\":\"desc\",\"SortField\":\"Id\",\"TreeCode\":\"\",\"TreeName\":\"\",\"TreeParentCode\":\"\",\"PermissionPrefix\":\"testtable\",\"CheckedBtn\":[1,2,3,4,5,6,7,8],\"ColNum\":12,\"GenerateRepo\":0,\"GenerateMenu\":false,\"OperBtnStyle\":1,\"UseSnowflakeId\":false,\"EnableLog\":false,\"FrontTpl\":2}', 'admin', '2024-12-23 16:04:03', 'admin', '2024-12-23 16:49:45', NULL);

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `ColumnId` bigint NOT NULL AUTO_INCREMENT COMMENT '列id',
  `ColumnName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '导入代码生成表列名 首字母转了小写',
  `TableId` bigint NULL DEFAULT NULL,
  `TableName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ColumnComment` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ColumnType` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据库列类型',
  `CsharpType` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'C#类型',
  `CsharpField` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'C# 字段名 首字母大写',
  `IsPk` tinyint(1) NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `IsRequired` tinyint(1) NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `IsIncrement` tinyint(1) NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `IsInsert` tinyint(1) NULL DEFAULT NULL COMMENT '是否插入（1是）',
  `IsEdit` tinyint(1) NULL DEFAULT NULL COMMENT '是否需要编辑（1是）',
  `IsList` tinyint(1) NULL DEFAULT NULL COMMENT '是否显示列表（1是）',
  `IsQuery` tinyint(1) NULL DEFAULT NULL COMMENT '是否查询（1是）',
  `IsSort` tinyint(1) NULL DEFAULT NULL COMMENT '是否排序（1是）',
  `IsExport` tinyint(1) NULL DEFAULT NULL COMMENT '是否导出（1是）',
  `HtmlType` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `QueryType` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'EQ' COMMENT '查询类型（等于、不等于、大于、小于、范围）',
  `Sort` int NULL DEFAULT NULL,
  `DictType` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字典类型',
  `AutoFillType` int NULL DEFAULT NULL COMMENT '自动填充类型 1、添加 2、编辑 3、添加编辑',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ColumnId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代码生成表字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------
INSERT INTO `gen_table_column` VALUES (1, 'id', 1, 'banner_config', 'id', 'int', 'int', 'Id', 1, 1, 1, 0, 0, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (2, 'title', 1, 'banner_config', 'Title', 'varchar', 'string', 'Title', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (3, 'content', 1, 'banner_config', '说明', 'varchar', 'string', 'Content', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (4, 'link', 1, 'banner_config', '链接', 'varchar', 'string', 'Link', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (5, 'imgUrl', 1, 'banner_config', '图片', 'varchar', 'string', 'ImgUrl', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'imageUpload', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (6, 'jumpType', 1, 'banner_config', '跳转类型 0、不跳转 1、外链 2、内部跳转', 'int', 'int', 'JumpType', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'select', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (7, 'addTime', 1, 'banner_config', '添加时间', 'datetime', 'DateTime', 'AddTime', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'datetime', 'BETWEEN', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (8, 'clicksNumber', 1, 'banner_config', '点击次数', 'int', 'int', 'ClicksNumber', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (9, 'showStatus', 1, 'banner_config', '是否显示', 'int', 'int', 'ShowStatus', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'radio', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (10, 'adType', 1, 'banner_config', '广告类型', 'int', 'int', 'AdType', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'select', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (11, 'beginTime', 1, 'banner_config', 'BeginTime', 'datetime', 'DateTime', 'BeginTime', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'datetime', 'BETWEEN', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (12, 'endTime', 1, 'banner_config', 'EndTime', 'datetime', 'DateTime', 'EndTime', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'datetime', 'BETWEEN', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (13, 'sortId', 1, 'banner_config', '排序id', 'int', 'int', 'SortId', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-10 14:08:54', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (17, 'iD', 3, 'justdemo', 'Id', 'bigint', 'long', 'Id', 1, 1, 0, 1, 0, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-11 16:50:51', 'admin', '2024-12-11 16:56:16', NULL);
INSERT INTO `gen_table_column` VALUES (18, 'demoName', 3, 'justdemo', 'DemoName', 'varchar', 'string', 'DemoName', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-11 16:50:51', 'admin', '2024-12-11 16:56:16', NULL);
INSERT INTO `gen_table_column` VALUES (19, 'demoRemark', 3, 'justdemo', 'DemoRemark', 'text', 'string', 'DemoRemark', 0, 0, 0, 1, 1, 1, 0, 0, 1, 'input', 'EQ', 0, '', 0, 'admin', '2024-12-11 16:50:51', 'admin', '2024-12-11 16:56:16', NULL);
INSERT INTO `gen_table_column` VALUES (20, 'iD', 4, 'testtable', 'Id', 'bigint unsigned', 'long', 'Id', 1, 0, 1, 0, 0, 1, 0, 0, 1, 'input', 'EQ', 0, '', 1, 'admin', '2024-12-23 16:04:03', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (21, 'name', 4, 'testtable', 'Name', 'varchar', 'string', 'Name', 0, 0, 0, 1, 1, 1, 1, 0, 1, 'input', 'LIKE', 0, '', 0, 'admin', '2024-12-23 16:04:03', NULL, NULL, NULL);
INSERT INTO `gen_table_column` VALUES (22, 'myType', 4, 'testtable', 'MyType', 'enum', 'int', 'MyType', 0, 0, 0, 1, 1, 1, 1, 1, 1, 'radio', 'EQ', 0, 'rpa_run_type', 0, 'admin', '2024-12-23 16:04:03', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for justdemo
-- ----------------------------
DROP TABLE IF EXISTS `justdemo`;
CREATE TABLE `justdemo`  (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `DemoName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `DemoRemark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '只是演示表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of justdemo
-- ----------------------------
INSERT INTO `justdemo` VALUES (1, '2', '3');
INSERT INTO `justdemo` VALUES (2, '3', '4');
INSERT INTO `justdemo` VALUES (4, NULL, NULL);
INSERT INTO `justdemo` VALUES (5, NULL, NULL);
INSERT INTO `justdemo` VALUES (7, NULL, NULL);
INSERT INTO `justdemo` VALUES (10, NULL, NULL);
INSERT INTO `justdemo` VALUES (11, NULL, NULL);
INSERT INTO `justdemo` VALUES (12, NULL, NULL);
INSERT INTO `justdemo` VALUES (13, NULL, NULL);
INSERT INTO `justdemo` VALUES (14, NULL, NULL);
INSERT INTO `justdemo` VALUES (15, NULL, NULL);

-- ----------------------------
-- Table structure for smscode_log
-- ----------------------------
DROP TABLE IF EXISTS `smscode_log`;
CREATE TABLE `smscode_log`  (
  `Id` bigint NOT NULL,
  `SmsCode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '短信验证码',
  `Userid` bigint NULL DEFAULT NULL COMMENT '用户id',
  `PhoneNum` bigint NULL DEFAULT NULL COMMENT '手机号',
  `SmsContent` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '短信内容',
  `AddTime` datetime NULL DEFAULT NULL COMMENT '发送时间',
  `UserIP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户IP',
  `Location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地理位置',
  `SendType` int NULL DEFAULT NULL COMMENT '1、登录 2、注册 3、找回密码',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '短信验证码记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of smscode_log
-- ----------------------------

-- ----------------------------
-- Table structure for sqldifflog
-- ----------------------------
DROP TABLE IF EXISTS `sqldifflog`;
CREATE TABLE `sqldifflog`  (
  `PId` bigint NOT NULL COMMENT '主键',
  `TableName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表名',
  `BusinessData` varchar(4000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务数据内容',
  `DiffType` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '差异类型insert，update，delete',
  `Sql` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '执行sql语句',
  `BeforeData` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '变更前数据',
  `AfterData` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '变更后数据',
  `UserName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作用户名',
  `AddTime` datetime NULL DEFAULT NULL,
  `ConfigId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据库配置id',
  PRIMARY KEY (`PId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据差异日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sqldifflog
-- ----------------------------
INSERT INTO `sqldifflog` VALUES (1871096212753620992, 'banner_config', '删除广告管理', 'delete', 'DELETE FROM `banner_config` WHERE `Id` IN (1) ', '{\"Id\":1,\"Title\":\"123\",\"Content\":\"123123\",\"Link\":\"123\",\"ImgUrl\":null,\"JumpType\":0,\"AddTime\":\"2024-12-23T15:31:03\",\"ClicksNumber\":0,\"ShowStatus\":1,\"AdType\":1,\"BeginTime\":\"2024-12-22T16:00:00\",\"EndTime\":\"2024-12-22T16:00:00\",\"SortId\":0}', NULL, 'admin', '2024-12-23 15:31:15', '0');
INSERT INTO `sqldifflog` VALUES (1871097008887046144, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (5) ', '{\"MenuId\":5,\"MenuName\":\"外部打开111\",\"ParentId\":0,\"OrderNum\":5,\"Path\":\"http://www.baidu.com\",\"Component\":null,\"IsCache\":0,\"IsFrame\":1,\"MenuType\":\"M\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":null,\"Icon\":\"link\",\"menuName_key\":\"menu.officialWebsite\",\"Create_by\":null,\"Create_time\":\"2024-12-10T10:40:31\",\"Update_by\":\"admin\",\"Update_time\":\"2024-12-23T15:33:58\",\"Remark\":null}', NULL, 'admin', '2024-12-23 15:34:25', '0');
INSERT INTO `sqldifflog` VALUES (1888165921613291520, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1134) ', '{\"MenuId\":1134,\"MenuName\":\"导出\",\"ParentId\":1129,\"OrderNum\":5,\"Path\":\"#\",\"Component\":null,\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"F\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":\"testtable:export\",\"Icon\":\"\",\"menuName_key\":null,\"Create_by\":\"system\",\"Create_time\":\"2024-12-23T16:17:32\",\"Update_by\":null,\"Update_time\":null,\"Remark\":null}', NULL, 'admin', '2025-02-08 18:00:11', '0');
INSERT INTO `sqldifflog` VALUES (1888165939921428480, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1130) ', '{\"MenuId\":1130,\"MenuName\":\"查询\",\"ParentId\":1129,\"OrderNum\":1,\"Path\":\"#\",\"Component\":null,\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"F\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":\"testtable:query\",\"Icon\":\"\",\"menuName_key\":null,\"Create_by\":\"system\",\"Create_time\":\"2024-12-23T16:17:32\",\"Update_by\":null,\"Update_time\":null,\"Remark\":null}', NULL, 'admin', '2025-02-08 18:00:15', '0');
INSERT INTO `sqldifflog` VALUES (1888165948670746624, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1131) ', '{\"MenuId\":1131,\"MenuName\":\"新增\",\"ParentId\":1129,\"OrderNum\":2,\"Path\":\"#\",\"Component\":null,\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"F\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":\"testtable:add\",\"Icon\":\"\",\"menuName_key\":null,\"Create_by\":\"system\",\"Create_time\":\"2024-12-23T16:17:32\",\"Update_by\":null,\"Update_time\":null,\"Remark\":null}', NULL, 'admin', '2025-02-08 18:00:17', '0');
INSERT INTO `sqldifflog` VALUES (1888165957571059712, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1132) ', '{\"MenuId\":1132,\"MenuName\":\"删除\",\"ParentId\":1129,\"OrderNum\":3,\"Path\":\"#\",\"Component\":null,\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"F\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":\"testtable:delete\",\"Icon\":\"\",\"menuName_key\":null,\"Create_by\":\"system\",\"Create_time\":\"2024-12-23T16:17:32\",\"Update_by\":null,\"Update_time\":null,\"Remark\":null}', NULL, 'admin', '2025-02-08 18:00:19', '0');
INSERT INTO `sqldifflog` VALUES (1889517773084827648, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1129,1136,1139,1141) ', '{\"MenuId\":1129,\"MenuName\":\"程序管理\",\"ParentId\":0,\"OrderNum\":0,\"Path\":\"\",\"Component\":\"\",\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"M\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":\"\",\"Icon\":\"build\",\"menuName_key\":null,\"Create_by\":\"system\",\"Create_time\":\"2024-12-23T16:17:32\",\"Update_by\":\"admin\",\"Update_time\":\"2025-02-08T18:07:57\",\"Remark\":null}', NULL, 'admin', '2025-02-12 11:31:57', '0');
INSERT INTO `sqldifflog` VALUES (1889517773105799168, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1129,1136,1139,1141) ', '{\"MenuId\":1136,\"MenuName\":\"RPA管理\",\"ParentId\":1129,\"OrderNum\":999,\"Path\":\"ExeProgramManager\",\"Component\":\"RPA/ExeProgramManager\",\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"C\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":null,\"Icon\":\"\",\"menuName_key\":null,\"Create_by\":\"admin\",\"Create_time\":\"2025-02-08T18:08:24\",\"Update_by\":\"admin\",\"Update_time\":\"2025-02-12T11:18:31\",\"Remark\":null}', NULL, 'admin', '2025-02-12 11:31:57', '0');
INSERT INTO `sqldifflog` VALUES (1889517773118382080, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1129,1136,1139,1141) ', '{\"MenuId\":1139,\"MenuName\":\"FileManagement1\",\"ParentId\":1129,\"OrderNum\":999,\"Path\":\"FileManagement\",\"Component\":\"RPA/FileManagement\",\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"C\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":null,\"Icon\":\"\",\"menuName_key\":null,\"Create_by\":\"admin\",\"Create_time\":\"2025-02-12T11:27:58\",\"Update_by\":null,\"Update_time\":null,\"Remark\":null}', NULL, 'admin', '2025-02-12 11:31:57', '0');
INSERT INTO `sqldifflog` VALUES (1889517773139353600, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1129,1136,1139,1141) ', '{\"MenuId\":1141,\"MenuName\":\"任务列表\",\"ParentId\":1129,\"OrderNum\":999,\"Path\":\"JobTaskManager\",\"Component\":\"RPA/JobTaskManager\",\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"C\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":null,\"Icon\":\"\",\"menuName_key\":null,\"Create_by\":\"admin\",\"Create_time\":\"2025-02-12T11:30:39\",\"Update_by\":\"admin\",\"Update_time\":\"2025-02-12T11:31:27\",\"Remark\":null}', NULL, 'admin', '2025-02-12 11:31:57', '0');
INSERT INTO `sqldifflog` VALUES (1889517812385456128, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1137,1140) ', '{\"MenuId\":1137,\"MenuName\":\"任务管理\",\"ParentId\":0,\"OrderNum\":0,\"Path\":\"#\",\"Component\":null,\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"M\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":null,\"Icon\":\"build\",\"menuName_key\":null,\"Create_by\":\"admin\",\"Create_time\":\"2025-02-12T11:12:46\",\"Update_by\":null,\"Update_time\":null,\"Remark\":null}', NULL, 'admin', '2025-02-12 11:32:07', '0');
INSERT INTO `sqldifflog` VALUES (1889517812398039040, 'sys_menu', '删除菜单', 'delete', 'DELETE FROM `sys_menu` WHERE `MenuId` IN (1137,1140) ', '{\"MenuId\":1140,\"MenuName\":\"任务列表\",\"ParentId\":1137,\"OrderNum\":999,\"Path\":\"JobTaskManager\",\"Component\":\"RPA/JobTaskManager\",\"IsCache\":0,\"IsFrame\":0,\"MenuType\":\"C\",\"Visible\":\"0\",\"Status\":\"0\",\"Perms\":null,\"Icon\":\"\",\"menuName_key\":null,\"Create_by\":\"admin\",\"Create_time\":\"2025-02-12T11:29:55\",\"Update_by\":null,\"Update_time\":null,\"Remark\":null}', NULL, 'admin', '2025-02-12 11:32:07', '0');

-- ----------------------------
-- Table structure for sys_common_lang
-- ----------------------------
DROP TABLE IF EXISTS `sys_common_lang`;
CREATE TABLE `sys_common_lang`  (
  `Id` bigint NOT NULL COMMENT 'id',
  `lang_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '语言code',
  `lang_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '语言key',
  `lang_name` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `Addtime` datetime NULL DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '多语言配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_common_lang
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `ConfigId` int NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `ConfigName` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数名称',
  `ConfigKey` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数键名',
  `ConfigValue` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数键值',
  `ConfigType` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统内置（Y是 N否）',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ConfigId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', NULL, '2024-12-10 10:40:31', NULL, NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (2, '账号自助-验证码开关', 'sys.account.captchaOnOff', 'off', 'Y', NULL, '2024-12-10 10:40:31', 'admin', '2024-12-10 16:43:56', '开启验证码功能（off、关闭，1、动态验证码 2、动态gif泡泡 3、泡泡 4、静态验证码）');
INSERT INTO `sys_config` VALUES (3, '开启注册功能', 'sys.account.register', 'False', 'Y', NULL, '2024-12-10 10:40:31', 'admin', '2024-12-10 17:04:34', '注册开关');
INSERT INTO `sys_config` VALUES (4, '允许评论', 'article.comment', '1', 'Y', NULL, '2024-12-10 10:40:31', NULL, NULL, '评论绑定手机号0、全放开1、绑定手机号');
INSERT INTO `sys_config` VALUES (5, '允许发布内容', 'article.publish', '1', 'Y', NULL, '2024-12-10 10:40:31', NULL, NULL, '发布绑定手机号0、全放开1、绑定手机号');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `DeptId` bigint NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `ParentId` bigint NULL DEFAULT NULL COMMENT '父部门ID',
  `Ancestors` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '祖级列表',
  `DeptName` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '部门名称',
  `OrderNum` int NULL DEFAULT NULL COMMENT '显示顺序',
  `Leader` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `Phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `Email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `Status` int NULL DEFAULT 0 COMMENT '部门状态:0正常,1停用',
  `DelFlag` int NULL DEFAULT 0 COMMENT '删除标志（0代表存在 2代表删除）',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`DeptId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '部门配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (1, 0, NULL, '公司总部', 0, NULL, NULL, NULL, 0, 0, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dept` VALUES (2, 1, NULL, '研发部门', 1, NULL, NULL, NULL, 0, 0, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dept` VALUES (3, 1, NULL, '市场部门', 2, NULL, NULL, NULL, 0, 0, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dept` VALUES (4, 1, NULL, '测试部门', 3, NULL, NULL, NULL, 0, 0, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dept` VALUES (5, 1, NULL, '财务部门', 4, NULL, NULL, NULL, 0, 0, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `DictCode` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `DictSort` int NULL DEFAULT NULL COMMENT '字典排序',
  `DictLabel` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典标签',
  `DictValue` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典键值',
  `DictType` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典类型',
  `CssClass` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `ListClass` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `IsDefault` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `Status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态（0正常 1停用）',
  `LangKey` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '多语言翻译key值',
  `Extend1` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '扩展1',
  `Extend2` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '扩展2',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`DictCode`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'common.male', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'common.female', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'common.unknow', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'common.show', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'common.hidden', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'common.normal', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'common.disable', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'common.normal', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '异常', '1', 'sys_job_status', '', 'danger', 'N', '0', 'common.abnormal', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'common.default', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'common.system', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'common.yes', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'common.no', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 0, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (30, 1, '发布', '1', 'sys_article_status', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (31, 2, '草稿', '2', 'sys_article_status', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (32, 1, '中文', 'zh-cn', 'sys_lang_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (33, 2, '英文', 'en', 'sys_lang_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (34, 3, '繁体', 'zh-tw', 'sys_lang_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (35, 1, '程序集', '1', 'sys_job_type', '', 'primary', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (36, 2, 'api', '2', 'sys_job_type', '', 'warning', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (37, 3, 'SQL语句', '3', 'sys_job_type', '', 'danger', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (38, 1, '文章', '0', 'sys_article_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (39, 2, '笔记', '1', 'sys_article_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, 'uniapp2发布入口');
INSERT INTO `sys_dict_data` VALUES (40, 3, '动态', '2', 'sys_article_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (41, 4, '视频', '3', 'sys_article_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (42, 1, '文章', '0', 'article_category_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (43, 2, '圈子', '1', 'article_category_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (44, 1, '移动端首页', '1', 'sys_ad_type', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (45, 1, '所有人可评论', '0', 'sys_comment_permi', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (46, 2, '粉丝', '1', 'sys_comment_permi', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (47, 3, '仅自己', '2', 'sys_comment_permi', '', '', 'N', '0', '', NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (48, 0, '待运行', '0', 'rpa_run_type', '', '', NULL, '0', '', NULL, NULL, 'admin', '2024-12-23 16:20:07', NULL, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (49, 0, '运行中', '1', 'rpa_run_type', '', '', NULL, '0', '', NULL, NULL, 'admin', '2024-12-23 16:21:07', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `DictId` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `DictName` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典名称',
  `DictType` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典类型',
  `Status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '状态 0、正常 1、停用',
  `Type` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'N' COMMENT '系统内置 Y是 N否',
  `CustomSql` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '自定义sql',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`DictId`) USING BTREE,
  UNIQUE INDEX `index_dict_type`(`DictType` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (11, '文章状态', 'sys_article_status', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (12, '多语言类型', 'sys_lang_type', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '多语言字典类型');
INSERT INTO `sys_dict_type` VALUES (13, '任务类型', 'sys_job_type', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '任务类型列表');
INSERT INTO `sys_dict_type` VALUES (14, '邮件模板', 'sql_email_tpl', '0', 'Y', 'SELECT id dictValue, name dictLabel FROM emailTpl', NULL, '2024-12-10 10:40:31', NULL, NULL, '邮件模板列表');
INSERT INTO `sys_dict_type` VALUES (15, '内容类型', 'sys_article_type', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '内容类型');
INSERT INTO `sys_dict_type` VALUES (16, '内容目录类型', 'article_category_type', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (17, '广告类型', 'sys_ad_type', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (18, '评论权限', 'sys_comment_permi', '0', 'Y', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '内容评论权限');
INSERT INTO `sys_dict_type` VALUES (19, 'RPA运行类型', 'rpa_run_type', '0', 'N', NULL, 'admin', '2024-12-23 16:08:39', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_file
-- ----------------------------
DROP TABLE IF EXISTS `sys_file`;
CREATE TABLE `sys_file`  (
  `Id` bigint NOT NULL COMMENT '自增id',
  `RealName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件原名',
  `FileType` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件类型',
  `FileName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '存储文件名',
  `FileUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件存储地址 eg：/uploads/20220202',
  `StorePath` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '仓库位置 eg：/uploads',
  `FileSize` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件大小',
  `FileExt` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件扩展名',
  `Create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '上传时间',
  `StoreType` int NULL DEFAULT NULL COMMENT '存储类型',
  `AccessUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '访问路径',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文件存储表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_file
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `InfoId` bigint NOT NULL AUTO_INCREMENT,
  `UserName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户账号',
  `Status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '登录状态 0成功 1失败',
  `Ipaddr` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登录IP地址',
  `LoginLocation` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登录地点',
  `Browser` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '浏览器类型',
  `Os` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作系统',
  `Msg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '提示消息',
  `LoginTime` datetime NULL DEFAULT NULL COMMENT '访问时间',
  `ClientId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户端id',
  `UserId` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`InfoId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '登录日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (1, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 131.0.0', 'Windows 10', '登录成功', '2024-12-10 11:00:07', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (2, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 131.0.0', 'Windows 10', '登录成功', '2024-12-10 16:44:26', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (3, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 131.0.0', 'Windows 10', '登录成功', '2024-12-10 17:03:51', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (4, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 131.0.0', 'Windows 10', '登录成功', '2024-12-10 17:06:13', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (5, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 131.0.0', 'Windows 10', '登录成功', '2024-12-10 17:08:59', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (6, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 131.0.0', 'Windows 10', '登录成功', '2024-12-11 17:57:16', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (7, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 131.0.0', 'Windows 10', '登录成功', '2024-12-23 15:22:33', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (8, 'admin', '1', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '用户名或密码错误', '2025-02-06 17:38:30', 'f0cf89fbad2efb92edc59f0da9604348', 0);
INSERT INTO `sys_logininfor` VALUES (9, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '登录成功', '2025-02-06 17:38:33', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (10, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '登录成功', '2025-02-08 14:49:08', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (11, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '登录成功', '2025-02-08 14:52:42', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (12, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '登录成功', '2025-02-10 14:24:21', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (13, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '登录成功', '2025-02-12 10:57:54', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (14, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '登录成功', '2025-02-12 11:09:50', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (15, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '登录成功', '2025-02-12 11:10:39', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (16, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 132.0.0', 'Windows 10', '登录成功', '2025-02-12 11:10:51', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (17, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 133.0.0', 'Windows 10', '登录成功', '2025-02-14 10:52:21', 'f0cf89fbad2efb92edc59f0da9604348', 1);
INSERT INTO `sys_logininfor` VALUES (18, 'admin', '0', '127.0.0.1', '0-内网IP-内网IP', 'Windows 10 Other Edge 133.0.0', 'Windows 10', '登录成功', '2025-02-14 10:52:23', 'f0cf89fbad2efb92edc59f0da9604348', 1);

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `MenuId` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `MenuName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '菜单名称',
  `ParentId` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `OrderNum` int NULL DEFAULT 0 COMMENT '显示顺序',
  `Path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路由地址',
  `Component` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `IsCache` int NULL DEFAULT 0 COMMENT '是否缓存（1不缓存 0缓存）',
  `IsFrame` int NULL DEFAULT 0 COMMENT '是否外链 1、是 0、否',
  `MenuType` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型（M目录 C菜单 F按钮 L链接）',
  `Visible` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '显示状态（0显示 1隐藏）',
  `Status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `Perms` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限字符串',
  `Icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `menuName_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '菜单名key',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`MenuId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1150 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, 0, 0, 'M', '0', '0', NULL, 'system', 'menu.system', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, 0, 0, 'M', '0', '0', NULL, 'monitor', 'menu.monitoring', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 3, 'tool', NULL, 0, 0, 'M', '1', '0', NULL, 'tool', 'menu.systemTools', NULL, '2024-12-10 10:40:31', 'admin', '2025-02-14 14:56:22', NULL);
INSERT INTO `sys_menu` VALUES (6, '控制台', 0, 0, 'dashboard', 'index_v1', 0, 0, 'C', '1', '0', NULL, 'dashboard', 'menu.dashboard', NULL, '2024-12-10 10:40:31', 'admin', '2025-02-08 17:59:49', NULL);
INSERT INTO `sys_menu` VALUES (7, '组件示例', 0, 1, '', NULL, 0, 0, 'M', '1', '0', NULL, 'zujian', 'menu.zujianDemo', NULL, '2024-12-10 10:40:31', 'admin', '2025-02-14 14:56:12', NULL);
INSERT INTO `sys_menu` VALUES (8, '图标icon', 7, 0, 'icon', 'components/icons/index', 0, 0, 'C', '0', '0', NULL, 'icon1', 'menu.icon', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (9, '邮件管理', 3, 3, 'email', NULL, 0, 0, 'M', '0', '0', NULL, 'email', 'menu.emailList', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (10, '数据大屏', 0, 0, 'dataScreen', 'dataScreen/index', 0, 0, 'M', '1', '0', 'system:datascreen:list', 'server', 'menu.dataScreen', NULL, '2024-12-10 10:40:31', 'admin', '2025-02-08 17:59:54', NULL);
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', 0, 0, 'C', '0', '0', 'system:user:list', 'user', 'menu.systemUser', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', 0, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'menu.systemRole', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'sysmenu', 'system/menu/index', 0, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'menu.systemMenu', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', 0, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'menu.systemDept', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', 0, 0, 'C', '0', '0', 'system:post:list', 'post', 'menu.systemPost', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', 0, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'menu.systemDic', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (106, '角色分配', 1, 2, 'roleusers', 'system/roleusers/index', 0, 0, 'C', '1', '0', 'system:roleusers:list', 'people', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (107, '参数设置', 1, 8, 'config', 'system/config/index', 0, 0, 'C', '0', '0', 'system:config:list', 'edit', 'menu.systemParam', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 10, 'log', NULL, 0, 0, 'M', '0', '0', NULL, 'log', 'menu.systemLog', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (109, '通知公告', 1, 9, 'notice', 'system/notice/index', 0, 0, 'C', '0', '0', 'system:notice:list', 'message', 'menu.systemNotice', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 1, 'job', 'monitor/job/index', 0, 0, 'C', '0', '0', NULL, 'job', 'menu.timedTask', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (111, '在线用户', 2, 2, 'onlineusers', 'monitor/onlineuser/index', 0, 0, 'C', '0', '0', NULL, 'online', 'layout.onlineUsers', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', 0, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'menu.serviceMonitor', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', 0, 0, 'C', '1', '1', 'monitor:cache:list', 'redis', 'menu.cacheMonitor', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (114, '表单构建', 3, 1, 'build', 'tool/build/index', 0, 0, 'C', '0', '0', 'tool:build:list', 'build', 'menu.formBuild', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (115, '代码生成', 3, 2, 'gen', 'tool/gen/index', 0, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'menu.codeGeneration', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (116, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', 0, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'menu.systemInterface', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (117, '发送邮件', 9, 4, 'sendEmail', 'tool/email/sendEmail', 0, 0, 'C', '0', '0', 'tool:email:send', 'emailSend', 'menu.sendEmail', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (118, '内容管理', 0, 18, 'article', NULL, 0, 0, 'M', '1', '0', NULL, 'documentation', 'menu.systemArticle', NULL, '2024-12-10 10:40:31', 'admin', '2025-02-14 14:55:57', NULL);
INSERT INTO `sys_menu` VALUES (119, '内容列表', 118, 1, 'index', 'article/manager', 0, 0, 'C', '0', '0', 'system:article:list', 'list', 'menu.articleList', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', 0, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'menu.operLog', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', 0, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'menu.loginLog', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (502, '数据差异日志', 108, 3, 'SqlDiffLog', 'monitor/SqlDiffLog', 0, 0, 'C', '0', '0', 'sqldifflog:list', 'log', 'menu.dataDiffLog', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1001, '用户查询', 100, 1, '', NULL, 0, 0, 'F', '0', '0', 'system:user:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1002, '用户添加', 100, 2, '', NULL, 0, 0, 'F', '0', '0', 'system:user:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1003, '用户修改', 100, 3, '', NULL, 0, 0, 'F', '0', '0', 'system:user:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1004, '用户删除', 100, 4, '', NULL, 0, 0, 'F', '0', '0', 'system:user:remove', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1005, '用户导出', 100, 5, '', NULL, 0, 0, 'F', '0', '0', 'system:user:export', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1006, '用户导入', 100, 6, '', NULL, 0, 0, 'F', '0', '0', 'system:user:import', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1007, '重置密码', 100, 7, '', NULL, 0, 0, 'F', '0', '0', 'system:user:resetPwd', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1008, '角色查询', 101, 1, '', NULL, 0, 0, 'F', '0', '0', 'system:role:query', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1009, '角色新增', 101, 2, '', NULL, 0, 0, 'F', '0', '0', 'system:role:add', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1010, '角色修改', 101, 3, '', NULL, 0, 0, 'F', '0', '0', 'system:role:edit', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1011, '角色删除', 101, 4, '', NULL, 0, 0, 'F', '0', '0', 'system:role:remove', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1012, '菜单授权', 101, 5, '', NULL, 0, 0, 'F', '0', '0', 'system:role:authorize', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1013, '菜单查询', 102, 1, '', NULL, 0, 0, 'F', '0', '0', 'system:menu:query', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1014, '菜单新增', 102, 2, '', NULL, 0, 0, 'F', '0', '0', 'system:menu:add', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1015, '菜单修改', 102, 3, '', NULL, 0, 0, 'F', '0', '0', 'system:menu:edit', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1016, '菜单删除', 102, 4, '', NULL, 0, 0, 'F', '0', '0', 'system:menu:remove', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1017, '修改排序', 102, 5, '', NULL, 0, 0, 'F', '0', '0', 'system:menu:changeSort', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1018, '部门查询', 103, 1, '', NULL, 0, 0, 'F', '0', '0', 'system:dept:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1019, '部门新增', 103, 2, '', NULL, 0, 0, 'F', '0', '0', 'system:dept:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1020, '部门修改', 103, 3, '', NULL, 0, 0, 'F', '0', '0', 'system:dept:update', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1021, '部门删除', 103, 4, '', NULL, 0, 0, 'F', '0', '0', 'system:dept:remove', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1022, '岗位查询', 104, 1, '', NULL, 0, 0, 'F', '0', '0', 'system:post:list', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1023, '岗位添加', 104, 2, '', NULL, 0, 0, 'F', '0', '0', 'system:post:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1024, '岗位删除', 104, 3, '', NULL, 0, 0, 'F', '0', '0', 'system:post:remove', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1025, '岗位编辑', 104, 4, '', NULL, 0, 0, 'F', '0', '0', 'system:post:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 1, '', NULL, 0, 0, 'F', '0', '0', 'system:dict:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 2, '', NULL, 0, 0, 'F', '0', '0', 'system:dict:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 3, '', NULL, 0, 0, 'F', '0', '0', 'system:dict:remove', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1029, '新增用户', 106, 1, '', NULL, 0, 0, 'F', '0', '0', 'system:roleusers:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1030, '删除用户', 106, 2, '', NULL, 0, 0, 'F', '0', '0', 'system:roleusers:del', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1031, '字典查询', 105, 1, '', NULL, 0, 0, 'F', '0', '0', 'system:dict:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1032, '任务查询', 110, 1, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:job:list', '#', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1033, '任务新增', 110, 2, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:job:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1034, '任务删除', 110, 3, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:job:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1035, '任务修改', 110, 4, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:job:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1036, '任务启动', 110, 5, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:job:start', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1037, '任务运行', 110, 7, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:job:run', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1038, '任务停止', 110, 8, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:job:stop', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1039, '任务日志', 2, 0, 'job/log', 'monitor/job/log', 0, 0, 'C', '1', '0', 'monitor:job:query', 'log', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1040, '任务导出', 110, 10, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:job:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1041, '操作查询', 500, 1, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:operlog:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1042, '操作删除', 500, 2, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:operlog:remove', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1043, '操作日志导出', 500, 3, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:operlog:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1044, '登录查询', 501, 1, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:logininfor:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1045, '登录删除', 501, 1, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:logininfor:remove', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1046, '登录日志导出', 501, 1, '#', NULL, 0, 0, 'F', '0', '0', 'monitor:logininfor:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1047, '内容发布', 118, 2, '/article/publish', 'article/publish', 0, 0, 'C', '1', '0', 'system:article:add', 'log', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1048, '内容审核', 119, 0, '#', NULL, 0, 0, 'F', '0', '0', 'article:audit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1049, '内容修改', 119, 3, '#', NULL, 0, 0, 'F', '0', '0', 'system:article:update', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1050, '内容删除', 119, 4, '#', NULL, 0, 0, 'F', '0', '0', 'system:article:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1051, '查询公告', 109, 1, '#', NULL, 0, 0, 'F', '0', '0', 'system:notice:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1052, '新增公告', 109, 2, '#', NULL, 0, 0, 'F', '0', '0', 'system:notice:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1053, '删除公告', 109, 3, '#', NULL, 0, 0, 'F', '0', '0', 'system:notice:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1054, '修改公告', 109, 4, '#', NULL, 0, 0, 'F', '0', '0', 'system:notice:update', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1055, '导出公告', 109, 5, '#', NULL, 0, 0, 'F', '0', '0', 'system:notice:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1060, '生成修改', 3, 1, '/gen/editTable', 'tool/gen/editTable', 0, 0, 'C', '1', '0', 'tool:gen:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1061, '生成查询', 115, 2, '#', NULL, 0, 0, 'F', '0', '0', 'tool:gen:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1062, '生成删除', 115, 1, '#', NULL, 0, 0, 'F', '0', '0', 'tool:gen:remove', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1063, '导入代码', 115, 1, '#', NULL, 0, 0, 'F', '0', '0', 'tool:gen:import', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1064, '生成代码', 115, 1, '#', NULL, 0, 0, 'F', '0', '0', 'tool:gen:code', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1065, '预览代码', 115, 1, '#', NULL, 0, 0, 'F', '0', '0', 'tool:gen:preview', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1070, '岗位导出', 104, 4, '', NULL, 0, 0, 'F', '0', '0', 'system:post:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1071, '字典导出', 105, 4, '', NULL, 0, 0, 'F', '0', '0', 'system:dict:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1072, '文件存储', 3, 17, 'file', 'tool/file/index', 0, 0, 'C', '0', '0', 'tool:file:list', 'upload', 'menu.fileStorage', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1073, '查询', 1072, 1, '#', NULL, 0, 0, 'F', '0', '0', 'tool:file:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1074, '新增', 1072, 2, '#', NULL, 0, 0, 'F', '0', '0', 'tool:file:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1075, '删除', 1072, 3, '#', NULL, 0, 0, 'F', '0', '0', 'tool:file:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1076, '修改', 1072, 4, '#', NULL, 0, 0, 'F', '0', '0', 'tool:file:update', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1077, '导出', 1072, 5, '#', NULL, 0, 0, 'F', '0', '0', 'tool:file:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1078, '多语言配置', 1, 999, 'commonLang', 'system/commonLang/index', 0, 0, 'C', '0', '0', 'system:lang:list', 'language', 'menu.systemLang', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1079, '查询', 1078, 1, '#', NULL, 0, 0, 'F', '0', '0', 'system:lang:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1080, '新增', 1078, 2, '#', NULL, 0, 0, 'F', '0', '0', 'system:lang:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1081, '删除', 1078, 3, '#', NULL, 0, 0, 'F', '0', '0', 'system:lang:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1082, '修改', 1078, 4, '#', NULL, 0, 0, 'F', '0', '0', 'system:lang:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1083, '导出', 1078, 5, '#', NULL, 0, 0, 'F', '0', '0', 'system:lang:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1084, '内容目录', 118, 999, 'articleCategory', 'article/articleCategory', 0, 0, 'C', '0', '0', 'articlecategory:list', 'tree-table', 'menu.articleCategory', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1085, '查询', 1084, 1, '#', NULL, 0, 0, 'F', '0', '0', 'articlecategory:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1086, '新增', 1084, 2, '#', NULL, 0, 0, 'F', '0', '0', 'articlecategory:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1087, '删除', 1084, 3, '#', NULL, 0, 0, 'F', '0', '0', 'articlecategory:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1088, '修改', 1084, 4, '#', NULL, 0, 0, 'F', '0', '0', 'articlecategory:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1089, '导出', 1084, 5, '#', NULL, 0, 0, 'F', '0', '0', 'articlecategory:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1090, '导入', 1078, 5, '#', NULL, 0, 0, 'F', '0', '0', 'system:lang:import', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1091, '批量强退', 111, 2, '', NULL, 0, 0, 'F', '0', '0', 'monitor:online:batchLogout', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '在线用户强退');
INSERT INTO `sys_menu` VALUES (1092, '单条强退', 111, 1, '', NULL, 0, 0, 'F', '0', '0', 'monitor:online:forceLogout', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '在线用户强退');
INSERT INTO `sys_menu` VALUES (1093, '广告管理', 0, 999, 'bannerConfig', 'public/BannerConfig', 0, 0, 'C', '1', '0', 'bannerconfig:list', 'app', NULL, NULL, '2024-12-10 10:40:31', 'admin', '2025-02-14 14:55:48', NULL);
INSERT INTO `sys_menu` VALUES (1094, '查询', 1093, 1, '#', NULL, 0, 0, 'F', '0', '0', 'bannerconfig:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1095, '新增', 1093, 2, '#', NULL, 0, 0, 'F', '0', '0', 'bannerconfig:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1096, '删除', 1093, 3, '#', NULL, 0, 0, 'F', '0', '0', 'bannerconfig:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1097, '修改', 1093, 4, '#', NULL, 0, 0, 'F', '0', '0', 'bannerconfig:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1098, '导出', 1093, 5, '#', NULL, 0, 0, 'F', '0', '0', 'bannerconfig:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1102, '删除', 502, 2, '', NULL, 0, 0, 'F', '0', '0', 'sqldifflog:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '审计日志删除');
INSERT INTO `sys_menu` VALUES (1103, '导出', 502, 3, '', NULL, 0, 0, 'F', '0', '0', 'sqldifflog:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '审计日志导出');
INSERT INTO `sys_menu` VALUES (1104, '邮件模板', 9, 1, 'email', 'tool/email/emailTpl', 0, 0, 'C', '0', '0', 'tool:emailtpl:list', 'icon1', 'menu.emailTemplate', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1105, '添加', 1104, 2, '', NULL, 0, 0, 'F', '0', '0', 'tool:emailtpl:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1106, '修改', 1104, 3, '', NULL, 0, 0, 'F', '0', '0', 'tool:emailtpl:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1107, '删除', 1104, 4, '', NULL, 0, 0, 'F', '0', '0', 'tool:emailtpl:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1108, '短信记录', 108, 1, 'smsLog', 'monitor/SmsLog', 0, 0, 'C', '0', '0', 'smscodelog:list', 'validCode', 'menu.smsLog', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1109, '短信删除', 1108, 2, '', NULL, 0, 0, 'F', '0', '0', 'smscodelog:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1110, '短信导出', 1108, 3, '', NULL, 0, 0, 'F', '0', '0', 'smscodelog:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1111, '邮件记录', 9, 1, 'emailLog', 'tool/email/emailLog', 0, 0, 'C', '0', '0', 'emaillog:list', 'emailLog', 'menu.emailLog', NULL, '2024-12-10 10:40:31', NULL, NULL, '邮件记录');
INSERT INTO `sys_menu` VALUES (1112, '邮件查询', 1111, 2, '', NULL, 0, 0, 'F', '0', '0', 'emaillog:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1113, '邮件删除', 1111, 3, '', NULL, 0, 0, 'F', '0', '0', 'emaillog:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1114, '用户在线日志', 2, 1, 'userOnlineLog', 'monitor/UserOnlineLog', 0, 0, 'C', '0', '0', 'useronlinelog:list', 'log', 'menu.userOnlineLog', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1115, '用户在线日志导出', 1114, 2, '', NULL, 0, 0, 'F', '0', '0', 'useronlinelog:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1116, '用户在线日志删除', 1114, 3, '', NULL, 0, 0, 'F', '0', '0', 'useronlinelog:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1117, '话题管理', 118, 999, 'topic', '	\narticle/articleTopic', 0, 0, 'C', '0', '0', 'articletopic:list', 'icon1', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1118, '查询', 1117, 1, '#', NULL, 0, 0, 'F', '0', '0', 'articletopic:query', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1119, '新增', 1117, 2, '#', NULL, 0, 0, 'F', '0', '0', 'articletopic:add', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1120, '删除', 1117, 3, '#', NULL, 0, 0, 'F', '0', '0', 'articletopic:delete', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1121, '修改', 1117, 4, '#', NULL, 0, 0, 'F', '0', '0', 'articletopic:edit', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1122, '导出', 1117, 5, '#', NULL, 0, 0, 'F', '0', '0', 'articletopic:export', '', NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1123, '功能名只是演示', 0, 999, 'Justdemo', 'business/Justdemo', 0, 0, 'C', '1', '0', 'justdemo:list', 'icon1', NULL, 'system', '2024-12-11 17:55:58', 'admin', '2025-02-14 14:55:45', NULL);
INSERT INTO `sys_menu` VALUES (1124, '查询', 1123, 1, '#', NULL, 0, 0, 'F', '0', '0', 'justdemo:query', '', NULL, 'system', '2024-12-11 17:55:58', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1125, '新增', 1123, 2, '#', NULL, 0, 0, 'F', '0', '0', 'justdemo:add', '', NULL, 'system', '2024-12-11 17:55:58', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1126, '删除', 1123, 3, '#', NULL, 0, 0, 'F', '0', '0', 'justdemo:delete', '', NULL, 'system', '2024-12-11 17:55:58', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1127, '修改', 1123, 4, '#', NULL, 0, 0, 'F', '0', '0', 'justdemo:edit', '', NULL, 'system', '2024-12-11 17:55:58', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1128, '导出', 1123, 5, '#', NULL, 0, 0, 'F', '0', '0', 'justdemo:export', '', NULL, 'system', '2024-12-11 17:55:58', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1142, 'RPA管理', 0, 0, '', NULL, 0, 0, 'M', '0', '0', NULL, 'pc', NULL, 'admin', '2025-02-12 11:32:29', 'admin', '2025-02-12 15:50:30', NULL);
INSERT INTO `sys_menu` VALUES (1143, '程序列表', 1142, 999, 'ExeProgramManager', 'RPA/ExeProgramManager', 0, 0, 'C', '0', '0', NULL, '', NULL, 'admin', '2025-02-12 11:33:08', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1144, '任务列表', 1142, 999, 'JobTaskManager', 'RPA/JobTaskManager', 0, 0, 'C', '0', '0', NULL, '', NULL, 'admin', '2025-02-12 11:33:38', 'admin', '2025-02-12 15:41:41', NULL);
INSERT INTO `sys_menu` VALUES (1146, '文件管理', 1142, 999, 'FileManagement', 'RPA/FileManagement', 0, 0, 'C', '0', '0', NULL, '', NULL, 'admin', '2025-02-12 15:42:57', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1147, '资源机管理', 1142, 999, 'ResourceMachineManagement', 'RPA/ResourceMachineManagement', 0, 0, 'C', '0', '0', NULL, '', NULL, 'admin', '2025-02-12 15:43:22', 'admin', '2025-02-12 15:44:53', NULL);
INSERT INTO `sys_menu` VALUES (1148, '资源池管理', 1142, 999, 'ResourcePoolManager', 'RPA/ResourcePoolManager', 0, 0, 'C', '0', '0', NULL, '', NULL, 'admin', '2025-02-12 15:43:43', NULL, NULL, NULL);
INSERT INTO `sys_menu` VALUES (1149, '凭证管理', 1142, 999, 'RpaCredentialManager', 'RPA/RpaCredentialManager', 0, 0, 'C', '0', '0', NULL, '', NULL, 'admin', '2025-02-12 15:45:21', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` int NOT NULL COMMENT '公告类型 (1通知 2公告)',
  `notice_content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '公告内容',
  `Status` int NOT NULL DEFAULT 0 COMMENT '公告状态 (0正常 1关闭)',
  `Publisher` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发布人',
  `BeginTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `EndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `Popup` int NULL DEFAULT NULL COMMENT '弹出提示',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '首页公告', 2, NULL, 0, NULL, NULL, NULL, 0, NULL, '2024-12-10 10:43:31', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `OperId` bigint NOT NULL AUTO_INCREMENT COMMENT '操作id',
  `Title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作模块',
  `BusinessType` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除 4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据）',
  `Method` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求方法',
  `RequestMethod` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求方式',
  `OperatorType` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `OperName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人员',
  `OperUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求url',
  `OperIp` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作地址',
  `OperLocation` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作地点',
  `OperParam` varchar(4000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求参数',
  `JsonResult` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '返回参数',
  `Status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `ErrorMsg` varchar(4000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '错误消息',
  `OperTime` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `Elapsed` bigint NULL DEFAULT NULL COMMENT '操作用时',
  `DeptName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`OperId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 207 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (1, '初始化数据', 1, NULL, 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0 内网IP', '', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Incorrect string value: \'\\xF0\\x9F\\x91\\x89<a...\' for column \'notice_content\' at row 1', '2024-12-10 10:40:44', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (2, '初始化数据', 1, NULL, 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0 内网IP', '', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Incorrect string value: \'\\xF0\\x9F\\x91\\x89<a...\' for column \'notice_content\' at row 1', '2024-12-10 10:42:29', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (3, '初始化数据', 1, NULL, 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0 内网IP', '', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Incorrect string value: \'\\xF0\\x9F\\x91\\x89<a...\' for column \'notice_content\' at row 1', '2024-12-10 10:43:25', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (4, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入1 更新0 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:43:32', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (5, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:41', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (6, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:42', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (7, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:43', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (8, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:44', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (9, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:44', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (10, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:44', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (11, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:45', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (12, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:45', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (13, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:46', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (14, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:46', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (15, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:47', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (16, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:47', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (17, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:47', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (18, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:48', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (19, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:48', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (20, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:49', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (21, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:49', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (22, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:49', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (23, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:50', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (24, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:51', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (25, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:52', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (26, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:52', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (27, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:52', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (28, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:53', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (29, '初始化数据', 1, 'Common.InitSeedData()', 'GET', 0, NULL, '/common/initseedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": [      \"[用户数据] 插入0 错误0 总共3\",      \"[岗位数据] 插入0 错误0 总共13\",      \"[角色数据] 插入0 错误0 总共3\",      \"[用户角色] 插入0 错误0 总共3\",      \"[菜单数据] 插入0 错误0 总共143\",      \"[系统配置] 插入0 错误0 总共5\",      \"[角色菜单] 插入0 错误0 总共38\",      \"[字典管理] 插入0 错误0 总共18\",      \"[字典数据] 插入0 更新47 错误0 总共47\",      \"[部门数据] 插入0 错误0 总共5\",      \"[文章目录] 插入0 更新5 错误0 总共5\",      \"[文章话题] 插入0 更新5 错误0 总共5\",      \"[通知公告数据] 插入0 更新1 错误0 总共1\"    ]  }}', 0, NULL, '2024-12-10 10:55:54', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (30, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"code\":\"s6nk\",\"uuid\":\"50069f947d2e470fa9a64fd67c941407\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 103,  \"msg\": \"验证码错误\"}', 0, NULL, '2024-12-10 10:59:56', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (31, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"code\":\"po9qob\",\"uuid\":\"8c49af6f295645aba04d005882f2e523\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 103,  \"msg\": \"验证码错误\"}', 0, NULL, '2024-12-10 11:00:03', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (32, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"code\":\"8upx\",\"uuid\":\"7ccda090d04b40d8863d5285d9683e96\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzM3OTk2MDYsImV4cCI6MTczMzg4NjAwNiwiaWF0IjoxNzMzNzk5NjA2LCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.RhoglIoC3m_IDe3Dsq72EEREE9W3SDX7YJaS--xPdWE\"}', 0, NULL, '2024-12-10 11:00:07', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (33, '代码生成', 6, 'CodeGenerator.ImportTableSave()', 'POST', 0, 'admin', '/tool/gen/importTable', '127.0.0.1', '0-内网IP-内网IP', '{\"tables\":[{\"name\":\"banner_config\",\"description\":\"广告管理\",\"dbObjectType\":0}],\"dbName\":\"zradmin\",\"frontTpl\":2}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-10 14:08:54', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (34, '代码生成', 8, 'CodeGenerator.CodeGenerate()', 'POST', 0, 'admin', '/tool/gen/genCode', '127.0.0.1', '0-内网IP-内网IP', '{\"tableId\":1,\"VueVersion\":3}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"path\": \"/Generatecode/ZrAdmin.NET-banner_config-**********.zip\",    \"fileName\": null  }}', 0, NULL, '2024-12-10 14:09:01', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (35, '下载文件', 0, 'Common.DownloadFile()', 'GET', 0, 'admin', '/common/downloadFile', '127.0.0.1', '0-内网IP-内网IP', '?path=%2FGeneratecode%2FZrAdmin.NET-banner_config-**********.zip', '', 0, NULL, '2024-12-10 14:09:01', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (36, '参数配置修改', 2, 'SysConfig.UpdateSysConfig()', 'PUT', 0, 'admin', '/system/config', '127.0.0.1', '0-内网IP-内网IP', '{\"configId\":2,\"configName\":\"账号自助-验证码开关\",\"configKey\":\"sys.account.captchaOnOff\",\"configValue\":\"off\",\"configType\":\"Y\",\"createTime\":\"2024-12-10 10:40:31\",\"remark\":\"开启验证码功能（off、关闭，1、动态验证码 2、动态gif泡泡 3、泡泡 4、静态验证码）\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": 1}', 0, NULL, '2024-12-10 16:43:57', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (37, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2024-12-10 16:44:06', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (38, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"371fc91924d74e589a75d202deda782d\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzM4MjAyNjYsImV4cCI6MTczMzkwNjY2NiwiaWF0IjoxNzMzODIwMjY2LCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.vtNda8ZK5F2SYRGD7PmRQa2cSPCe7IEYKBFJlCk9af8\"}', 0, NULL, '2024-12-10 16:44:26', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (39, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2024-12-10 16:46:22', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (40, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"bcfdc3d8250143a2be414eb06afd32ea\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9qoDTcspZ6u_B8ZofelJ1GOeayFdzT0_z4fuX9ZP5UQ\"}', 0, NULL, '2024-12-10 17:03:51', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (41, '参数配置修改', 2, 'SysConfig.UpdateSysConfig()', 'PUT', 0, 'admin', '/system/config', '127.0.0.1', '0-内网IP-内网IP', '{\"configId\":3,\"configName\":\"开启注册功能\",\"configKey\":\"sys.account.register\",\"configValue\":\"False\",\"configType\":\"Y\",\"createTime\":\"2024-12-10 10:40:31\",\"remark\":\"注册开关\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": 1}', 0, NULL, '2024-12-10 17:04:34', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (42, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2024-12-10 17:06:00', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (43, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"e864a127f805410c8153998b5fd4e48e\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzM4MjE1NzMsImV4cCI6MTczMzkwNzk3MywiaWF0IjoxNzMzODIxNTczLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.d6vkJHY38PNyHfVCX0_hz8IsSbkS4lG3bcPQaCZnLeI\"}', 0, NULL, '2024-12-10 17:06:13', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (44, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2024-12-10 17:08:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (45, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"b8b20821fcf84f188bf06cc512e1e135\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzM4MjE3MzgsImV4cCI6MTczMzkwODEzOCwiaWF0IjoxNzMzODIxNzM4LCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.nbVudhX1YQUCY-KolneuzXxuGd4SjQkNAEJDEhMWMD4\"}', 0, NULL, '2024-12-10 17:08:59', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (46, '代码生成', 8, 'CodeGenerator.EditSave()', 'PUT', 0, 'admin', '/tool/gen/', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": true}', 0, NULL, '2024-12-11 16:12:01', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (47, '代码生成', 6, 'CodeGenerator.ImportTableSave()', 'POST', 0, 'admin', '/tool/gen/importTable', '127.0.0.1', '0-内网IP-内网IP', '{\"tables\":[{\"name\":\"justdemo\",\"description\":\"\",\"dbObjectType\":0}],\"dbName\":\"zradmin\",\"frontTpl\":2}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-11 16:47:59', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (48, '代码生成', 3, 'CodeGenerator.Remove()', 'DELETE', 0, 'admin', '/tool/gen/2', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": 3}', 0, NULL, '2024-12-11 16:49:11', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (49, '代码生成', 6, 'CodeGenerator.ImportTableSave()', 'POST', 0, 'admin', '/tool/gen/importTable', '127.0.0.1', '0-内网IP-内网IP', '{\"tables\":[{\"name\":\"justdemo\",\"description\":\"\",\"dbObjectType\":0}],\"dbName\":\"zradmin\",\"frontTpl\":2}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-11 16:50:51', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (50, '代码生成', 8, 'CodeGenerator.EditSave()', 'PUT', 0, 'admin', '/tool/gen/', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": true}', 0, NULL, '2024-12-11 16:56:08', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (51, '代码生成', 2, 'CodeGenerator.SynchDb()', 'GET', 0, 'admin', '/tool/gen/synchDb/3', '127.0.0.1', '0-内网IP-内网IP', '?tableName=justdemo&dbName=zradmin', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": true}', 0, NULL, '2024-12-11 16:56:16', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (52, '代码生成', 8, 'CodeGenerator.CodeGenerate()', 'POST', 0, 'admin', '/tool/gen/genCode', '127.0.0.1', '0-内网IP-内网IP', '{\"tableId\":3,\"VueVersion\":3}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"path\": \"/Generatecode/ZrAdmin.NET-justdemo-1211165637.zip\",    \"fileName\": null  }}', 0, NULL, '2024-12-11 16:56:38', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (53, '下载文件', 0, 'Common.DownloadFile()', 'GET', 0, 'admin', '/common/downloadFile', '127.0.0.1', '0-内网IP-内网IP', '?path=%2FGeneratecode%2FZrAdmin.NET-justdemo-1211165637.zip', '', 0, NULL, '2024-12-11 16:56:38', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (54, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, '', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2024-12-11 17:57:08', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (55, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, NULL, '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2024-12-11 17:57:11', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (56, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"fd0fd645052a467290a9a4da573c08d5\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzM5MTEwMzYsImV4cCI6MTczMzk5NzQzNiwiaWF0IjoxNzMzOTExMDM2LCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.rEQjTPtaRvp_rEbRcWTfNz1nnzluXQQ5jfGBuwzq1aU\"}', 0, NULL, '2024-12-11 17:57:16', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (57, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":1,\"demoName\":\"2\",\"demoRemark\":\"3\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 1,    \"demoName\": \"2\",    \"demoRemark\": \"3\"  }}', 0, NULL, '2024-12-11 17:57:58', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (58, '功能名只是演示', 2, 'Justdemo.UpdateJustdemo()', 'PUT', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":1,\"demoName\":\"***********\",\"demoRemark\":\"333333\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-11 17:58:06', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (59, '功能名只是演示', 3, 'Justdemo.DeleteJustdemo()', 'POST', 0, 'admin', '/business/Justdemo/delete/1', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-11 17:58:10', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (60, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":1,\"demoName\":\"2\",\"demoRemark\":\"3\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 1,    \"demoName\": \"2\",    \"demoRemark\": \"3\"  }}', 0, NULL, '2024-12-11 17:58:26', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (61, '功能名只是演示', 1, NULL, 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0 内网IP', '{\"id\":1,\"demoName\":\"2\",\"demoRemark\":\"3\"}', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Duplicate entry \'1\' for key \'justdemo.PRIMARY\'', '2024-12-11 17:58:32', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (62, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":2,\"demoName\":\"3\",\"demoRemark\":\"4\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 2,    \"demoName\": \"3\",    \"demoRemark\": \"4\"  }}', 0, NULL, '2024-12-11 17:58:59', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (63, '功能名只是演示', 5, 'Justdemo.Export()', 'GET', 0, 'admin', '/business/Justdemo/export', '127.0.0.1', '0-内网IP-内网IP', '?pageNum=1&pageSize=10&sort=&sortType=asc', '', 0, NULL, '2024-12-11 17:59:04', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (64, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":5}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 5,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:16', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (65, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":7}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 7,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:19', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (66, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":4}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 4,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:24', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (67, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":10}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 10,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:34', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (68, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":11}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 11,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:43', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (69, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":12}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 12,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:47', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (70, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":13}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 13,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:49', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (71, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":14}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 14,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:53', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (72, '功能名只是演示', 1, 'Justdemo.AddJustdemo()', 'POST', 0, 'admin', '/business/Justdemo', '127.0.0.1', '0-内网IP-内网IP', '{\"id\":15}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 15,    \"demoName\": null,    \"demoRemark\": null  }}', 0, NULL, '2024-12-11 17:59:57', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (73, '功能名只是演示', 5, 'Justdemo.Export()', 'GET', 0, 'admin', '/business/Justdemo/export', '127.0.0.1', '0-内网IP-内网IP', '?pageNum=1&pageSize=10&sort=&sortType=asc', '', 0, NULL, '2024-12-11 18:00:23', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (74, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, '', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2024-12-23 15:22:24', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (75, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, NULL, '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2024-12-23 15:22:25', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (76, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"ce487b7e62c44e4f86fee67aab6c5aa5\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzQ5Mzg1NTMsImV4cCI6MTczNTAyNDk1MywiaWF0IjoxNzM0OTM4NTUzLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.L0os3ImVGtAUWqaenkndeEyxJwtGkoyR6QE0KYLx4VY\"}', 0, NULL, '2024-12-23 15:22:33', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (77, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1093,\"menuName\":\"广告管理\",\"parentId\":0,\"orderNum\":999,\"path\":\"bannerConfig\",\"component\":\"public/BannerConfig\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"perms\":\"bannerconfig:list\",\"icon\":\"app\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-23 15:30:17', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (78, '广告管理', 1, 'BannerConfig.AddBannerConfig()', 'POST', 0, 'admin', '/public/BannerConfig', '127.0.0.1', '0-内网IP-内网IP', '{\"title\":\"123\",\"content\":\"123123\",\"link\":\"123\",\"jumpType\":0,\"showStatus\":1,\"adType\":1,\"beginTime\":\"2024-12-22T16:00:00.000Z\",\"endTime\":\"2024-12-22T16:00:00.000Z\",\"sortId\":0}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"id\": 1,    \"title\": \"123\",    \"content\": \"123123\",    \"link\": \"123\",    \"imgUrl\": null,    \"jumpType\": 0,    \"addTime\": \"2024-12-23 15:31:02\",    \"clicksNumber\": 0,    \"showStatus\": 1,    \"adType\": 1,    \"beginTime\": \"2024-12-22 16:00:00\",    \"endTime\": \"2024-12-22 16:00:00\",    \"sortId\": 0  }}', 0, NULL, '2024-12-23 15:31:03', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (79, '广告管理', 3, 'BannerConfig.DeleteBannerConfig()', 'DELETE', 0, 'admin', '/public/BannerConfig/delete/1', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-23 15:31:15', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (80, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":5,\"menuName\":\"外部打开111\",\"parentId\":0,\"orderNum\":5,\"path\":\"http://www.izhaorui.cn\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"link\",\"menuNameKey\":\"menu.officialWebsite\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-23 15:33:00', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (81, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":5,\"menuName\":\"外部打开111\",\"parentId\":0,\"orderNum\":5,\"path\":\"http://www.baidu.com\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"link\",\"menuNameKey\":\"menu.officialWebsite\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\",\"updateTime\":\"2024-12-23 15:33:00\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-23 15:33:58', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (82, '菜单管理', 3, 'SysMenu.RemoveAll()', 'DELETE', 0, 'admin', '/system/Menu/deleteAll/5', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-23 15:34:25', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (83, '发送通知公告', 0, 'SysNotice.SendNotice()', 'PUT', 0, 'admin', '/system/notice/send/1', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"noticeId\": 1,    \"noticeTitle\": \"首页公告\",    \"noticeType\": 2,    \"noticeContent\": null,    \"status\": 0,    \"publisher\": null,    \"beginTime\": null,    \"endTime\": null,    \"popup\": 0,    \"createBy\": null,    \"createTime\": \"2024-12-10 10:43:31\",    \"updateTime\": null,    \"remark\": null  }}', 0, NULL, '2024-12-23 15:53:54', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (84, '发送通知公告', 0, 'SysNotice.SendNotice()', 'PUT', 0, 'admin', '/system/notice/send/1', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"noticeId\": 1,    \"noticeTitle\": \"首页公告\",    \"noticeType\": 2,    \"noticeContent\": null,    \"status\": 0,    \"publisher\": null,    \"beginTime\": null,    \"endTime\": null,    \"popup\": 0,    \"createBy\": null,    \"createTime\": \"2024-12-10 10:43:31\",    \"updateTime\": null,    \"remark\": null  }}', 0, NULL, '2024-12-23 15:54:01', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (85, '发送通知公告', 0, 'SysNotice.SendNotice()', 'PUT', 0, 'admin', '/system/notice/send/1', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"noticeId\": 1,    \"noticeTitle\": \"首页公告\",    \"noticeType\": 2,    \"noticeContent\": null,    \"status\": 0,    \"publisher\": null,    \"beginTime\": null,    \"endTime\": null,    \"popup\": 0,    \"createBy\": null,    \"createTime\": \"2024-12-10 10:43:31\",    \"updateTime\": null,    \"remark\": null  }}', 0, NULL, '2024-12-23 15:54:07', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (86, '发送通知公告', 0, 'SysNotice.SendNotice()', 'PUT', 0, 'admin', '/system/notice/send/1', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"noticeId\": 1,    \"noticeTitle\": \"首页公告\",    \"noticeType\": 2,    \"noticeContent\": null,    \"status\": 0,    \"publisher\": null,    \"beginTime\": null,    \"endTime\": null,    \"popup\": 0,    \"createBy\": null,    \"createTime\": \"2024-12-10 10:43:31\",    \"updateTime\": null,    \"remark\": null  }}', 0, NULL, '2024-12-23 15:54:27', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (87, '代码生成', 6, 'CodeGenerator.ImportTableSave()', 'POST', 0, 'admin', '/tool/gen/importTable', '127.0.0.1', '0-内网IP-内网IP', '{\"tables\":[{\"name\":\"testtable\",\"description\":\"\",\"dbObjectType\":0}],\"dbName\":\"rpasystem\",\"frontTpl\":2}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2024-12-23 16:04:03', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (88, '字典操作', 1, 'SysDictType.Add()', 'POST', 0, 'admin', '/system/dict/type/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"dictName\":\"RPA运行类型\",\"dictType\":\"rpa_run_type\",\"status\":\"0\",\"type\":\"N\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": 19}', 0, NULL, '2024-12-23 16:08:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (89, '代码生成', 8, 'CodeGenerator.EditSave()', 'PUT', 0, 'admin', '/tool/gen/', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": true}', 0, NULL, '2024-12-23 16:10:34', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (90, '代码生成', 8, 'CodeGenerator.CodeGenerate()', 'POST', 0, 'admin', '/tool/gen/genCode', '127.0.0.1', '0-内网IP-内网IP', '{\"tableId\":4,\"VueVersion\":3}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"path\": \"/Generatecode/ZrAdmin.NET-testtable-1223161510.zip\",    \"fileName\": null  }}', 0, NULL, '2024-12-23 16:15:11', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (91, '下载文件', 0, 'Common.DownloadFile()', 'GET', 0, 'admin', '/common/downloadFile', '127.0.0.1', '0-内网IP-内网IP', '?path=%2FGeneratecode%2FZrAdmin.NET-testtable-1223161510.zip', '', 0, NULL, '2024-12-23 16:15:11', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (92, NULL, 0, NULL, 'GET', 0, 'admin', '/business/Testtable/list', '127.0.0.1', '0 内网IP', '?pageNum=1&pageSize=10&sort=Id&sortType=desc', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Table \'zradmin.testtable\' doesn\'t exist', '2024-12-23 16:17:49', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (93, NULL, 0, NULL, 'GET', 0, 'admin', '/business/Testtable/list', '127.0.0.1', '0 内网IP', '?pageNum=1&pageSize=10&sort=Id&sortType=desc', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Table \'zradmin.testtable\' doesn\'t exist', '2024-12-23 16:17:58', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (94, '字典数据', 1, 'SysDictData.Add()', 'POST', 0, 'admin', '/system/dict/data', '127.0.0.1', '0-内网IP-内网IP', '{\"dictLabel\":\"待运行\",\"dictValue\":\"0\",\"dictSort\":0,\"status\":\"0\",\"dictType\":\"rpa_run_type\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": 48}', 0, NULL, '2024-12-23 16:20:07', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (95, '字典数据', 1, 'SysDictData.Add()', 'POST', 0, 'admin', '/system/dict/data', '127.0.0.1', '0-内网IP-内网IP', '{\"dictLabel\":\"运行中\",\"dictValue\":\"1\",\"dictSort\":0,\"status\":\"0\",\"dictType\":\"rpa_run_type\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": 49}', 0, NULL, '2024-12-23 16:21:07', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (96, '只是测试', 1, 'Testtable.AddTesttable()', 'POST', 0, 'admin', '/business/Testtable', '127.0.0.1', '0-内网IP-内网IP', '{\"name\":\"111\",\"myTypeChecked\":[\"0\"],\"myType\":\"0\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2024-12-23 16:21:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (97, '代码生成', 8, 'CodeGenerator.EditSave()', 'PUT', 0, 'admin', '/tool/gen/', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": true}', 0, NULL, '2024-12-23 16:23:07', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (98, '代码生成', 8, 'CodeGenerator.CodeGenerate()', 'POST', 0, 'admin', '/tool/gen/genCode', '127.0.0.1', '0-内网IP-内网IP', '{\"tableId\":4,\"VueVersion\":3}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"path\": \"/Generatecode/ZrAdmin.NET-testtable-1223164158.zip\",    \"fileName\": null  }}', 0, NULL, '2024-12-23 16:41:58', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (99, '下载文件', 0, 'Common.DownloadFile()', 'GET', 0, 'admin', '/common/downloadFile', '127.0.0.1', '0-内网IP-内网IP', '?path=%2FGeneratecode%2FZrAdmin.NET-testtable-1223164158.zip', '', 0, NULL, '2024-12-23 16:41:59', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (100, '只是测试', 1, NULL, 'POST', 0, 'admin', '/business/Testtable', '127.0.0.1', '0 内网IP', '{\"name\":\"121111\",\"myTypeChecked\":[\"0\"],\"myType\":\"0\"}', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, '中文提示 : 自增不是能string,如何非要用可以程序启动配置：StaticConfig.Check_StringIdentity=false\r\nEnglish Message : Auto-incremented is not a string, how can I use a executable startup configuration: StaticConfig.Check_StringIdentity=false ', '2024-12-23 16:42:57', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (101, '只是测试', 1, NULL, 'POST', 0, 'admin', '/business/Testtable', '127.0.0.1', '0 内网IP', '{\"name\":\"121111\",\"myTypeChecked\":[\"1\"],\"myType\":\"1\"}', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, '中文提示 : 自增不是能string,如何非要用可以程序启动配置：StaticConfig.Check_StringIdentity=false\r\nEnglish Message : Auto-incremented is not a string, how can I use a executable startup configuration: StaticConfig.Check_StringIdentity=false ', '2024-12-23 16:43:17', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (102, '代码生成', 8, 'CodeGenerator.EditSave()', 'PUT', 0, 'admin', '/tool/gen/', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": true}', 0, NULL, '2024-12-23 16:44:10', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (103, '代码生成', 8, 'CodeGenerator.CodeGenerate()', 'POST', 0, 'admin', '/tool/gen/genCode', '127.0.0.1', '0-内网IP-内网IP', '{\"tableId\":4,\"VueVersion\":3}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"path\": \"/Generatecode/ZrAdmin.NET-testtable-1223164538.zip\",    \"fileName\": null  }}', 0, NULL, '2024-12-23 16:45:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (104, '下载文件', 0, 'Common.DownloadFile()', 'GET', 0, 'admin', '/common/downloadFile', '127.0.0.1', '0-内网IP-内网IP', '?path=%2FGeneratecode%2FZrAdmin.NET-testtable-1223164538.zip', '', 0, NULL, '2024-12-23 16:45:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (105, '只是测试', 1, NULL, 'POST', 0, 'admin', '/business/Testtable', '127.0.0.1', '0 内网IP', '{\"name\":\"1111\",\"myTypeChecked\":[\"1\"],\"myType\":\"1\"}', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, '中文提示 : 自增不是能string,如何非要用可以程序启动配置：StaticConfig.Check_StringIdentity=false\r\nEnglish Message : Auto-incremented is not a string, how can I use a executable startup configuration: StaticConfig.Check_StringIdentity=false ', '2024-12-23 16:46:32', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (106, '只是测试', 1, NULL, 'POST', 0, 'admin', '/business/Testtable', '127.0.0.1', '0 内网IP', '{\"name\":\"111\",\"myTypeChecked\":[\"1\"],\"myType\":\"1\"}', '{\r\n  \"code\": 101,\r\n  \"msg\": \"Object of type \'System.Int32\' cannot be converted to type \'System.Nullable`1[System.Int64]\'.\"\r\n}', 1, 'Object of type \'System.Int32\' cannot be converted to type \'System.Nullable`1[System.Int64]\'.', '2024-12-23 16:47:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (107, '只是测试', 1, NULL, 'POST', 0, 'admin', '/business/Testtable', '127.0.0.1', '0 内网IP', '{\"name\":\"111\",\"myTypeChecked\":[\"1\"],\"myType\":\"1\"}', '{\r\n  \"code\": 101,\r\n  \"msg\": \"Object of type \'System.Int32\' cannot be converted to type \'System.Nullable`1[System.Int64]\'.\"\r\n}', 1, 'Object of type \'System.Int32\' cannot be converted to type \'System.Nullable`1[System.Int64]\'.', '2024-12-23 16:47:45', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (108, '代码生成', 8, 'CodeGenerator.EditSave()', 'PUT', 0, 'admin', '/tool/gen/', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": true}', 0, NULL, '2024-12-23 16:49:45', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (109, '代码生成', 8, 'CodeGenerator.CodeGenerate()', 'POST', 0, 'admin', '/tool/gen/genCode', '127.0.0.1', '0-内网IP-内网IP', '{\"tableId\":4,\"VueVersion\":3}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"path\": \"/Generatecode/ZrAdmin.NET-testtable-1223165012.zip\",    \"fileName\": null  }}', 0, NULL, '2024-12-23 16:50:13', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (110, '下载文件', 0, 'Common.DownloadFile()', 'GET', 0, 'admin', '/common/downloadFile', '127.0.0.1', '0-内网IP-内网IP', '?path=%2FGeneratecode%2FZrAdmin.NET-testtable-1223165012.zip', '', 0, NULL, '2024-12-23 16:50:13', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (111, NULL, 0, NULL, 'GET', 0, 'admin', '/business/Testtable/list', '127.0.0.1', '0 内网IP', '?pageNum=1&pageSize=10&sort=Id&sortType=desc', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Attempted to access a missing method.', '2024-12-23 16:51:07', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (112, NULL, 0, NULL, 'GET', 0, 'admin', '/business/Testtable/list', '127.0.0.1', '0 内网IP', '?pageNum=1&pageSize=10&sort=Id&sortType=desc', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Attempted to access a missing method.', '2024-12-23 16:51:35', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (113, NULL, 0, NULL, 'GET', 0, 'admin', '/business/Testtable/list', '127.0.0.1', '0 内网IP', '?pageNum=1&pageSize=10&sort=Id&sortType=desc', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'MyType can\'t  convert string to int32', '2024-12-23 16:51:58', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (114, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, '', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2025-02-06 17:37:38', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (115, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, NULL, '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2025-02-06 17:37:40', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (116, '登录', 0, NULL, 'POST', 0, NULL, '/login', '127.0.0.1', '0 内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"566f8a242b9c41bcbc6f9ab65b323585\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{\r\n  \"code\": 105,\r\n  \"msg\": \"用户名或密码错误\"\r\n}', 1, '用户名或密码错误', '2025-02-06 17:38:30', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (117, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"be33c94ddd254de18b0cbcffc96be387\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3Mzg4MzQ3MTMsImV4cCI6MTczODkyMTExMywiaWF0IjoxNzM4ODM0NzEzLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.1Cg_xE0VZCeUKmw2mnunOGkY06sP9H_QJj2C5odHeBQ\"}', 0, NULL, '2025-02-06 17:38:33', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (118, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, '', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2025-02-07 18:36:08', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (119, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"a2d013c3a3784e4b9feee84a298b8001\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-07 18:36:14', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (120, '登录', 0, 'SysLogin.Login()', 'POST', 0, '123', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"123\",\"password\":\"***\",\"uuid\":\"6e02a2fc742a4c9982744e3ce8538f74\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-07 18:38:17', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (121, '登录', 0, 'SysLogin.Login()', 'POST', 0, '123', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"123\",\"password\":\"***\",\"uuid\":\"fc2275f9298a403ca2440489d627abaf\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-07 18:38:22', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (122, '登录', 0, 'SysLogin.Login()', 'POST', 0, '123', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"123\",\"password\":\"***\",\"uuid\":\"02995c753e0b4b18a0899d0046a9d78a\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-07 18:38:35', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (123, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"14937d4245c8405793105eadeb449e81\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-07 18:38:49', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (124, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"6290de9b98bc40f78b64f4c20403f322\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-07 18:38:52', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (125, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"c88ffabce66f467bb7209d7cc59dddf2\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-07 18:38:55', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (126, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"825585dac4654b7aadb9ced8762388bd\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-07 18:38:58', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (127, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"d45e9b431c7045a3ae59bbabe30c085b\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-08 09:29:00', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (128, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"c1619dc15d2542748a0c656e3d1a5074\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-08 14:43:11', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (129, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"627bf57b0b514086b4192d5cbdcf18c3\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3Mzg5OTczNDgsImV4cCI6MTczOTA4Mzc0OCwiaWF0IjoxNzM4OTk3MzQ4LCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.QWimgPa7mGNqisns2QYxO2FmusdgnpBp5-hJVZC78rY\"}', 0, NULL, '2025-02-08 14:49:08', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (130, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2025-02-08 14:51:44', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (131, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"2b5ea78725874f18abb2fb42ef9591e1\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', 'Infrastructure.Model.ApiResult', 0, NULL, '2025-02-08 14:51:49', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (132, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"b4a85918cdbe46c28307e975ec18f1b4\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3Mzg5OTc1NjIsImV4cCI6MTczOTA4Mzk2MiwiaWF0IjoxNzM4OTk3NTYyLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.kdA-FgpGhzFaewR4UoL_h43ZboV9z-lGD9EG8uQCM0I\"}', 0, NULL, '2025-02-08 14:52:42', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (133, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1129,\"menuName\":\"只是测试\",\"parentId\":0,\"orderNum\":999,\"path\":\"ExeProgramManager\",\"component\":\"RPA/ExeProgramManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"perms\":\"testtable:list\",\"icon\":\"icon1\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"system\",\"createTime\":\"2024-12-23 16:17:32\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 15:52:05', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (134, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1129,\"menuName\":\"只是测试\",\"parentId\":0,\"orderNum\":999,\"path\":\"ExeProgramManager\",\"component\":\"RPA/ExeProgramManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"perms\":\"ExeProgramManager:list\",\"icon\":\"icon1\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"system\",\"createTime\":\"2024-12-23 16:17:32\",\"updateTime\":\"2025-02-08 15:52:05\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 15:53:15', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (135, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1129,\"menuName\":\"只是测试\",\"parentId\":0,\"orderNum\":0,\"path\":\"ExeProgramManager\",\"component\":\"RPA/ExeProgramManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"perms\":\"ExeProgramManager:list\",\"icon\":\"icon1\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"system\",\"createTime\":\"2024-12-23 16:17:32\",\"updateTime\":\"2025-02-08 15:53:15\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 17:59:04', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (136, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":6,\"menuName\":\"控制台\",\"parentId\":0,\"orderNum\":0,\"path\":\"dashboard\",\"component\":\"index_v1\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"1\",\"status\":\"0\",\"icon\":\"dashboard\",\"menuNameKey\":\"menu.dashboard\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 17:59:49', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (137, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":10,\"menuName\":\"数据大屏\",\"parentId\":0,\"orderNum\":0,\"path\":\"dataScreen\",\"component\":\"dataScreen/index\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"1\",\"status\":\"0\",\"perms\":\"system:datascreen:list\",\"icon\":\"server\",\"menuNameKey\":\"menu.dataScreen\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 17:59:54', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (138, '菜单管理', 3, 'SysMenu.RemoveAll()', 'DELETE', 0, 'admin', '/system/Menu/deleteAll/1134', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:00:11', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (139, '菜单管理', 3, 'SysMenu.RemoveAll()', 'DELETE', 0, 'admin', '/system/Menu/deleteAll/1130', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:00:15', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (140, '菜单管理', 3, 'SysMenu.RemoveAll()', 'DELETE', 0, 'admin', '/system/Menu/deleteAll/1131', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:00:17', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (141, '菜单管理', 3, 'SysMenu.RemoveAll()', 'DELETE', 0, 'admin', '/system/Menu/deleteAll/1132', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:00:19', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (142, '菜单管理', 3, 'SysMenu.Remove()', 'DELETE', 0, 'admin', '/system/Menu/1133', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:00:25', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (143, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1129,\"menuName\":\"程序管理\",\"parentId\":0,\"orderNum\":0,\"path\":\"ExeProgramManager\",\"component\":\"RPA/ExeProgramManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"perms\":\"ExeProgramManager:list\",\"icon\":\"icon1\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"system\",\"createTime\":\"2024-12-23 16:17:32\",\"updateTime\":\"2025-02-08 17:59:04\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:00:38', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (144, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1129,\"menuName\":\"程序管理\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"component\":\"RPA/ExeProgramManager\",\"path\":\"ExeProgramManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:03:22', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (145, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1129,\"menuName\":\"程序管理\",\"parentId\":0,\"orderNum\":0,\"path\":\"ExeProgramManager\",\"component\":\"RPA/ExeProgramManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"perms\":\"ExeProgramManager:list\",\"icon\":\"icon1\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"system\",\"createTime\":\"2024-12-23 16:17:32\",\"updateTime\":\"2025-02-08 18:00:38\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:03:55', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (146, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1129,\"menuName\":\"程序管理\",\"parentId\":0,\"orderNum\":0,\"path\":\"ExeProgramManager\",\"component\":\"\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"perms\":\"ExeProgramManager:list\",\"icon\":\"icon1\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"system\",\"createTime\":\"2024-12-23 16:17:32\",\"updateTime\":\"2025-02-08 18:03:55\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:07:06', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (147, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1129,\"menuName\":\"程序管理\",\"parentId\":0,\"orderNum\":0,\"path\":\"ExeProgramManager\",\"component\":\"\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"perms\":\"\",\"icon\":\"build\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"system\",\"createTime\":\"2024-12-23 16:17:32\",\"updateTime\":\"2025-02-08 18:07:06\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:07:57', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (148, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1129,\"menuName\":\"RPA管理\",\"menuType\":\"M\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"component\":\"RPA/ExeProgramManager\",\"path\":\"ExeProgramManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-08 18:08:24', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (149, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, '', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2025-02-10 14:24:09', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (150, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, NULL, '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2025-02-10 14:24:15', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (151, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"21875a10304d43b48fb3ea268f44e781\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzkxNjg2NjEsImV4cCI6MTczOTI1NTA2MSwiaWF0IjoxNzM5MTY4NjYxLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.FkVbLwgCVRocYNFhFfXaNhwtxiqZkg76ygr7lFTfSrA\"}', 0, NULL, '2025-02-10 14:24:21', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (152, '修改用户状态', 2, 'SysUser.ChangeStatus()', 'PUT', 0, 'admin', '/system/user/changeStatus', '127.0.0.1', '0-内网IP-内网IP', '{\"userId\":3,\"status\":1}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-10 14:41:23', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (153, '修改用户状态', 2, 'SysUser.ChangeStatus()', 'PUT', 0, 'admin', '/system/user/changeStatus', '127.0.0.1', '0-内网IP-内网IP', '{\"userId\":3,\"status\":0}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-10 14:41:32', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (154, '初始化数据', 1, 'Common.UpdateSeedData()', 'GET', 0, NULL, '/Common/UpdateSeedData', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"result\": {      \"item1\": \"[通知公告数据] 插入0 更新1 错误0 总共1\",      \"item2\": [],      \"item3\": []    },    \"result5\": {      \"item1\": \"[菜单数据] 插入0 错误0 总共19\",      \"item2\": [],      \"item3\": [        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1104,            \"menuName\": \"邮件模板\",            \"parentId\": 9,            \"orderNum\": 1,            \"path\": \"email\",            \"component\": \"tool/email/emailTpl\",            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"C\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"tool:emailtpl:list\",            \"icon\": \"icon1\",            \"menuNameKey\": \"menu.emailTemplate\",            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1105,            \"menuName\": \"添加\",            \"parentId\": 1104,            \"orderNum\": 2,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"tool:emailtpl:add\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1106,            \"menuName\": \"修改\",            \"parentId\": 1104,            \"orderNum\": 3,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"tool:emailtpl:edit\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1107,            \"menuName\": \"删除\",            \"parentId\": 1104,            \"orderNum\": 4,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"tool:emailtpl:delete\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1108,            \"menuName\": \"短信记录\",            \"parentId\": 108,            \"orderNum\": 1,            \"path\": \"smsLog\",            \"component\": \"monitor/SmsLog\",            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"C\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"smscodelog:list\",            \"icon\": \"validCode\",            \"menuNameKey\": \"menu.smsLog\",            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1109,            \"menuName\": \"短信删除\",            \"parentId\": 1108,            \"orderNum\": 2,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"smscodelog:delete\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1110,            \"menuName\": \"短信导出\",            \"parentId\": 1108,            \"orderNum\": 3,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"smscodelog:export\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1111,            \"menuName\": \"邮件记录\",            \"parentId\": 9,            \"orderNum\": 1,            \"path\": \"emailLog\",            \"component\": \"tool/email/emailLog\",            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"C\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"emaillog:list\",            \"icon\": \"emailLog\",            \"menuNameKey\": \"menu.emailLog\",            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": \"邮件记录\"          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1112,            \"menuName\": \"邮件查询\",            \"parentId\": 1111,            \"orderNum\": 2,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"emaillog:query\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1113,            \"menuName\": \"邮件删除\",            \"parentId\": 1111,            \"orderNum\": 3,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"emaillog:delete\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1114,            \"menuName\": \"用户在线日志\",            \"parentId\": 2,            \"orderNum\": 1,            \"path\": \"userOnlineLog\",            \"component\": \"monitor/UserOnlineLog\",            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"C\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"useronlinelog:list\",            \"icon\": \"log\",            \"menuNameKey\": \"menu.userOnlineLog\",            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1115,            \"menuName\": \"用户在线日志导出\",            \"parentId\": 1114,            \"orderNum\": 2,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"useronlinelog:export\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1116,            \"menuName\": \"用户在线日志删除\",            \"parentId\": 1114,            \"orderNum\": 3,            \"path\": \"\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"useronlinelog:delete\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1117,            \"menuName\": \"话题管理\",            \"parentId\": 118,            \"orderNum\": 999,            \"path\": \"topic\",            \"component\": \"\\t\\narticle/articleTopic\",            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"C\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"articletopic:list\",            \"icon\": \"icon1\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1118,            \"menuName\": \"查询\",            \"parentId\": 1117,            \"orderNum\": 1,            \"path\": \"#\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"articletopic:query\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1119,            \"menuName\": \"新增\",            \"parentId\": 1117,            \"orderNum\": 2,            \"path\": \"#\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"articletopic:add\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1120,            \"menuName\": \"删除\",            \"parentId\": 1117,            \"orderNum\": 3,            \"path\": \"#\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"articletopic:delete\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1121,            \"menuName\": \"修改\",            \"parentId\": 1117,            \"orderNum\": 4,            \"path\": \"#\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"articletopic:edit\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        },        {          \"storageMessage\": null,          \"storageType\": null,          \"item\": {            \"menuId\": 1122,            \"menuName\": \"导出\",            \"parentId\": 1117,            \"orderNum\": 5,            \"path\": \"#\",            \"component\": null,            \"isCache\": \"0\",            \"isFrame\": \"0\",            \"menuType\": \"F\",            \"visible\": \"0\",            \"status\": \"0\",            \"perms\": \"articletopic:export\",            \"icon\": \"\",            \"menuNameKey\": null,            \"children\": [],            \"subNum\": 0,            \"hasChildren\": false,            \"createBy\": null,            \"createTime\": \"2025-02-10 14:47:18\",            \"updateTime\": null,            \"remark\": null          }        }      ]    }  }}', 0, NULL, '2025-02-10 14:47:19', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (155, NULL, 0, NULL, 'POST', 0, NULL, '/api/exeprogram', '127.0.0.1', '0 内网IP', '{\"id\":\"24\",\"programName\":\"RRRRRRRRRRRRRRRR\",\"inputParameters\":\"[{\\\"IsRequired\\\":true,\\\"DefaultValue\\\":\\\"rr\\\",\\\"ParametersName\\\":\\\"111\\\",\\\"ParametersType\\\":\\\"string\\\",\\\"ParametersOptions\\\":\\\"1\\\",\\\"ParametersDescription\\\":\\\"2222222\\\",\\\"ParametersSelectValue\\\":\\\"\\\"}]\",\"programType\":\"2\",\"resourceSelection\":\"null\",\"version\":\"*******\",\"isExclusive\":\"false\",\"remarks\":\"null\"}', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Cannot return null from an action method with a return type of \'Microsoft.AspNetCore.Mvc.IActionResult\'.', '2025-02-10 15:29:21', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (156, NULL, 0, NULL, 'POST', 0, NULL, '/api/exeprogram', '127.0.0.1', '0 内网IP', '{\"id\":\"24\",\"programName\":\"RRRRRRRRRRRRRRRR\",\"inputParameters\":\"[{\\\"IsRequired\\\":true,\\\"DefaultValue\\\":\\\"rr\\\",\\\"ParametersName\\\":\\\"111\\\",\\\"ParametersType\\\":\\\"string\\\",\\\"ParametersOptions\\\":\\\"1\\\",\\\"ParametersDescription\\\":\\\"rrr\\\",\\\"ParametersSelectValue\\\":\\\"\\\"}]\",\"programType\":\"2\",\"resourceSelection\":\"null\",\"version\":\"*******\",\"isExclusive\":\"false\",\"remarks\":\"null\"}', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Cannot return null from an action method with a return type of \'Microsoft.AspNetCore.Mvc.IActionResult\'.', '2025-02-10 15:29:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (157, NULL, 0, NULL, 'POST', 0, NULL, '/api/exeprogram', '127.0.0.1', '0 内网IP', '{\"id\":\"24\",\"programName\":\"RRRRRRRRRRRRRRRR\",\"inputParameters\":\"[{\\\"IsRequired\\\":true,\\\"DefaultValue\\\":\\\"rr\\\",\\\"ParametersName\\\":\\\"111\\\",\\\"ParametersType\\\":\\\"string\\\",\\\"ParametersOptions\\\":\\\"1\\\",\\\"ParametersDescription\\\":\\\"22222222\\\",\\\"ParametersSelectValue\\\":\\\"\\\"}]\",\"programType\":\"2\",\"resourceSelection\":\"null\",\"version\":\"*******\",\"isExclusive\":\"false\",\"remarks\":\"null\"}', '{\r\n  \"code\": 500,\r\n  \"msg\": \"服务器好像出了点问题，请联系系统管理员...\",\r\n  \"error\": \"请在issue里面寻找答案或者官方文档查看常见问题：https://gitee.com/izory/ZrAdminNetCore/issues\"\r\n}', 1, 'Cannot return null from an action method with a return type of \'Microsoft.AspNetCore.Mvc.IActionResult\'.', '2025-02-10 15:41:26', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (158, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2025-02-10 15:41:26', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (159, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, '', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2025-02-11 16:48:06', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (160, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"5c6046390f934342a63eb0aa1a7733e1\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzkzMjkwNzQsImV4cCI6MTczOTQxNTQ3NCwiaWF0IjoxNzM5MzI5MDc0LCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.B7EMHtQWtB3UXqgC0s_N9H-hRjvj96yC71E4iHQt8Pw\"}', 0, NULL, '2025-02-12 10:57:54', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (161, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2025-02-12 11:04:59', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (162, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"uuid\":\"320931961871442ea92649466c157019\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzkzMjk3OTAsImV4cCI6MTczOTQxNjE5MCwiaWF0IjoxNzM5MzI5NzkwLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.gP-kcAo5Y7N4Qmg6pc8eLDK5WhiFRRQlGvceCOuAsGw\"}', 0, NULL, '2025-02-12 11:09:51', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (163, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2025-02-12 11:09:57', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (164, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzkzMjk4MzgsImV4cCI6MTczOTQxNjIzOCwiaWF0IjoxNzM5MzI5ODM4LCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.lamKdLD-cxntFxWndp0b6U0PPJjFuDG68AZnt1PTfLY\"}', 0, NULL, '2025-02-12 11:10:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (165, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2025-02-12 11:10:50', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (166, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3MzkzMjk4NTAsImV4cCI6MTczOTQxNjI1MCwiaWF0IjoxNzM5MzI5ODUwLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.sN3BcUOq9du7kgW-M3IdBkUDQDfCGbm88VuuS1DtSqk\"}', 0, NULL, '2025-02-12 11:10:51', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (167, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":0,\"menuName\":\"任务管理\",\"icon\":\"build\",\"menuType\":\"M\",\"orderNum\":0,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:12:46', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (168, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1137,\"menuName\":\"任务列表\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"component\":\"RPA/JobTaskManager.vue\",\"path\":\"JobTaskManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:15:03', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (169, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1138,\"menuName\":\"任务列表\",\"parentId\":1137,\"orderNum\":999,\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager.vue\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:15:03\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:17:06', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (170, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1136,\"menuName\":\"RPA管理\",\"parentId\":1129,\"orderNum\":999,\"path\":\"ExeProgramManager\",\"component\":\"RPA/ExeProgramManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-08 18:08:24\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:18:31', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (171, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1138,\"menuName\":\"任务列表\",\"parentId\":1137,\"orderNum\":999,\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:15:03\",\"updateTime\":\"2025-02-12 11:17:06\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:19:47', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (172, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1138,\"menuName\":\"任务列表\",\"parentId\":1137,\"orderNum\":999,\"path\":\"JobTaskManager\",\"component\":\"RPA/ExeProgramManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:15:03\",\"updateTime\":\"2025-02-12 11:19:47\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:21:37', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (173, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1138,\"menuName\":\"任务列表\",\"parentId\":1137,\"orderNum\":999,\"path\":\"ExeProgramManager\",\"component\":\"RPA/ExeProgramManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:15:03\",\"updateTime\":\"2025-02-12 11:21:37\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:23:05', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (174, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1138,\"menuName\":\"任务列表\",\"parentId\":1137,\"orderNum\":999,\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:15:03\",\"updateTime\":\"2025-02-12 11:23:05\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:24:19', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (175, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1138,\"menuName\":\"任务列表\",\"parentId\":1137,\"orderNum\":999,\"path\":\"FileManagement\",\"component\":\"RPA/FileManagement\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:15:03\",\"updateTime\":\"2025-02-12 11:24:19\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:27:21', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (176, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1129,\"menuName\":\"FileManagement1\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"path\":\"FileManagement\",\"component\":\"RPA/FileManagement\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:27:58', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (177, '菜单管理', 3, 'SysMenu.Remove()', 'DELETE', 0, 'admin', '/system/Menu/1138', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:28:36', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (178, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1137,\"menuName\":\"任务列表\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:29:55', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (179, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1129,\"menuName\":\"JobTaskManager\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:30:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (180, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1141,\"menuName\":\"任务列表\",\"parentId\":1129,\"orderNum\":999,\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:30:39\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:31:27', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (181, '菜单管理', 3, 'SysMenu.RemoveAll()', 'DELETE', 0, 'admin', '/system/Menu/deleteAll/1129', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:31:57', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (182, '菜单管理', 3, 'SysMenu.RemoveAll()', 'DELETE', 0, 'admin', '/system/Menu/deleteAll/1137', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:32:07', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (183, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":0,\"menuName\":\"程序管理\",\"menuType\":\"M\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:32:29', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (184, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1142,\"menuName\":\"程序管理\",\"parentId\":0,\"orderNum\":0,\"path\":\"\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:32:29\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:32:39', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (185, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1142,\"menuName\":\"程序列表\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"component\":\"RPA/ExeProgramManager\",\"path\":\"ExeProgramManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:33:08', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (186, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1142,\"menuName\":\"任务列表\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 11:33:38', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (187, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":0,\"menuName\":\"任务管理\",\"menuType\":\"M\",\"orderNum\":0,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:40:00', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (188, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1144,\"menuName\":\"任务列表\",\"parentId\":1145,\"orderNum\":999,\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:33:38\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:40:13', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (189, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1144,\"menuName\":\"任务列表\",\"parentId\":1142,\"orderNum\":999,\"path\":\"JobTaskManager\",\"component\":\"RPA/JobTaskManager\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:33:38\",\"updateTime\":\"2025-02-12 15:40:13\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:41:41', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (190, '菜单管理', 3, 'SysMenu.Remove()', 'DELETE', 0, 'admin', '/system/Menu/1145', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:42:10', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (191, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1142,\"menuName\":\"RPA管理\",\"parentId\":0,\"orderNum\":0,\"path\":\"\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:32:29\",\"updateTime\":\"2025-02-12 11:32:39\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:42:21', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (192, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1142,\"menuName\":\"文件管理\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"path\":\"FileManagement\",\"component\":\"RPA/FileManagement\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:42:57', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (193, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1142,\"menuName\":\"资源机管理\",\"menuType\":\"M\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"path\":\"ResourceMachineManagement\",\"component\":\"RPA/ResourceMachineManagement\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:43:22', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (194, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1142,\"menuName\":\"资源池管理\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"path\":\"ResourcePoolManager\",\"component\":\"RPA/ResourcePoolManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:43:43', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (195, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1147,\"menuName\":\"资源机管理\",\"parentId\":1142,\"orderNum\":999,\"path\":\"ResourceMachineManagement\",\"component\":\"RPA/ResourceMachineManagement\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 15:43:22\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:44:53', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (196, '菜单管理', 1, 'SysMenu.MenuAdd()', 'PUT', 0, 'admin', '/system/menu/add', '127.0.0.1', '0-内网IP-内网IP', '{\"parentId\":1142,\"menuName\":\"凭证管理\",\"menuType\":\"C\",\"orderNum\":999,\"isFrame\":\"0\",\"isCache\":\"0\",\"visible\":\"0\",\"status\":\"0\",\"path\":\"RpaCredentialManager\",\"component\":\"RPA/RpaCredentialManager\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:45:21', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (197, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1142,\"menuName\":\"RPA管理\",\"parentId\":0,\"orderNum\":0,\"path\":\"\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"0\",\"status\":\"0\",\"icon\":\"pc\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"admin\",\"createTime\":\"2025-02-12 11:32:29\",\"updateTime\":\"2025-02-12 15:42:21\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-12 15:50:30', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (198, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, '', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": null,    \"id\": 0  }}', 0, NULL, '2025-02-14 10:52:21', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (199, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3Mzk1MDE1NDEsImV4cCI6MTczOTU4Nzk0MSwiaWF0IjoxNzM5NTAxNTQxLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.O-ryPUQOi5OkGPlXd7ZvrWuBIVDxtuh3y0mCcsKJx7I\"}', 0, NULL, '2025-02-14 10:52:22', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (200, '注销', 0, 'SysLogin.LogOut()', 'POST', 0, 'admin', '/LogOut', '127.0.0.1', '0-内网IP-内网IP', '{}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": {    \"name\": \"admin\",    \"id\": 1  }}', 0, NULL, '2025-02-14 10:52:23', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (201, '登录', 0, 'SysLogin.Login()', 'POST', 0, 'admin', '/login', '127.0.0.1', '0-内网IP-内网IP', '{\"username\":\"admin\",\"password\":\"***\",\"clientId\":\"f0cf89fbad2efb92edc59f0da9604348\"}', '{  \"code\": 200,  \"msg\": \"success\",  \"data\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkJlYXJlciJ9.eyJwcmltYXJ5c2lkIjoiMSIsIm5hbWVpZCI6IjEiLCJ1bmlxdWVfbmFtZSI6ImFkbWluIiwiZ3JvdXBzaWQiOiIwIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy91c2VyZGF0YSI6IntcIlVzZXJJZFwiOjEsXCJEZXB0SWRcIjowLFwiVXNlck5hbWVcIjpcImFkbWluXCIsXCJSb2xlSWRzXCI6W1wiYWRtaW5cIl0sXCJSb2xlc1wiOlt7XCJSb2xlSWRcIjoxLFwiUm9sZUtleVwiOlwiYWRtaW5cIixcIkRhdGFTY29wZVwiOjF9XSxcIkV4cGlyZVRpbWVcIjpcIjAwMDEtMDEtMDFUMDA6MDA6MDBcIn0iLCJBdWRpZW5jZSI6IlpSQWRtaW4uTkVUIiwiSXNzdWVyIjoiWlJBZG1pbi5ORVQiLCJuYmYiOjE3Mzk1MDE1NDMsImV4cCI6MTczOTU4Nzk0MywiaWF0IjoxNzM5NTAxNTQzLCJpc3MiOiJaUkFkbWluLk5FVCIsImF1ZCI6IlpSQWRtaW4uTkVUIn0.nlp_tObOO0aXf4cM4OpZhKdB13qoS_kAqVKEdmpSj3c\"}', 0, NULL, '2025-02-14 10:52:23', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (202, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1123,\"menuName\":\"功能名只是演示\",\"parentId\":0,\"orderNum\":999,\"path\":\"Justdemo\",\"component\":\"business/Justdemo\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"1\",\"status\":\"0\",\"perms\":\"justdemo:list\",\"icon\":\"icon1\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createBy\":\"system\",\"createTime\":\"2024-12-11 17:55:58\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-14 14:55:45', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (203, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":1093,\"menuName\":\"广告管理\",\"parentId\":0,\"orderNum\":999,\"path\":\"bannerConfig\",\"component\":\"public/BannerConfig\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"C\",\"visible\":\"1\",\"status\":\"0\",\"perms\":\"bannerconfig:list\",\"icon\":\"app\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\",\"updateTime\":\"2024-12-23 15:30:17\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-14 14:55:48', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (204, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":118,\"menuName\":\"内容管理\",\"parentId\":0,\"orderNum\":18,\"path\":\"article\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"1\",\"status\":\"0\",\"icon\":\"documentation\",\"menuNameKey\":\"menu.systemArticle\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-14 14:55:57', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (205, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":7,\"menuName\":\"组件示例\",\"parentId\":0,\"orderNum\":1,\"path\":\"\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"1\",\"status\":\"0\",\"icon\":\"zujian\",\"menuNameKey\":\"menu.zujianDemo\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-14 14:56:12', 0, NULL);
INSERT INTO `sys_oper_log` VALUES (206, '菜单管理', 2, 'SysMenu.MenuEdit()', 'POST', 0, 'admin', '/system/Menu/edit', '127.0.0.1', '0-内网IP-内网IP', '{\"menuId\":3,\"menuName\":\"系统工具\",\"parentId\":0,\"orderNum\":3,\"path\":\"tool\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuType\":\"M\",\"visible\":\"1\",\"status\":\"0\",\"icon\":\"tool\",\"menuNameKey\":\"menu.systemTools\",\"children\":[],\"subNum\":0,\"hasChildren\":false,\"createTime\":\"2024-12-10 10:40:31\"}', '{  \"code\": 200,  \"msg\": \"success\"}', 0, NULL, '2025-02-14 14:56:22', 0, NULL);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `PostId` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位Id',
  `PostCode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `PostName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `PostSort` int NOT NULL,
  `Status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`PostId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '岗位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'CEO', '董事长', 1, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (2, 'SE', '项目经理', 2, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (3, 'HR', '人力资源', 3, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (4, 'USER', '普通员工', 4, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (5, 'PM', '人事经理', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (6, 'GM', '总经理', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (7, 'COO', '首席运营官', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (8, 'CFO', '首席财务官', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (9, 'CTO', '首席技术官', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (10, 'HRD', '人力资源总监', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (11, 'VP', '副总裁', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (12, 'OD', '运营总监', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_post` VALUES (13, 'MD', '市场总监', 0, '0', NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `RoleId` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `RoleName` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  `RoleKey` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色权限',
  `RoleSort` int NOT NULL COMMENT '角色排序',
  `Status` int NULL DEFAULT 0 COMMENT '帐号状态（0正常 1停用）',
  `DelFlag` int NULL DEFAULT 0 COMMENT '删除标志（0代表存在 2代表删除）',
  `DataScope` int NULL DEFAULT 1 COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限））',
  `menu_check_strictly` tinyint(1) NULL DEFAULT NULL COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT NULL COMMENT '部门树选择项是否关联显示',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`RoleId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, 0, 0, 1, 1, 0, NULL, '2024-12-10 10:40:31', NULL, NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, 0, 0, 5, 1, 0, NULL, '2024-12-10 10:40:31', NULL, NULL, '普通角色');
INSERT INTO `sys_role` VALUES (3, '编辑角色', 'editor', 3, 0, 0, 5, 1, 0, NULL, '2024-12-10 10:40:31', NULL, NULL, '编辑角色');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `RoleId` bigint NOT NULL,
  `DeptId` bigint NOT NULL,
  PRIMARY KEY (`RoleId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色部门' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `Role_id` bigint NOT NULL,
  `Menu_id` bigint NOT NULL,
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`Role_id`, `Menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色菜单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (2, 1, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 3, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 6, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 100, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 101, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 102, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 103, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 104, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 106, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 108, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 109, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 114, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 500, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 501, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1001, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1008, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1013, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1018, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1022, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1031, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1041, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1044, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (2, 1051, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (3, 3, NULL, '2024-12-10 10:40:31', NULL, NULL, '系统工具');
INSERT INTO `sys_role_menu` VALUES (3, 4, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (3, 118, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (3, 119, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章管理');
INSERT INTO `sys_role_menu` VALUES (3, 1047, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_role_menu` VALUES (3, 1048, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章管理');
INSERT INTO `sys_role_menu` VALUES (3, 1049, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章管理');
INSERT INTO `sys_role_menu` VALUES (3, 1050, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章管理');
INSERT INTO `sys_role_menu` VALUES (3, 1084, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章目录');
INSERT INTO `sys_role_menu` VALUES (3, 1085, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章目录');
INSERT INTO `sys_role_menu` VALUES (3, 1086, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章目录');
INSERT INTO `sys_role_menu` VALUES (3, 1087, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章目录');
INSERT INTO `sys_role_menu` VALUES (3, 1088, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章目录');
INSERT INTO `sys_role_menu` VALUES (3, 1089, NULL, '2024-12-10 10:40:31', NULL, NULL, '文章目录');

-- ----------------------------
-- Table structure for sys_tasks
-- ----------------------------
DROP TABLE IF EXISTS `sys_tasks`;
CREATE TABLE `sys_tasks`  (
  `ID` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务id',
  `Name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称',
  `JobGroup` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务分组',
  `Cron` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '运行时间表达式',
  `AssemblyName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '程序集名称',
  `ClassName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务所在类',
  `RunTimes` int NOT NULL DEFAULT 0 COMMENT '执行次数',
  `BeginTime` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `EndTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `TriggerType` int NOT NULL COMMENT '触发器类型（0、simple 1、cron） \n            默认 : 1',
  `IntervalSecond` int NOT NULL COMMENT '执行间隔时间(单位:秒) \n            默认 : 0',
  `IsStart` int NOT NULL DEFAULT 0 COMMENT '是否启动',
  `JobParams` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '传入参数',
  `LastRunTime` datetime NULL DEFAULT NULL COMMENT '最后运行时间',
  `ApiUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'api执行地址',
  `TaskType` int NULL DEFAULT 1 COMMENT '任务类型 1、程序集 2、网络请求 3、SQL语句',
  `SqlText` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SQL语句',
  `RequestMethod` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '网络请求方式',
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '计划任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_tasks
-- ----------------------------

-- ----------------------------
-- Table structure for sys_tasks_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_tasks_log`;
CREATE TABLE `sys_tasks_log`  (
  `JobLogId` bigint NOT NULL AUTO_INCREMENT COMMENT '日志Id',
  `JobId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务Id',
  `JobName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名',
  `JobGroup` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务分组',
  `Status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `Exception` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '异常',
  `JobMessage` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `InvokeTarget` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '调用目标字符串',
  `CreateTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Elapsed` double NULL DEFAULT NULL COMMENT '执行用时，毫秒',
  PRIMARY KEY (`JobLogId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_tasks_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `UserId` bigint NOT NULL AUTO_INCREMENT COMMENT '用户id',
  `UserName` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户账号',
  `NickName` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户昵称',
  `UserType` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `Avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
  `Password` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `Phonenumber` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `Sex` int NULL DEFAULT NULL COMMENT '用户性别（0男 1女 2未知）',
  `Status` int NULL DEFAULT 0 COMMENT '帐号状态（0正常 1停用）',
  `DelFlag` int NULL DEFAULT 0 COMMENT '删除标志（0代表存在 2代表删除）',
  `LoginIP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `LoginDate` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `DeptId` bigint NULL DEFAULT 0 COMMENT '部门Id',
  `Province` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `City` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`UserId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'admin', '管理员', '0', NULL, NULL, 'E10ADC3949BA59ABBE56E057F20F883E', NULL, 0, 0, 0, '127.0.0.1', '2025-02-14 10:52:23', 0, NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '管理员');
INSERT INTO `sys_user` VALUES (2, 'user', '普通用户', '0', NULL, NULL, 'E10ADC3949BA59ABBE56E057F20F883E', NULL, 0, 0, 0, NULL, NULL, 0, NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '普通用户');
INSERT INTO `sys_user` VALUES (3, 'editor', '编辑人员', '0', NULL, NULL, 'E10ADC3949BA59ABBE56E057F20F883E', NULL, 0, 0, 0, NULL, NULL, 0, NULL, NULL, NULL, '2024-12-10 10:40:31', NULL, NULL, '编辑人员');

-- ----------------------------
-- Table structure for sys_user_msg
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_msg`;
CREATE TABLE `sys_user_msg`  (
  `MsgId` bigint NOT NULL COMMENT '消息ID',
  `UserId` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `Content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '消息内容',
  `IsRead` int NULL DEFAULT NULL COMMENT '是否已读',
  `AddTime` datetime NULL DEFAULT NULL COMMENT '添加时间',
  `TargetId` bigint NULL DEFAULT NULL COMMENT '目标ID',
  `MsgType` int NULL DEFAULT NULL COMMENT '消息类型',
  `IsDelete` int NULL DEFAULT NULL COMMENT '是否删除',
  `FromUserid` bigint NULL DEFAULT NULL COMMENT '来源用户',
  PRIMARY KEY (`MsgId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户系统消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_msg
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `UserId` bigint NOT NULL,
  `PostId` bigint NOT NULL,
  PRIMARY KEY (`UserId`, `PostId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  `Create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `Update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (2, 2, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (3, 3, NULL, '2024-12-10 10:40:31', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for testtable
-- ----------------------------
DROP TABLE IF EXISTS `testtable`;
CREATE TABLE `testtable`  (
  `ID` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `MyType` enum('待运行','运行中','结束') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of testtable
-- ----------------------------
INSERT INTO `testtable` VALUES (1, '222', '运行中');
INSERT INTO `testtable` VALUES (4, '222123123', '待运行');
INSERT INTO `testtable` VALUES (5, '333123123', '结束');
INSERT INTO `testtable` VALUES (7, '111', '待运行');
INSERT INTO `testtable` VALUES (8, '111', '待运行');

-- ----------------------------
-- Table structure for useronlinelog
-- ----------------------------
DROP TABLE IF EXISTS `useronlinelog`;
CREATE TABLE `useronlinelog`  (
  `Id` bigint NOT NULL COMMENT 'Id',
  `UserId` bigint NULL DEFAULT NULL COMMENT '用户id',
  `OnlineTime` double NULL DEFAULT NULL COMMENT '在线时长(分)',
  `TodayOnlineTime` double NULL DEFAULT NULL COMMENT '今日在线时长',
  `AddTime` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `Location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地址位置',
  `UserIP` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户IP',
  `LoginTime` datetime NULL DEFAULT NULL,
  `Remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `Platform` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登录平台',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户在线时长' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of useronlinelog
-- ----------------------------
INSERT INTO `useronlinelog` VALUES (1866372397045784576, 1, 220.37, 220.37, '2024-12-10 14:40:30', '内网IP', '127.0.0.1', '2024-12-10 11:00:07', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1866403506215329792, 1, 123.49, 343.96, '2024-12-10 16:44:07', '内网IP', '127.0.0.1', '2024-12-10 14:40:37', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1866404073918570496, 1, 1.93, 345.89, '2024-12-10 16:46:22', '内网IP', '127.0.0.1', '2024-12-10 16:44:26', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1866408962035949568, 1, 1.93, 347.82, '2024-12-10 17:05:47', '内网IP', '127.0.0.1', '2024-12-10 17:03:52', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1866409683535929344, 1, 2.44, 350.46, '2024-12-10 17:08:39', '内网IP', '127.0.0.1', '2024-12-10 17:06:13', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1866770195155132416, 1, 1.04, 1.04, '2024-12-11 17:01:12', '内网IP', '127.0.0.1', '2024-12-11 17:00:10', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1866784890410901504, 1, 2.32, 2.32, '2024-12-11 17:59:36', '内网IP', '127.0.0.1', '2024-12-11 17:57:16', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1866789051739283456, 1, 12.38, 12.38, '2024-12-11 18:16:08', '内网IP', '127.0.0.1', '2024-12-11 18:03:45', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871096050572468224, 1, 8.05, 8.05, '2024-12-23 15:30:36', '内网IP', '127.0.0.1', '2024-12-23 15:22:33', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871096656401932288, 1, 2.4, 10.45, '2024-12-23 15:33:01', '内网IP', '127.0.0.1', '2024-12-23 15:30:37', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871096899004669952, 1, 0.95, 11.4, '2024-12-23 15:33:59', '内网IP', '127.0.0.1', '2024-12-23 15:33:02', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871100134675324928, 1, 12.85, 24.25, '2024-12-23 15:46:50', '内网IP', '127.0.0.1', '2024-12-23 15:33:59', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871107773379518464, 1, 1.35, 1.35, '2024-12-23 16:17:11', '内网IP', '127.0.0.1', '2024-12-23 16:15:50', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871108293414494208, 1, 1.29, 3.38, '2024-12-23 16:19:15', '内网IP', '127.0.0.1', '2024-12-23 16:17:58', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871108534591168512, 1, 0.61, 4.32, '2024-12-23 16:20:13', '内网IP', '127.0.0.1', '2024-12-23 16:19:36', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871108824165916672, 1, 1.14, 5.46, '2024-12-23 16:21:22', '内网IP', '127.0.0.1', '2024-12-23 16:20:13', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871114162638299136, 1, 0.7, 0.7, '2024-12-23 16:42:35', '内网IP', '127.0.0.1', '2024-12-23 16:41:52', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871115105421369344, 1, 1.89, 1.89, '2024-12-23 16:46:19', '内网IP', '127.0.0.1', '2024-12-23 16:44:26', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871116291591512064, 1, 0.95, 0.95, '2024-12-23 16:51:02', '内网IP', '127.0.0.1', '2024-12-23 16:50:05', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871116426249641984, 1, 0.52, 1.47, '2024-12-23 16:51:34', '内网IP', '127.0.0.1', '2024-12-23 16:51:03', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1871119189037686784, 1, 10.58, 10.58, '2024-12-23 17:02:33', '内网IP', '127.0.0.1', '2024-12-23 16:51:58', 'Windows 10 Other Edge 131.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888124400872595456, 1, 22.48, 22.48, '2025-02-08 15:15:11', '内网IP', '127.0.0.1', '2025-02-08 14:52:43', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888125595922079744, 1, 4.72, 27.2, '2025-02-08 15:19:56', '内网IP', '127.0.0.1', '2025-02-08 15:15:13', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888133695991324672, 1, 0.78, 1.03, '2025-02-08 15:52:08', '内网IP', '127.0.0.1', '2025-02-08 15:51:21', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888134082286723072, 1, 1.52, 2.55, '2025-02-08 15:53:40', '内网IP', '127.0.0.1', '2025-02-08 15:52:08', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888134829736861696, 1, 2.96, 5.51, '2025-02-08 15:56:38', '内网IP', '127.0.0.1', '2025-02-08 15:53:40', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888135901385728000, 1, 4, 9.73, '2025-02-08 16:00:53', '内网IP', '127.0.0.1', '2025-02-08 15:56:53', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888137608429707264, 1, 3.98, 14.18, '2025-02-08 16:07:40', '内网IP', '127.0.0.1', '2025-02-08 16:03:41', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888168416779575296, 1, 120.77, 3158.8, '2025-02-08 18:10:06', '内网IP', '127.0.0.1', '2025-02-08 16:09:19', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888836666647519232, 1, 1.12, 1.12, '2025-02-10 14:25:29', '内网IP', '127.0.0.1', '2025-02-10 14:24:22', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888839945196544000, 1, 13.02, 14.14, '2025-02-10 14:38:31', '内网IP', '127.0.0.1', '2025-02-10 14:25:30', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1888855781995061248, 1, 12.07, 12.07, '2025-02-10 15:41:26', '内网IP', '127.0.0.1', '2025-02-10 15:29:22', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889129436234784768, 1, 1087.4, 1099.47, '2025-02-11 09:48:51', '内网IP', '127.0.0.1', '2025-02-10 15:41:27', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889234939287838720, 1, 419.19, 421.49, '2025-02-11 16:48:04', '内网IP', '127.0.0.1', '2025-02-11 09:48:53', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889510988995239936, 1, 7.08, 7.08, '2025-02-12 11:05:00', '内网IP', '127.0.0.1', '2025-02-12 10:57:55', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889512955834408960, 1, 1.96, 9.31, '2025-02-12 11:12:49', '内网IP', '127.0.0.1', '2025-02-12 11:10:51', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889513530282090496, 1, 2.27, 11.58, '2025-02-12 11:15:06', '内网IP', '127.0.0.1', '2025-02-12 11:12:49', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889514046370226176, 1, 2.04, 13.62, '2025-02-12 11:17:09', '内网IP', '127.0.0.1', '2025-02-12 11:15:06', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889514601469583360, 1, 1.69, 15.8, '2025-02-12 11:19:21', '内网IP', '127.0.0.1', '2025-02-12 11:17:39', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889515175946625024, 1, 1.81, 18.06, '2025-02-12 11:21:38', '内网IP', '127.0.0.1', '2025-02-12 11:19:49', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889515541916426240, 1, 1.24, 19.49, '2025-02-12 11:23:05', '内网IP', '127.0.0.1', '2025-02-12 11:21:51', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889576684416540672, 1, 242.43, 446.97, '2025-02-12 15:26:03', '内网IP', '127.0.0.1', '2025-02-12 11:23:37', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889582063984713728, 1, 21.36, 608.42, '2025-02-12 15:47:25', '内网IP', '127.0.0.1', '2025-02-12 15:26:04', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1889582844687294464, 1, 3.09, 611.51, '2025-02-12 15:50:32', '内网IP', '127.0.0.1', '2025-02-12 15:47:26', 'Windows 10 Other Edge 132.0.0', 'web');
INSERT INTO `useronlinelog` VALUES (1890294385602600960, 1, 1.52, 1.55, '2025-02-14 14:57:56', '内网IP', '127.0.0.1', '2025-02-14 14:56:25', 'Windows 10 Other Edge 133.0.0', 'web');

SET FOREIGN_KEY_CHECKS = 1;
