<template>
  <div class="exe-program-manager">
    <!-- 顶部操作栏 -->
    <div class="operation-bar">
      <div class="left-section">
        <el-input
          v-model="searchQuery"
          placeholder="请输入程序名称搜索"
          style="width: 300px;"
          clearable
          @keyup.enter="search"
        >
          <template #append>
            <el-button @click="search">搜索</el-button>
          </template>
        </el-input>
      </div>
      <div class="right-section">
        <el-button type="primary" @click="openDialog(null)">新增程序</el-button>
      </div>
    </div>

    <!-- EXE程序列表 -->
    <el-table 
      v-loading="loading"
      element-loading-text="加载中..."
      :data="exePrograms" 
      class="custom-table"
      style="margin-top: 20px;">
      <el-table-column prop="programName" label="程序名" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="version" label="版本号" width="100">
        <template #default="scope">
          {{ scope.row.version || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="inputParameters" label="输入参数" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="isExclusive" label="是否独占" width="80">
        <template #default="scope">{{ scope.row.isExclusive ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column prop="programType" label="程序类型" width="100">
        <template #default="scope">
          {{ getProgramTypeLabel(scope.row.programType) }}
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="160" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.createdAt ? new Date(scope.row.createdAt).toLocaleString() : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="updatedAt" label="最后更新时间" width="160" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.updatedAt ? new Date(scope.row.updatedAt).toLocaleString() : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button type="primary" @click="openDialog(scope.row)">修改</el-button>
          <el-button type="danger" @click="deleteExeProgram(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/修改程序弹出窗口 -->
    <el-dialog :title="isEditMode ? '修改程序' : '新增程序'" v-model="dialogVisible" width="1000px">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="EXE程序" name="exe" :disabled="isEditMode && activeTab !== 'exe'">
          <el-form :model="exeForm" label-width="120px" ref="exeFormRef" :rules="formRules">
            <el-form-item label="上传文件包" prop="ProgramPackageFile" :rules="isEditMode ? {} : { required: true, message: '请上传文件包', trigger: 'change' }">
              <el-upload
                :file-list="fileList"
                :auto-upload="false"
                :on-change="handleFileChange"
                accept=".zip"
              >
                <el-button type="primary">选择文件</el-button>
                <div slot="tip" class="el-upload__tip">仅支持.zip格式文件</div>
              </el-upload>
            </el-form-item>
            <el-form-item label="程序名" prop="programName" :rules="{ required: true, message: '请输入程序名', trigger: 'blur' }">
              <el-input v-model="exeForm.programName" :disabled="isEditMode"></el-input>
            </el-form-item>
            <el-form-item label="版本号" v-if="isEditMode">
              <el-input v-model="exeForm.version" disabled></el-input>
            </el-form-item>
            <el-form-item label="是否独占">
              <el-switch v-model="exeForm.isExclusive"></el-switch>
            </el-form-item>
            <el-form-item label="默认资源选择">
              <resource-selector v-model="exeForm.resourceSelection" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="exeForm.remarks"></el-input>
            </el-form-item>
            <el-form-item label="输入参数">
              <input-parameters-editor
                :key="'exe-' + inputParametersKey"
                :program-type="1"
                :initial-parameters="exeForm.inputParameters"
                @update:parameters="updateExeParameters"
              ></input-parameters-editor>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="RPA程序" name="rpa" :disabled="isEditMode && activeTab !== 'rpa'">
          <el-form :model="rpaForm" label-width="120px" ref="rpaFormRef" :rules="formRules">
            <el-form-item label="上传文件包" prop="ProgramPackageFile" :rules="isEditMode ? {} : { required: true, message: '请上传文件包', trigger: 'change' }">
              <el-upload
                :file-list="fileList"
                :auto-upload="false"
                :on-change="handleFileChange"
                accept=".zip"
              >
                <el-button type="primary">选择文件</el-button>
                <div slot="tip" class="el-upload__tip">仅支持.zip格式文件</div>
              </el-upload>
            </el-form-item>
            <el-form-item label="程序名" prop="programName" :rules="{ required: true, message: '请输入程序名', trigger: 'blur' }">
              <el-input v-model="rpaForm.programName" :disabled="isEditMode"></el-input>
            </el-form-item>
            <el-form-item label="版本号" v-if="isEditMode">
              <el-input v-model="rpaForm.version" disabled></el-input>
            </el-form-item>
            <el-form-item label="是否独占">
              <el-switch v-model="rpaForm.isExclusive"></el-switch>
            </el-form-item>
            <el-form-item label="默认资源选择">
              <resource-selector v-model="rpaForm.resourceSelection" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="rpaForm.remarks"></el-input>
            </el-form-item>
            <el-form-item label="输入参数">
              <input-parameters-editor
                :key="'rpa-' + inputParametersKey"
                :program-type="0"
                :initial-parameters="rpaForm.inputParameters"
                @update:parameters="updateRpaParameters"
              ></input-parameters-editor>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="编排程序" name="orchestration" :disabled="isEditMode && activeTab !== 'orchestration'">
          <el-form :model="orchestrationForm" label-width="120px" ref="orchestrationFormRef" :rules="formRules">
            <el-form-item label="程序名" prop="programName" :rules="{ required: true, message: '请输入程序名', trigger: 'blur' }">
              <el-input v-model="orchestrationForm.programName" :disabled="isEditMode"></el-input>
            </el-form-item>
            <el-form-item label="版本号" v-if="isEditMode">
              <el-input v-model="orchestrationForm.version" disabled></el-input>
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="orchestrationForm.remarks"></el-input>
            </el-form-item>
            <el-form-item label="输入参数">
              <input-parameters-editor
                :key="'orchestration-' + inputParametersKey"
                :program-type="2"
                :initial-parameters="orchestrationForm.inputParameters"
                @update:parameters="updateOrchestrationParameters"
              ></input-parameters-editor>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="submitExeProgram">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      layout="total, sizes, prev, pager, next"
      :page-sizes="[10, 20, 50, 100]"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'; // 添加 onMounted
import axios from 'axios';
import InputParametersEditor from '../components/InputParametersEditor.vue';
import ResourceSelector from '../components/ResourceSelector.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

export default {
  components: {
    InputParametersEditor,
    ResourceSelector
  },
  setup() {
    const exePrograms = ref([]);
    const dialogVisible = ref(false);
    const isEditMode = ref(false);
    const activeTab = ref('exe');
    const inputParametersKey = ref(0);
    const fileList = ref([]);
    const searchQuery = ref('');
    const loading = ref(false);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);

    // 添加formRules定义
    const formRules = {
      programName: [
        { required: true, message: '请输入程序名', trigger: 'blur' }
      ]
    };

    // 表单引用
    const exeFormRef = ref(null);
    const rpaFormRef = ref(null);
    const orchestrationFormRef = ref(null);

    // 程序类型枚举
    const ProgramTypeEnum = {
      RPA: 0,
      EXE: 1,
      ORCHESTRATION: 2
    };

    // 获取RPA默认参数
    const getDefaultRpaParams = () => {
      return [
        { 
          ParametersName: 'InputFile', 
          ParametersType: 'file', 
          ParametersDescription: '输入文件',
          DefaultValue: '',
          IsRequired: true 
        },
        { 
          ParametersName: 'ServerIP', 
          ParametersType: 'string', 
          ParametersDescription: '服务器IP',
          DefaultValue: '',
          IsRequired: true 
        },
        { 
          ParametersName: 'UserName', 
          ParametersType: 'RpaCredentials', 
          ParametersDescription: '用户名',
          DefaultValue: '',
          IsRequired: true 
        }
      ];
    };

    // 三种类型的表单数据
    const exeForm = ref({
      programName: '',
      programType: 1, // EXE
      isExclusive: false,
      inputParameters: '',
      remarks: '',
      resourceSelection: '',
      version: '',
      ProgramPackageFile: null
    });

    const rpaForm = ref({
      programName: '',
      programType: 0, // RPA
      isExclusive: true,
      inputParameters: '',
      remarks: '',
      resourceSelection: '',
      version: '',
      ProgramPackageFile: null
    });

    const orchestrationForm = ref({
      programName: '',
      programType: 2, // 编排
      inputParameters: '',
      remarks: '',
      version: ''
    });

    // 处理Tab切换
    const handleTabClick = () => {
      // 如果切换到RPA标签，且参数为空，则设置默认参数
      if (activeTab.value === 'rpa' && (!rpaForm.value.inputParameters || rpaForm.value.inputParameters === '[]')) {
        rpaForm.value.inputParameters = JSON.stringify(getDefaultRpaParams());
      }
      inputParametersKey.value++;
    };

    // 更新各类型的参数
    const updateExeParameters = (newParameters) => {
      exeForm.value.inputParameters = newParameters;
    };

    const updateRpaParameters = (newParameters) => {
      rpaForm.value.inputParameters = newParameters;
    };

    const updateOrchestrationParameters = (newParameters) => {
      orchestrationForm.value.inputParameters = newParameters;
    };

    // 重置表单数据
    const resetForms = () => {
      exeForm.value = {
        programName: '',
        programType: 1,
        isExclusive: false,
        inputParameters: '',
        remarks: '',
        resourceSelection: '',
        version: '',
        ProgramPackageFile: null
      };

      rpaForm.value = {
        programName: '',
        programType: 0,
        isExclusive: true,
        inputParameters: JSON.stringify(getDefaultRpaParams()),
        remarks: '',
        resourceSelection: '',
        version: '',
        ProgramPackageFile: null
      };

      orchestrationForm.value = {
        programName: '',
        programType: 2,
        inputParameters: '',
        remarks: '',
        version: ''
      };

      fileList.value = [];
      inputParametersKey.value++;
    };

    // 处理取消
    const handleCancel = () => {
      dialogVisible.value = false;
      resetForms();
    };

    // 处理后台返回的数据，确保remarks和inputParameters不为null
    const processFormData = (row) => {
      return {
        ...row,
        remarks: row.remarks ?? '',  // 将null转为空字符串
        inputParameters: row.inputParameters || '[]', // 确保inputParameters不为null或undefined
        ProgramPackageFile: null
      };
    };

    // 打开对话框
    const openDialog = (row) => {
      dialogVisible.value = true;
      if (row) {
        isEditMode.value = true;
        
        // 处理后台返回的数据
        const processedRow = processFormData(row);
        
        // 根据程序类型选择对应的表单
        switch (row.programType) {
          case 1: // EXE
            activeTab.value = 'exe';
            exeForm.value = processedRow;
            break;
          case 0: // RPA
            activeTab.value = 'rpa';
            rpaForm.value = processedRow;
            break;
          case 2: // 编排
            activeTab.value = 'orchestration';
            orchestrationForm.value = processedRow;
            break;
        }
      } else {
        isEditMode.value = false;
        resetForms();
        // 如果当前是RPA标签，确保有默认参数
        if (activeTab.value === 'rpa') {
          rpaForm.value.inputParameters = JSON.stringify(getDefaultRpaParams());
          inputParametersKey.value++;
        }
      }
    };

    // 提交表单
    const submitExeProgram = async () => {
      try {
        // 获取当前表单引用
        let formRef;
        let currentForm;
        
        switch (activeTab.value) {
          case 'exe':
            formRef = exeFormRef.value;
            currentForm = exeForm.value;
            break;
          case 'rpa':
            formRef = rpaFormRef.value;
            currentForm = rpaForm.value;
            break;
          case 'orchestration':
            formRef = orchestrationFormRef.value;
            currentForm = orchestrationForm.value;
            break;
        }

        // 表单验证
        if (!formRef) return;
        
        await formRef.validate();

        // 修改额外验证文件上传的逻辑：只在新增EXE或RPA程序时验证
        if ((activeTab.value === 'exe' || activeTab.value === 'rpa') && !isEditMode.value && !currentForm.ProgramPackageFile) {
          ElMessage.error('请上传文件包');
          return;
        }

        const formData = new FormData();

        // 添加所有字段到formData，特殊处理resourceSelection和remarks
        Object.keys(currentForm).forEach(key => {
          if (key === 'ProgramPackageFile' && currentForm[key]) {
            formData.append(key, currentForm[key]);
          } else if (key === 'resourceSelection') {
            // 如果resourceSelection为空字符串或"null"，则不添加该字段
            if (currentForm[key] && currentForm[key] !== 'null') {
              formData.append(key, currentForm[key]);
            }
          } else if (key === 'remarks') {
            // remarks字段如果为null或"null"，则设置为空字符串
            formData.append(key, currentForm[key] === null || currentForm[key] === 'null' ? '' : currentForm[key]);
          } else if (key !== 'ProgramPackageFile') {
            formData.append(key, currentForm[key]);
          }
        });

        await axios.post('/api/exeprogram', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        ElMessage.success('保存成功');
        dialogVisible.value = false;
        resetForms();
        fetchExePrograms();
      } catch (error) {
        if (error.message) {
          ElMessage.error(error.message);
        } else {
          console.error('提交失败:', error);
          ElMessage.error('保存失败');
        }
      }
    };

    const fetchExePrograms = async () => {
      loading.value = true;
      try {
        const response = await axios.get('/api/exeprogram/search', {
          params: {
            query: searchQuery.value
          }
        });
        exePrograms.value = response.data;
        console.log('Programs loaded:', response.data); // 添加调试日志
      } catch (error) {
        console.error('获取程序列表失败:', error);
      } finally {
        loading.value = false;
      }
    };

    const handleCurrentChange = (val) => {
      currentPage.value = val;
      fetchExePrograms();
    };

    const handleSizeChange = (val) => {
      pageSize.value = val;
      fetchExePrograms();
    };

    // 在组件挂载时加载数据
    onMounted(() => {
      fetchExePrograms(); // 组件加载时获取所有程序
    });

    const deleteExeProgram = async (id) => {
      try {
        // 查找要删除的程序
        const program = exePrograms.value.find(p => p.id === id);
        if (!program) {
          ElMessage.error('未找到要删除的程序');
          return;
        }

        // 弹出确认对话框，显示程序名
        await ElMessageBox.confirm(
          `确定要删除程序"${program.programName}"吗？`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        await axios.delete(`/api/exeprogram/${id}`);
        ElMessage.success('删除成功');
        fetchExePrograms();
      } catch (error) {
        if (error !== 'cancel') { // 用户点击取消按钮时不显示错误
          console.error('删除EXE程序失败:', error);
          ElMessage.error('删除失败');
        }
      }
    };

    const getProgramTypeLabel = (value) => {
      switch (value) {
        case ProgramTypeEnum.RPA:
          return 'RPA';
        case ProgramTypeEnum.EXE:
          return 'EXE';
        case ProgramTypeEnum.ORCHESTRATION:
          return '编排';
        default:
          return '未知';
      }
    };

    const search = () => {
      fetchExePrograms();
    };

    const handleFileChange = (file) => {
      // 获取当前激活的表单
      let currentForm;
      switch (activeTab.value) {
        case 'exe':
          currentForm = exeForm.value;
          break;
        case 'rpa':
          currentForm = rpaForm.value;
          break;
        default:
          return;
      }

      // 设置文件
      currentForm.ProgramPackageFile = file.raw;

      // 如果程序名为空，则使用文件名（去掉.zip后缀）作为程序名
      if (!currentForm.programName) {
        const fileName = file.name;
        currentForm.programName = fileName.toLowerCase().endsWith('.zip') 
          ? fileName.slice(0, -4) // 移除.zip后缀
          : fileName;
      }

      // 更新文件列表
      fileList.value = [file];
    };

    return {
      exePrograms,
      dialogVisible,
      isEditMode,
      activeTab,
      exeForm,
      rpaForm,
      orchestrationForm,
      fileList,
      inputParametersKey,
      searchQuery,
      loading,
      currentPage,
      pageSize,
      total,
      formRules,
      handleTabClick,
      handleCancel,
      openDialog,
      deleteExeProgram,
      submitExeProgram,
      updateExeParameters,
      updateRpaParameters,
      updateOrchestrationParameters,
      getProgramTypeLabel,
      search,
      handleCurrentChange,
      handleSizeChange,
      handleFileChange,
      exeFormRef,
      rpaFormRef,
      orchestrationFormRef
    };
  },
};
</script>

<style scoped>
/* 只保留必要的样式 */
:deep(.custom-table) {
  --el-table-row-height: 50px;
}

:deep(.custom-table .el-button) {
  padding: 4px 12px;
}

:deep(.el-dialog) {
  --el-dialog-padding-primary: 20px;
}

:deep(.param-row) {
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 10px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 16px;
}
</style>
