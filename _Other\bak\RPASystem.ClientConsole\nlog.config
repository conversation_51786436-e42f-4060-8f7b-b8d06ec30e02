<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <targets>
    <target xsi:type="File" name="errorfile" fileName="${basedir}/Logs/Error/${shortdate}.log"
            layout="${longdate}|${level:uppercase=true}|${logger}|${message} ${exception:format=tostring}"
            maxArchiveFiles="10" archiveAboveSize="52428800" />
  </targets>

  <rules>
    <logger name="*" minlevel="Error" writeTo="errorfile" />
  </rules>
</nlog>
