<!--
 * @Descripttion: (${genTable.functionName}/${genTable.tableName})
 * @Author: (${replaceDto.Author})
 * @Date: (${replaceDto.AddTime})
-->
<template>
  <div>
    <el-form :model="queryParams" label-position="right" inline ref="queryRef" v-show="showSearch" @submit.prevent>
$foreach(column in genTable.Columns)
$set(labelName = "")
$set(columnName = "")
$set(numLabel = "")
$if(column.IsQuery == true)
$set(columnName = column.CsharpFieldFl)
$if(column.ColumnComment != "")
$set(labelName = column.ColumnComment)
$else
$set(labelName = column.CsharpFieldFl)
$end
$if(column.CsharpType == "int" || column.CsharpType == "long")
$set(numLabel = ".number")
$end
$if(column.HtmlType == "month")
      <el-form-item label="$labelName" prop="${columnName}">
        <el-date-picker
          v-model="queryParams.${columnName}"
          type="month"
          value-format="YYYY-MM-DD"
          placeholder="请选择$labelName">
        </el-date-picker>
      </el-form-item>
$elseif(column.HtmlType == "datetime")
      <el-form-item label="$labelName">
        <el-date-picker
          v-model="dateRange${column.CsharpField}" 
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="defaultTime"
          :shortcuts="dateOptions">
        </el-date-picker>
      </el-form-item>
$elseif(column.HtmlType == "datePicker")
      <el-form-item label="$labelName">
        <el-date-picker
          style="width: 200px"
          v-model="dateRange${column.CsharpField}" 
          type="daterange"
          start-placeholder="Start date"
          end-placeholder="End date"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="defaultTime">
        </el-date-picker>
      </el-form-item>
$elseif(column.HtmlType.Contains("select"))
      <el-form-item label="${labelName}" prop="${columnName}">
        <el-select clearable $if(column.HtmlType == "selectMulti")multiple$end v-model="queryParams.${columnName}" placeholder="请选择${labelName}">
          <el-option v-for="item in $if(column.DictType != "") options.${column.DictType} $else options.${column.CsharpFieldFl}Options$end" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue">
            <span class="fl">{{ item.dictLabel }}</span>
            <span class="fr" style="color: var(--el-text-color-secondary);">{{ item.dictValue }}</span>          
          </el-option>
        </el-select>
      </el-form-item>
$elseif(column.HtmlType == "radio")
      <el-form-item label="${labelName}" prop="${columnName}">
        <el-radio-group v-model="queryParams.${columnName}">
          <el-radio>全部</el-radio>
          <el-radio v-for="item in $if(column.DictType != "") options.${column.DictType} $else options.${column.CsharpFieldFl}Options$end" :key="item.dictValue" :value="item.dictValue">{{item.dictLabel}}</el-radio>
        </el-radio-group>
      </el-form-item>
$else
      <el-form-item label="${labelName}" prop="${columnName}">
        <el-input v-model${numLabel}="queryParams.${columnName}" placeholder="请输入${labelName}" />
      </el-form-item>
$end
$end
$end
      <el-form-item>
        <el-button icon="search" type="primary" @click="handleQuery">{{ ${t}t('btn.search') }}</el-button>
        <el-button icon="refresh" @click="resetQuery">{{ ${t}t('btn.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 工具区域 -->
    <el-row :gutter="15" class="mb10">
$if(replaceDto.ShowBtnAdd)
      <el-col :span="1.5">
        <el-button type="primary" v-hasPermi="['${replaceDto.PermissionPrefix}:add']" plain icon="plus" @click="handleAdd">
          {{ ${t}t('btn.add') }}
        </el-button>
      </el-col>
$end
$if(replaceDto.ShowBtnMultiDel)
      <el-col :span="1.5">
        <el-button type="success" :disabled="single" v-hasPermi="['${replaceDto.PermissionPrefix}:edit']" plain icon="edit" @click="handleUpdate">
          {{ ${t}t('btn.edit') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" :disabled="multiple" v-hasPermi="['${replaceDto.PermissionPrefix}:delete']" plain icon="delete" @click="handleDelete">
          {{ ${t}t('btn.delete') }}
        </el-button>
      </el-col>
$end
$if(replaceDto.ShowBtnTruncate)
      <el-col :span="1.5">
        <el-button type="danger" v-hasPermi="['${replaceDto.PermissionPrefix}:delete']" plain icon="delete" @click="handleClear">
          {{ ${t}t('btn.clean') }}
        </el-button>
      </el-col>
$end
$if(replaceDto.ShowBtnImport)
      <el-col :span="1.5">
        <el-dropdown trigger="click" v-hasPermi="['${replaceDto.PermissionPrefix}:import']">
          <el-button type="primary" plain icon="Upload">
            {{ ${t}t('btn.import') }}<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="upload">
                <importData
                  templateUrl="${genTable.ModuleName}/${genTable.BusinessName}/importTemplate"
                  importUrl="/${genTable.ModuleName}/${genTable.BusinessName}/importData"
                  @success="handleFileSuccess"></importData>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-col>
$end
$if(replaceDto.ShowBtnExport)
      <el-col :span="1.5">
        <el-button type="warning" plain icon="download" @click="handleExport" v-hasPermi="['${replaceDto.PermissionPrefix}:export']">
          {{ ${t}t('btn.export') }}
        </el-button>
      </el-col>
$end
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table
      :data="dataList"
      v-loading="loading"
      ref="table"
      border
      header-cell-class-name="el-table-header-cell"
      highlight-current-row
      @sort-change="sortChange"
$if(replaceDto.ShowBtnMultiDel)
      @selection-change="handleSelectionChange"
$end
      >
$if(replaceDto.ShowBtnMultiDel)
      <el-table-column type="selection" width="50" align="center"/>
$end
$if(sub)
      <el-table-column align="center" width="90">
        <template #default="scope">
          <el-button text @click="rowClick(scope.row)">{{ ${t}t('btn.details') }}</el-button>
        </template>
      </el-table-column>
$end
$foreach(column in genTable.Columns)
$set(labelName = column.ColumnComment)
$set(showToolTipHtml = "")
$set(columnName = column.CsharpFieldFl)
$if(column.CsharpType == "string" || column.HtmlType == "datetime")
$set(showToolTipHtml = " :show-overflow-tooltip=\"true\"")
$end
$if(column.IsList == true)
$if(column.HtmlType == "customInput" && column.IsPk == false)
      <el-table-column prop="${columnName}" label="${labelName}" width="90" sortable align="center" v-if="columns.showColumn('${columnName}')">
        <template #default="scope">
          <span v-show="editIndex != scope.$${index}index" @click="editCurrRow(scope.$${index}index)">{{scope.row.${columnName}}}</span>
          <el-input
            :ref="setColumnsRef"
            v-show="(editIndex == scope.$${index}index)" 
            v-model="scope.row.${columnName}" 
            @blur="handleChangeSort(scope.row)"></el-input>
        </template>
      </el-table-column>
$elseif(column.HtmlType == "imageUpload")
      <el-table-column prop="${columnName}" label="${labelName}" align="center" v-if="columns.showColumn('${columnName}')">
        <template #default="scope">
          <ImagePreview :src="scope.row.${columnName}"></ImagePreview>
        </template>
      </el-table-column>
$elseif(column.HtmlType == "checkbox" || column.HtmlType.Contains("select") || column.HtmlType == "radio")
      <el-table-column prop="${columnName}" label="${labelName}" align="center"${column.sortStr} v-if="columns.showColumn('${columnName}')">
        <template #default="scope">
          <dict-tag :options="$if(column.DictType != "") options.${column.DictType} $else options.${column.CsharpFieldFl}Options$end" :value="scope.row.${columnName}" $if(column.HtmlType == "checkbox" || column.HtmlType == "selectMulti")split=","$end />
        </template>
      </el-table-column>
$elseif(column.HtmlType == "datetime")
      <el-table-column prop="${columnName}" label="${labelName}" :show-overflow-tooltip="true" ${column.sortStr} v-if="columns.showColumn('${columnName}')"/>
$else
      <el-table-column prop="${columnName}" label="${labelName}" align="center"${showToolTipHtml}${column.sortStr} v-if="columns.showColumn('${columnName}')"/>
$end
$end
$end
      <el-table-column label="操作" width="160">
        <template #default="scope">
$if(replaceDto.OperBtnStyle == 2)
        <el-button-group>
$if(replaceDto.ShowBtnView)
          <el-button text type="primary" icon="view" @click="handlePreview(scope.row)"></el-button>
$end
$if(replaceDto.ShowBtnEdit)
          <el-button text v-hasPermi="['${replaceDto.PermissionPrefix}:edit']" type="success" icon="edit" title="编辑" @click="handleUpdate(scope.row)"></el-button>
$end
$if(replaceDto.ShowBtnDelete)
          <el-button text v-hasPermi="['${replaceDto.PermissionPrefix}:delete']" type="danger" icon="delete" title="删除" @click="handleDelete(scope.row)"></el-button>
$end
        </el-button-group>
$else
$if(replaceDto.ShowBtnView)
          <el-button type="primary" size="small" icon="view" title="详情" @click="handlePreview(scope.row)"></el-button>
$end
$if(replaceDto.ShowBtnEdit)
          <el-button type="success" size="small" icon="edit" title="编辑" v-hasPermi="['${replaceDto.PermissionPrefix}:edit']" @click="handleUpdate(scope.row)"></el-button>
$end
$if(replaceDto.ShowBtnDelete)
          <el-button type="danger" size="small" icon="delete" title="删除" v-hasPermi="['${replaceDto.PermissionPrefix}:delete']" @click="handleDelete(scope.row)"></el-button>
$end
$end
        </template>
      </el-table-column>
    </el-table>
    <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

$* 一对一/一对多列表显示详情 *$
$if(sub)
    <el-drawer v-model="drawer" size="50%" direction="rtl">
      <el-table :data="$tool.FirstLowerCase(genTable.SubTable.ClassName)List" header-row-class-name="text-navy">
        <el-table-column label="序号" type="index" width="80" />
$foreach(column in genSubTable.Columns)
$set(columnName = column.CsharpFieldFl)
$if(column.IsList == true)
$if(column.HtmlType == "checkbox" || column.HtmlType.Contains("select") || column.HtmlType == "radio")
        <el-table-column prop="${columnName}" label="${column.ColumnComment}">
          <template #default="scope">
            <dict-tag :options="$if(column.DictType != "") options.${column.DictType} $else options.${column.CsharpFieldFl}Options$end" :value="scope.row.${columnName}" $if(column.HtmlType == "checkbox")split=","$end />
          </template>
        </el-table-column>
$else
        <el-table-column prop="${column.CsharpFieldFl}" label="${column.ColumnComment}"/>
$end
$end
$end
      </el-table>
    </el-drawer>
$end

    <el-dialog :title="title" :lock-scroll="false" v-model="open" ${if(sub)}:fullscreen="fullScreen"$end>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
$foreach(column in genTable.Columns)
$set(columnName = column.CsharpFieldFl)
$set(value = "item.dictValue")
$set(number = "")
$set(labelName = column.ColumnComment)
$if(column.CsharpType == "int" || column.CsharpType == "long")
    $set(value = "parseInt(item.dictValue)")
    $set(number = ".number")
    $set(switchType = ":active-value='1' :inactive-value='0'")
$end

$if(column.IsPK || column.IsIncrement)
$if(column.IsPK && column.IsIncrement == false && replaceDto.useSnowflakeId == false)
          <el-col :lg="${options.ColNum}">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-input v-model${number}="form.${columnName}" placeholder="请输入${labelName}" :disabled="opertype != 1"/>
            </el-form-item>
          </el-col>
$else
          <el-col :lg="${options.ColNum}" v-if="opertype != 1">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-input-number v-model.number="form.${columnName}" controls-position="right" placeholder="请输入${labelName}" :disabled="true"/>
            </el-form-item>
          </el-col>
$end
$else
$if(column.HtmlType == "inputNumber" || column.HtmlType == "customInput")
          <el-col :lg="${options.ColNum}">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-input-number v-model.number="form.${columnName}" :controls="true" controls-position="right" placeholder="请输入${labelName}" ${column.DisabledStr}/>
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "datetime" || column.HtmlType == "month")
          <el-col :lg="${options.ColNum}">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-date-picker
                v-model="form.${columnName}"
                type="datetime"
                placeholder="选择日期时间"${column.DisabledStr}
                value-format="YYYY-MM-DD HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "imageUpload")
          <el-col :lg="24">
            <el-form-item label="${labelName}" prop="${columnName}">
              <UploadImage v-model="form.${columnName}" :data="{ uploadType: 1 }" />
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "fileUpload")
          <el-col :lg="24">
            <el-form-item label="${labelName}" prop="${columnName}">
              <UploadFile v-model="form.${columnName}" :data="{ uploadType: 1 }" />
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "radio" || column.HtmlType == "selectRadio")
          <el-col :lg="${options.ColNum}">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-radio-group v-model="form.${columnName}"${column.DisabledStr}>
                <el-radio v-for="item in ${if(column.DictType != "")}options.${column.DictType}${else}options.${column.CsharpFieldFl}Options$end" :key="item.dictValue" :value="${value}">
                  {{item.dictLabel}}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "textarea")
          <el-col :lg="24">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-input type="textarea" v-model="form.${columnName}" placeholder="请输入${labelName}"${column.DisabledStr}/>
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "editor")
          <el-col :lg="24">
            <el-form-item label="${labelName}" prop="${columnName}">
              <editor v-model="form.${columnName}" :min-height="200" />
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "slider")
          <el-col :lg="24">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-slider v-model="form.${columnName}" />
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "select" || column.HtmlType == "selectMulti")
          <el-col :lg="${options.ColNum}">
            <el-form-item label="${labelName}" prop="${columnName}$if(column.HtmlType == "selectMulti")Checked$end">
              <el-select v-model="form.${columnName}$if(column.HtmlType == "selectMulti")Checked$end" $if(column.HtmlType == "selectMulti")multiple$end placeholder="请选择${labelName}"${column.DisabledStr}>
                <el-option
                  v-for="item in $if(column.DictType != "")options.${column.DictType}${else}options.${column.CsharpFieldFl}Options$end" 
                  :key="item.dictValue" 
                  :label="item.dictLabel" 
                  :value="${value}"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "checkbox")
          <el-col :lg="24">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-checkbox-group v-model="form.${columnName}Checked"${column.DisabledStr}>
                <el-checkbox v-for="item in $if(column.DictType != "")options.${column.DictType}${else}options.${column.CsharpFieldFl}Options$end" :key="item.dictValue" :label="item.dictValue">
                  {{item.dictLabel}}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "colorPicker")
          <el-col :lg="12">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-color-picker v-model="form.${columnName}" />
            </el-form-item>
          </el-col>
$elseif(column.HtmlType == "switch")
          <el-col :lg="12">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-switch v-model="form.${columnName}" ${switchType} />
            </el-form-item>
          </el-col>
$else
          <el-col :lg="${options.ColNum}">
            <el-form-item label="${labelName}" prop="${columnName}">
              <el-input v-model${number}="form.${columnName}" placeholder="请输入${labelName}" ${column.DisabledStr}/>
            </el-form-item>
          </el-col>
$end
$end
$end
        </el-row>
$* 子表信息 *$
$if(sub)
        <el-divider content-position="center">${genTable.SubTable.FunctionName}信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="Plus" @click="handleAdd${genTable.SubTable.ClassName}">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="Delete" @click="handleDelete${genTable.SubTable.ClassName}">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" icon="FullScreen" @click="fullScreen = !fullScreen">{{ fullScreen ? '退出全屏' : '全屏' }}</el-button>
          </el-col>
        </el-row>
        <el-table :data="${tool.FirstLowerCase(genTable.SubTable.ClassName)}List" :row-class-name="row${genTable.SubTable.ClassName}Index" @selection-change="handle${genTable.SubTable.ClassName}SelectionChange" ref="${genTable.SubTable.ClassName}Ref">
          <el-table-column type="selection" width="40" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
$foreach(column in genTable.SubTable.Columns)
$set(labelName = column.ColumnComment)
$set(columnName = column.CsharpFieldFl)
$set(value = "item.dictValue")
$if(column.CsharpType == "int" || column.CsharpType == "long")
$set(value = "parseInt(item.dictValue)")
$set(number = ".number")
$set(switchType = ":active-value='1' :inactive-value='0'")
$end
$if(column.IsList == true)
$if(column.IsPk || column.CsharpField == genTable.SubTableFkName)
$elseif(column.HtmlType == "inputNumber" || column.HtmlType == "customInput")
          <el-table-column label="${labelName}" align="center" prop="${columnName}" width="140">
            <template #default="scope">
              <el-input-number v-model="scope.row.${columnName}" controls-position="right" placeholder="请输入${labelName}" />
            </template>
          </el-table-column>
$elseif(column.HtmlType == "datetime" || column.HtmlType == "month")
          <el-table-column label="${labelName}" align="center" prop="${columnName}">
            <template #default="scope">
              <el-date-picker clearable v-model="scope.row.${columnName}" type="date" placeholder="选择日期时间"></el-date-picker>
            </template>
          </el-table-column>
$elseif(column.HtmlType == "radio" || column.HtmlType == "selectRadio" || column.HtmlType == "select" || column.HtmlType == "selectMulti")
          <el-table-column label="${labelName}" prop="${columnName}">
            <template #default="scope">
              <el-select v-model="scope.row.${columnName}" placeholder="请选择${labelName}"${column.DisabledStr}>
                <el-option
                  v-for="item in $if(column.DictType != "")options.${column.DictType}${else}options.${column.CsharpFieldFl}Options$end" 
                  :key="item.dictValue" 
                  :label="item.dictLabel" 
                  :value="${value}"></el-option>
              </el-select>
            </template>
          </el-table-column>
$elseif(column.HtmlType == "switch")
          <el-table-column label="${labelName}" prop="${columnName}">
            <template #default="scope">
              <el-switch v-model="scope.row.${columnName}" ${switchType} />
            </template>
          </el-table-column>
$elseif(column.HtmlType == "imageUpload")
          <el-table-column label="${labelName}" prop="${columnName}">
            <template #default="scope">
              <UploadImage v-model="form.${columnName}" :isShowTip="false" :style="{ 'width': '50px' }" :data="{ uploadType: 1 }" />
            </template>
          </el-table-column>
$else
          <el-table-column label="${labelName}" align="center" prop="${columnName}">
            <template #default="scope">
              <el-input v-model="scope.row.${columnName}" placeholder="请输入${labelName}" />
            </template>
          </el-table-column>
$end
$end
$end
        </el-table>
$end
      </el-form>
      <template #footer v-if="opertype != 3">
        <el-button text @click="cancel">{{ ${t}t('btn.cancel') }}</el-button>
$if(replaceDto.ShowBtnEdit || replaceDto.ShowBtnAdd)
        <el-button type="primary" @click="submitForm">{{ ${t}t('btn.submit') }}</el-button>
$end
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="${genTable.BusinessName.ToLower()}">
import { list${genTable.BusinessName},
$if(replaceDto.ShowBtnAdd) add${genTable.BusinessName}, $end
$if(replaceDto.ShowBtnDelete || replaceDto.ShowBtnMultiDel)del${genTable.BusinessName},$end 
$if(replaceDto.ShowBtnEdit) update${genTable.BusinessName},$end
get${genTable.BusinessName}, 
$if(replaceDto.ShowBtnTruncate) clear${genTable.BusinessName}, $end
$if(showCustomInput) changeSort $end } 
from '@/api/${tool.FirstLowerCase(genTable.ModuleName)}/${genTable.BusinessName.ToLower()}.js'
$if(replaceDto.ShowEditor == 1)
import Editor from '@/components/Editor'
$end
$if(replaceDto.ShowBtnImport)
import importData from '@/components/ImportData'
$end
const { proxy } = getCurrentInstance()
const ids = ref([])
const loading = ref(false)
const showSearch = ref(true)
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  sort: '${genTable.Options.SortField}',
  sortType: '${genTable.Options.SortType}',
$foreach(item in genTable.Columns)
$if(item.IsQuery == true)
  ${item.CsharpFieldFl}: undefined,
$end
$end
})
const columns = ref([
$set(index = 0)
$foreach(column in genTable.Columns)
$set(index = index + 1)
$set(showToolTipHtml = "")
$set(type = "")
$set(align = "center")
$if(column.CsharpType == "string" || column.HtmlType == "datetime")
$set(showToolTipHtml = " ,showOverflowTooltip: true")
$end
$if(column.HtmlType == "imageUpload")
$set(type = "img")
$set(align = "center")
$elseif(column.HtmlType == "checkbox" || column.HtmlType.Contains("select") || column.HtmlType == "radio")
$set(type = "dict")
$set(align = "center")
$end
$if(column.IsList)
  { visible: ${if(index < 9)}true${else}false${end}, align: '${align}', type: '${type}', prop: '${column.CsharpFieldFl}', label: '${column.ColumnComment}' ${showToolTipHtml} $if(column.DictType != "") ,dictType: '${column.DictType}'$end },
$end
$end
  //{ visible: false, prop: 'actions', label: '操作', type: 'slot', width: '160' }
])
const total = ref(0)
const dataList = ref([])
const queryRef = ref()
const defaultTime = ref([new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)])

$foreach(item in genTable.Columns)
$if((item.HtmlType == "datetime" || item.HtmlType == "datePicker") && item.IsQuery == true)
// ${item.ColumnComment}时间范围
const dateRange${item.CsharpField} = ref([])

$elseif(item.HtmlType == "customInput")
/******************自定义编辑 start **********************/
const editIndex = ref(-1)
const columnRefs = ref([])
const setColumnsRef = (el) => {
  if (el) {
    columnRefs.value.push(el)
  }
}
// 显示编辑排序
function editCurrRow(index) {
  editIndex.value = index

  setTimeout(() => {
    columnRefs.value[index].focus()
  }, 100)
}
// 保存排序
function handleChangeSort(info) {
  editIndex.value = -1
  proxy
    .${confirm}confirm('是否保存数据?')
    .then(function () {
      return changeSort({ value: info.${item.CsharpFieldFl}, id: info.${replaceDto.FistLowerPk} })
    })
    .then(() => {
      handleQuery()
      proxy.${modal}modal.msgSuccess('修改成功')
    })
}
/******************自定义编辑 end **********************/

$end
$end

$set(index = 0)
var dictParams = [
$foreach(item in dicts)
$if(item.DictType != "")
  "${item.DictType}",
$set(index = index + 1)
$end
$end
]

$if(index > 0)
proxy.getDicts(dictParams).then((response) => {
  response.data.forEach((element) => {
    state.options[element.dictType] = element.list
  })
})
$end

function getList(){
$foreach(item in genTable.Columns)
$if((item.HtmlType == "datetime" || item.HtmlType == "datePicker") && item.IsQuery == true)
  proxy.addDateRange(queryParams, dateRange${item.CsharpField}.value, '${item.CsharpField}');
$end
$end
  loading.value = true
  list${genTable.BusinessName}(queryParams).then(res => {
    const { code, data } = res
    if (code == 200) {
      dataList.value = data.result
      total.value = data.totalNum
      loading.value = false
    }
  })
}

// 查询
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

// 重置查询操作
function resetQuery(){
$foreach(item in genTable.Columns)
$if((item.HtmlType == "datetime" || item.HtmlType == "datePicker") && item.IsQuery == true)
  // ${item.ColumnComment}时间范围
  dateRange${item.CsharpField}.value = []
$end
$end
  proxy.resetForm("queryRef")
  handleQuery()
}
$if(replaceDto.ShowBtnMultiDel)
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.${replaceDto.FistLowerPk});
  single.value = selection.length != 1
  multiple.value = !selection.length;
}
$end
// 自定义排序
function sortChange(column) {
  var sort = undefined
  var sortType = undefined

  if (column.prop != null && column.order != null) {
    sort = column.prop
    sortType = column.order

$foreach(item in genTable.Columns)
$if(item.IsSort && item.CsharpField.ToLower() != item.ColumnName.ToLower())
    if (column.prop == '${item.CsharpFieldFl}') {
      sort = '${item.ColumnName}'
    }
$end
${end}
  }
  queryParams.sort = sort
  queryParams.sortType = sortType
  handleQuery()
}

/*************** form操作 ***************/
const formRef = ref()
const title = ref('')
// 操作类型 1、add 2、edit 3、view
const opertype = ref(0)
const open = ref(false)
const state = reactive({
  single: true,
  multiple: true,
  form: {},
  rules: {
$foreach(column in genTable.Columns)
$if(column.IsRequired && column.IsPk && replaceDto.useSnowflakeId)
    ${column.CsharpFieldFl}: [{ required: true, message: "${column.ColumnComment}不能为空", trigger: $if(column.htmlType == "select")"change"$else"blur"$end }],
$elseif(column.IsRequired && column.IsIncrement == false)
    ${column.CsharpFieldFl}$if(column.HtmlType == "selectMulti")Checked$end: [{ required: true, message: "${column.ColumnComment}不能为空", trigger: $if(column.htmlType == "select")"change"$else"blur"$end
    $if(column.CsharpType == "int" || column.CsharpType == "long"), type: "number" $end }],
$end
$end
  },
  options: {
$foreach(column in dicts)
$if(column.HtmlType == "radio" || column.HtmlType.Contains("select") || column.HtmlType == "checkbox")
    //$if(column.ColumnComment != "") ${column.ColumnComment} $else ${column.CsharpFieldFl}$end选项列表 格式 eg:{ dictLabel: '标签', dictValue: '0'}
    $if(column.DictType != "")${column.DictType}$else${column.CsharpFieldFl}Options$end: [],
$end
$end
  }
})

const { form, rules, options, single, multiple } = toRefs(state)

// 关闭dialog
function cancel(){
  open.value = false
  reset()
}

// 重置表单
function reset() {
  form.value = {
$foreach(item in genTable.Columns)
$if(item.HtmlType == "checkbox" || item.HtmlType == "selectMulti")
    ${item.CsharpFieldFl}Checked: [],
$else
    $item.CsharpFieldFl: null,
$end
$end
  };
$if(sub)
  ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List.value = []
$end
  proxy.resetForm("formRef")
}

$if(replaceDto.ShowBtnView)
/**
 * 查看
 * @param {*} row
 */
function handlePreview(row) {
  reset()
    const id = row.${replaceDto.FistLowerPk}
    get${genTable.BusinessName}(id).then((res) => {
    const { code, data } = res
    if (code == 200) {
      open.value = true
      title.value = '查看'
      opertype.value = 3
      form.value = {
        ...data,
$foreach(item in genTable.Columns)
$if(item.HtmlType == "checkbox" || item.HtmlType == "selectMulti")
        ${item.CsharpFieldFl}Checked: data.${item.CsharpFieldFl} ? data.${item.CsharpFieldFl}.split(',') : [],
$end
$end
      }
$if(sub)
      ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List.value = res.data.${tool.FirstLowerCase(genTable.SubTable.ClassName)}Nav
$end
    }
  })
}
$end

$if(replaceDto.ShowBtnAdd)
// 添加按钮操作
function handleAdd() {
  reset();
  open.value = true
  title.value = '添加${genTable.functionName}'
  opertype.value = 1
}
$end
$if(replaceDto.ShowBtnEdit)
// 修改按钮操作
function handleUpdate(row) {
  reset()
  const id = row.${replaceDto.FistLowerPk} || ids.value
  get${genTable.BusinessName}(id).then((res) => {
    const { code, data } = res
    if (code == 200) {
      open.value = true
      title.value = '修改${genTable.functionName}'
      opertype.value = 2

      form.value = {
        ...data,
$foreach(item in genTable.Columns)
$if(item.HtmlType == "checkbox" || item.HtmlType == "selectMulti")
        ${item.CsharpFieldFl}Checked: data.${item.CsharpFieldFl} ? data.${item.CsharpFieldFl}.split(',') : [],
$end
$end
      }
$if(sub)
      ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List.value = res.data.${tool.FirstLowerCase(genTable.SubTable.ClassName)}Nav
$end
    }
  })
}
$end

// 添加&修改 表单提交
function submitForm() {
  proxy.${refs}refs["formRef"].validate((valid) => {
    if (valid) {
$foreach(item in genTable.Columns)
$if(item.HtmlType == "checkbox" || item.HtmlType == "selectMulti")
      form.value.${item.CsharpFieldFl} = form.value.${item.CsharpFieldFl}Checked.toString();
$end
$end

$if(sub)
      form.value.${tool.FirstLowerCase(genTable.SubTable.ClassName)}Nav = ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List.value
$end
      if (form.value.${replaceDto.FistLowerPk} != undefined && opertype.value === 2) {
$if(replaceDto.ShowBtnEdit)
        update${genTable.BusinessName}(form.value).then((res) => {
          proxy.${modal}modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
$end
      } else {
$if(replaceDto.ShowBtnAdd)
        add${genTable.BusinessName}(form.value).then((res) => {
            proxy.${modal}modal.msgSuccess("新增成功")
            open.value = false
            getList()
          })
$end
      }
    }
  })
}

$if(replaceDto.ShowBtnMultiDel || replaceDto.ShowBtnDelete)
// 删除按钮操作
function handleDelete(row) {
  const Ids = row.${replaceDto.FistLowerPk} || ids.value

  proxy
    .${confirm}confirm('是否确认删除参数编号为"' + Ids + '"的数据项？', "警告", {
      confirmButtonText: proxy.${t}t('common.ok'),
      cancelButtonText: proxy.${t}t('common.cancel'),
      type: "warning",
    })
    .then(function () {
      return del${genTable.BusinessName}(Ids)
    })
    .then(() => {
      getList()
      proxy.${modal}modal.msgSuccess("删除成功")
    })
}
$end

$if(replaceDto.ShowBtnTruncate)
// 清空
function handleClear() {
  proxy
    .${confirm}confirm("是否确认清空所有数据项?", "警告", {
      confirmButtonText: proxy.${t}t('common.ok'),
      cancelButtonText: proxy.${t}t('common.cancel'),
      type: "warning",
    })
    .then(function () {
      return clear${genTable.BusinessName}()
    })
    .then(() => {
      handleQuery()
      proxy.${modal}modal.msgSuccess('清空成功')
    })
}
$end

$if(replaceDto.ShowBtnImport)
// 导入数据成功处理
const handleFileSuccess = (response) => {
  const { item1, item2 } = response.data
  var error = ''
  item2.forEach((item) => {
    error += item.storageMessage + ','
  })
  proxy.${alert}alert(item1 + '<p>' + error + '</p>', '导入结果', {
    dangerouslyUseHTMLString: true
  })
  getList()
}
$end

$if(replaceDto.ShowBtnExport)
// 导出按钮操作
function handleExport() {
  proxy
    .${confirm}confirm("是否确认导出${genTable.functionName}数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(async () => {
      await proxy.downFile('/${genTable.ModuleName}/${genTable.BusinessName}/export', { ...queryParams })
    })
}
$end

$if(sub)
/*********************${genTable.SubTable.FunctionName}子表信息*************************/
const ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List = ref([])
const checked${genTable.SubTable.ClassName} = ref([])
const fullScreen = ref(false)
const drawer = ref(false)

/** ${genTable.SubTable.FunctionName}序号 */
function row${genTable.SubTable.ClassName}Index({ row, rowIndex }) {
  row.index = rowIndex + 1;
}

/** ${genTable.SubTable.FunctionName}添加按钮操作 */
function handleAdd${genTable.SubTable.ClassName}() {
  let obj = {};
  //下面的代码自己设置默认值
$foreach(column in genTable.SubTable.Columns)
$if(column.IsPK || column.CsharpField == genTable.SubTableFkName)
$elseif(column.IsList == true && "" != column.CsharpField)
  //obj.${column.CsharpFieldFl} = null;
$end
$end
  ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List.value.push(obj);
}

/** 复选框选中数据 */
function handle${genTable.SubTable.ClassName}SelectionChange(selection) {
  checked${genTable.SubTable.ClassName}.value = selection.map(item => item.index)
}

/** ${genTable.SubTable.FunctionName}删除按钮操作 */
function handleDelete${genTable.SubTable.ClassName}() {
  if(checked${genTable.SubTable.ClassName}.value.length == 0){
    proxy.${modal}modal.msgError('请先选择要删除的${genTable.SubTable.FunctionName}数据')
  } else {
    const ${genTable.SubTable.ClassName}s = ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List.value;
    const checked${genTable.SubTable.ClassName}s = checked${genTable.SubTable.ClassName}.value;
    ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List.value = ${genTable.SubTable.ClassName}s.filter(function(item) {
      return checked${genTable.SubTable.ClassName}s.indexOf(item.index) == -1
    });
  }
}

/** ${genTable.SubTable.FunctionName}详情 */
function rowClick(row) {
  const id = row.${replaceDto.FistLowerPk} || ids.value
  get${genTable.BusinessName}(id).then((res) => {
    const { code, data } = res
    if (code == 200) {
      drawer.value = true
      ${tool.FirstLowerCase(genTable.SubTable.ClassName)}List.value = data.${tool.FirstLowerCase(genTable.SubTable.ClassName)}Nav
    }
  })
}
$end
handleQuery()
</script>