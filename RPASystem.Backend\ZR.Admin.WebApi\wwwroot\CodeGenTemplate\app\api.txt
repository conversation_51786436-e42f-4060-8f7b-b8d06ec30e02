﻿import request from '@/utils/request'

/**
* ${genTable.functionName}分页查询
* @param {查询条件} data
*/
export function list${genTable.BusinessName}(query) {
  return request({
    url: '/${genTable.ModuleName}/${genTable.BusinessName}/list',
    method: 'get',
    data: query,
  })
}

$if(replaceDto.ShowBtnAdd)
/**
* 新增${genTable.functionName}
* @param data
*/
export function add${genTable.BusinessName}(data) {
  return request({
    url: '/${genTable.ModuleName}/${genTable.BusinessName}',
    method: 'post',
    data: data,
  })
}
$end
$if(replaceDto.ShowBtnEdit)
/**
* 修改${genTable.functionName}
* @param data
*/
export function update${genTable.BusinessName}(data) {
  return request({
    url: '/${genTable.ModuleName}/${genTable.BusinessName}',
    method: 'PUT',
    data: data,
  })
}
$end
/**
* 获取${genTable.functionName}详情
* @param {Id}
*/
export function get${genTable.BusinessName}(id) {
  return request({
    url: '/${genTable.ModuleName}/${genTable.BusinessName}/' + id,
    method: 'get'
  })
}

$if(replaceDto.ShowBtnDelete || replaceDto.ShowBtnMultiDel)
/**
* 删除${genTable.functionName}
* @param {主键} pid
*/
export function del${genTable.BusinessName}(pid) {
  return request({
    url: '/${genTable.ModuleName}/${genTable.BusinessName}/delete/' + pid,
    method: 'delete'
  })
}
$end