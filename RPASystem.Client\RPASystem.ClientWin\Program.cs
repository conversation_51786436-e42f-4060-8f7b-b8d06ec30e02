﻿using NLog;
using System;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace RPASystem.Client
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            LogManager.Setup().LoadConfigurationFromFile("nlog.config");

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // 检查命令行参数和环境变量
            // bool shouldHide = args.Contains("-hide", StringComparer.OrdinalIgnoreCase);

            var mainForm = new MainForm();
            Application.Run(mainForm);
        }
    }
}
