﻿using RPASystem.Model;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;
using RPASystem.Service;
using Infrastructure.Attribute;
using System.IO;
using Microsoft.AspNetCore.Http;
using System.IO.Compression;

namespace RPASystem.Service
{

    [AppService(ServiceType = typeof(IExeProgramService), ServiceLifetime = LifeTime.Scoped)]
    public class ExeProgramService : IExeProgramService
    {
        private readonly RPASystemDbContext dbContext;
        private readonly string programPackageBasePath;

        public ExeProgramService(RPASystemDbContext dbContext)
        {
            this.dbContext = dbContext;
            // 设置程序包存储的基础路径
            this.programPackageBasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Updates", "ProgramPackage");
            // 确保目录存在
            if (!Directory.Exists(programPackageBasePath))
            {
                Directory.CreateDirectory(programPackageBasePath);
            }
        }

        public IEnumerable<object> GetAllExeProgramsForList()
        {
            // 只查询前端需要的字段
            return dbContext.ExePrograms
                .Select(p => new
                {
                    id = p.ID,                    // 注意：前端用的是小写的id
                    programName = p.ProgramName,
                    inputParameters = p.InputParameters,
                    programType = p.ProgramType,
                    resourceSelection = p.ResourceSelection,
                    version = p.Version,           // 添加版本号字段
                    isExclusive = p.IsExclusive,  // 添加其他必要字段
                    remarks = p.Remarks,
                    createdAt = p.CreatedAt,       // 添加创建时间
                    updatedAt = p.UpdatedAt        // 添加最后更新时间
                })
                .ToList();
        }

        public ExeProgram GetExeProgramById(long id)
        {
            return dbContext.ExePrograms.FirstOrDefault(p => p.ID == id);
        }

        private string GetNextVersion(string currentVersion)
        {
            if (string.IsNullOrEmpty(currentVersion))
            {
                return "*******";
            }

            var versionParts = currentVersion.Split('.').Select(int.Parse).ToArray();
            if (versionParts.Length != 4)
            {
                return "*******";
            }

            // 增加最后一位，如果超过9则进位
            versionParts[3]++;
            if (versionParts[3] > 9)
            {
                versionParts[3] = 0;
                versionParts[2]++;
                if (versionParts[2] > 9)
                {
                    versionParts[2] = 0;
                    versionParts[1]++;
                    if (versionParts[1] > 9)
                    {
                        versionParts[1] = 0;
                        versionParts[0]++;
                    }
                }
            }

            return string.Join(".", versionParts);
        }

        private string? GetVersionFromZipFile(IFormFile zipFile)
        {
            try
            {
                using (var stream = zipFile.OpenReadStream())
                using (var archive = new ZipArchive(stream, ZipArchiveMode.Read))
                {
                    // 查找Version.dat文件
                    var versionEntry = archive.GetEntry("Version.dat");
                    if (versionEntry != null)
                    {
                        using (var reader = new StreamReader(versionEntry.Open()))
                        {
                            var version = reader.ReadToEnd().Trim();
                            // 验证版本号格式是否正确 (x.x.x.x)
                            if (Regex.IsMatch(version, @"^\d+\.\d+\.\d+\.\d+$"))
                            {
                                return version;
                            }
                        }
                    }
                }
            }
            catch
            {
                // 如果读取失败，返回null使用默认逻辑
            }
            return null;
        }

        public void SaveExeProgram(ExeProgram exeProgram, IFormFile? programPackageFile = null)
        {
            // 如果ID存在则更新，否则新增
            if (exeProgram.ID > 0)
            {
                var existingProgram = dbContext.ExePrograms.AsNoTracking().FirstOrDefault(p => p.ID == exeProgram.ID);
                if (existingProgram != null)
                {
                    // 如果上传了新的程序包，则更新版本号
                    if (programPackageFile != null)
                    {
                        // 尝试从zip文件中读取版本号
                        string? versionFromZip = GetVersionFromZipFile(programPackageFile);
                        if (versionFromZip != null)
                        {
                            exeProgram.Version = versionFromZip;
                        }
                        else
                        {
                            // 如果没有找到Version.dat或格式不正确，使用原有的版本号递增逻辑
                            var currentVersion = new Version(existingProgram.Version);
                            exeProgram.Version = new Version(currentVersion.Major, currentVersion.Minor, currentVersion.Build, currentVersion.Revision + 1).ToString();
                        }
                        
                        // 删除旧文件
                        if (!string.IsNullOrEmpty(existingProgram.ProgramPackage) && File.Exists(existingProgram.ProgramPackage))
                        {
                            File.Delete(existingProgram.ProgramPackage);
                        }
                    }
                    else
                    {
                        // 如果没有上传新的程序包，保持原版本号和文件路径
                        exeProgram.Version = existingProgram.Version;
                        exeProgram.ProgramPackage = existingProgram.ProgramPackage;
                    }
                }
                exeProgram.UpdatedAt = DateTime.Now;
                dbContext.ExePrograms.Update(exeProgram);
            }
            else
            {
                // 新增程序时
                if (programPackageFile != null)
                {
                    // 尝试从zip文件中读取版本号
                    string? versionFromZip = GetVersionFromZipFile(programPackageFile);
                    exeProgram.Version = versionFromZip ?? "*******"; // 如果没有找到Version.dat，使用默认版本号
                }
                else
                {
                    exeProgram.Version = "*******";
                }
                exeProgram.UpdatedAt = DateTime.Now;
                exeProgram.CreatedAt = DateTime.Now;
                dbContext.ExePrograms.Add(exeProgram);
            }

            // 如果有新的程序包文件，直接保存到文件系统
            if (programPackageFile != null)
            {
                string filePath = Path.Combine(programPackageBasePath, $"{exeProgram.ProgramName}.zip");
                // 如果文件已存在，先删除
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                
                // 直接将文件流写入磁盘
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    programPackageFile.CopyTo(fileStream);
                }
                
                // 更新数据库中存储的路径
                exeProgram.ProgramPackage = filePath;
            }

            dbContext.SaveChanges();
        }

        public void DeleteExeProgram(long id)
        {
            var exeProgram = dbContext.ExePrograms.FirstOrDefault(p => p.ID == id);
            if (exeProgram != null)
            {
                // 删除本地文件
                if (!string.IsNullOrEmpty(exeProgram.ProgramPackage) && File.Exists(exeProgram.ProgramPackage))
                {
                    File.Delete(exeProgram.ProgramPackage);
                }

                dbContext.ExePrograms.Remove(exeProgram);
                dbContext.SaveChanges();
            }
        }

        public IEnumerable<object> SearchExePrograms(string query)
        {
            var programs = dbContext.ExePrograms
                .Where(p => string.IsNullOrEmpty(query) || p.ProgramName.Contains(query))
                .Select(p => new
                {
                    id = p.ID,
                    programName = p.ProgramName,
                    inputParameters = p.InputParameters,
                    programType = p.ProgramType,
                    resourceSelection = p.ResourceSelection,
                    version = p.Version,           // 添加版本号字段
                    isExclusive = p.IsExclusive,  // 添加其他必要字段
                    remarks = p.Remarks,
                    createdAt = p.CreatedAt,       // 添加创建时间
                    updatedAt = p.UpdatedAt        // 添加最后更新时间
                })
                .ToList();
            return programs;
        }

        public ExeProgram GetExeProgramByName(string programName)
        {
            return dbContext.ExePrograms.FirstOrDefault(p => p.ProgramName == programName);
        }

        /// <summary>
        /// 获取所有资源（资源池和资源机）
        /// </summary>
        public object GetAllResources()
        {
            var resourcePools = dbContext.ResourcePools
                .Select(p => new { 
                    id = p.ID,
                    poolName = p.PoolName,
                    isPool = true
                })
                .ToList();

            var resourceMachines = dbContext.ResourceMachines
                .Select(m => new { 
                    id = m.Id,
                    machineName = m.MachineName,
                    isPool = false
                })
                .ToList();

            return new { 
                resourcePools = resourcePools,
                resourceMachines = resourceMachines
            };


        }

        public byte[] DownloadExeProgram(string programName)
        {
            var exeProgram = dbContext.ExePrograms.FirstOrDefault(p => p.ProgramName == programName);
            if (exeProgram != null && !string.IsNullOrEmpty(exeProgram.ProgramPackage) && File.Exists(exeProgram.ProgramPackage))
            {
                return File.ReadAllBytes(exeProgram.ProgramPackage);
            }
            return null;
        }
    }
}
