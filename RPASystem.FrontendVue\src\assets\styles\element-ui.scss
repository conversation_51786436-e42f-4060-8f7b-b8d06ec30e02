// cover some element-ui styles

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

// table 里面操作按钮样式覆盖
.el-table__cell .cell {
  .el-button.is-text {
    padding: 8px 5px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}
// fix 2.2.0 版本label标签不居中
// .el-form-item--small .el-form-item__label {
//   line-height: 32px !important;
// }

// element ui 移动端组件适配
.el-icon {
  vertical-align: middle;
}

.el-header {
  --el-header-padding: 0 0px !important;
  // --el-header-height: 50px !important;
}
// el 2.2.0 text button
.el-button.is-text {
  color: var(--el-color-primary) !important;
}
@media screen and (max-width: 500px) {
  .el-message {
    min-width: 300px !important;
  }
}

@media screen and (max-width: 500px) {
  .el-message-box {
    width: 300px !important;
  }

  .el-pagination__jump {
    display: none !important;
  }

  .el-pagination__sizes {
    display: none !important;
  }
}

// dialog
@media screen and (max-width: 700px) {
  .el-dialog {
    --el-dialog-width: 100% !important;
    // --el-dialog-margin-top: 0 !important;
  }
  // .el-dialog:not(.is-fullscreen) {
  //   margin-top: 0 !important;
  // }
  .el-drawer {
    width: 85% !important;
  }
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  margin-left: 5px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}

//适配完毕

// 隐藏picture-card 上传按钮
.hide .el-upload--picture-card {
  display: none;
}

// 禁止菜单选中
.el-sub-menu .el-sub-menu__title span,
.el-menu .el-menu-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

// fix 侧边栏二级导航缩小图标消失问题
// .el-sub-menu__title {
//   padding-right: unset !important;
// }

// fix 顶部导航更多样式问题
// .el-menu--horizontal .el-sub-menu .el-sub-menu__icon-arrow {
//   right: calc(0px - var(--el-menu-base-level-padding)) !important;
// }

// 弹出搜索框
.header-search-select {
  .el-select-dropdown__item {
    height: unset !important;
    line-height: unset !important;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

// el-table 表头样式
.el-table-header-cell {
  text-align: center !important;
}

.el-dialog__headerbtn {
  width: 47px !important;
}

// el-tree

.tree-item-flex {
  &.is-expanded .el-tree-node__children {
    display: flex !important;
    flex-direction: column;
    flex-direction: row;
    flex-wrap: wrap;
  }
}

// 修复饿了么 el-select没有宽度
.el-select {
  --el-select-width: 200px !important;
}

// 按钮样式调整
.el-button {
  margin-left: unset !important;
  margin-right: 12px;
}
.el-button:last-child {
  margin-right: 0;
}
// 解决top布局一级菜单高度不整齐
.topbar-menu .el-menu-item {
  height: unset;
  line-height: unset;
}
// 顶部导航宽度
.nav3 {
  .el-sub-menu__title {
    --el-menu-base-level-padding: 5px;
  }
}
