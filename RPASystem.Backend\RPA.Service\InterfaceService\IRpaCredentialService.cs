using RPASystem.Model;

namespace RPASystem.Service
{
    public interface IRpaCredentialService
    {
        Task<List<RpaCredential>> GetAllCredentials();
        Task<RpaCredential> GetCredentialById(long id);
        Task<bool> CreateCredential(RpaCredential credential);
        Task<bool> UpdateCredential(RpaCredential credential);
        Task<bool> DeleteCredential(long id);
        Task<string> GetPasswordByUsername(string username);
    }
} 