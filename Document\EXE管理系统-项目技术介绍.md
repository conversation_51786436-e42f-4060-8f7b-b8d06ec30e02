# EXE管理系统-项目技术

## 使用的开发技术
- 数据库：Mysql （8.0.26 版本，数据库名：RPASystem，用户名：root，密码：123456）
- 开发语言：C# （.NET 8.0 版本）
- WEB后端：ASP.NET Core WEB API （基本的三层架构）
- WEB前端：Vue3 + Element Plus
- ORM框架：EntityFrameworkCore
- 客户端桌面应用：C#（.NETFramework 4.6.1）
- 客户端与服务器通讯：SignalR

## 项目的目录结构：
控制台客户端：
RPASystem.Client/RPASystem.ClientConsole
Web前端页面：
RPASystem.FrontendVue
Web后端服务：
RPASystem.Backend
RPASystem.Backend/RPASystem.WebApi //ASP.NET Core WebAPI 接口 
RPASystem.Backend/RPASystem.Model //模型（包括EF所使用的模型）
RPASystem.Backend/RPASystem.BLL //业务逻辑，也叫Service层
RPASystem.Backend/RPASystem.BLL/DB/RPASystemDbContext.cs //EF的DbContext  //注意：RPASystemDbContext类没有命名空间


## 所有代码规范
- 所有代码变量不要"_"下滑线开头命名，变量用骆驼命名法。

## 数据库
- 任何的表主键名称都叫ID，比如Users表的ID叫ID，作为外键叫UserID。外键命名：表名+ID
- 所有ID都用bigint
- sql脚本关键字用小写

## 前端代码风格
- 一个元素里的属性尽量不要换行，保持层次结构
- 尽可能写多点代码注释
- 管理页面默认风格：主页面显示表格数据，点击新增或修改弹出窗口编辑，修改和删除在每行的最后显示。点击删除需要确认才能删除。

## 代码参考
### Sql脚本代码输出示例，请作为参考
文件路径：/SqlScript/UserManagement.sql
```sql
-- 创建Users表
create table Users (
    ID bigint auto_increment primary key comment '用户ID',
    UserName varchar(255) not null comment '用户名'
);
```

### 后端C#代码输出示例，仅作为参考，具体代码请按通用标准编写
文件路径：/BLL/UserService.cs
```csharp
namespace BLL
{
    /// <summary>
    /// 用户管理服务
    /// </summary>
    public class UserService
    {
        private readonly RPASystemDbContext db;

        public UserService(RPASystemDbContext context)
        {
            this.db = context;
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="userDto">用户信息</param>
        /// <returns>注册结果</returns>
        public bool Register(UserDto userDto)
        {
            // 验证输入信息
            // 存储用户数据
            return true; // 返回注册结果
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录结果和会话令牌</returns>
        public (bool, string) Login(string username, string password)
        {
            // 验证用户名和密码
            // 生成会话令牌
            return (true, "token"); // 返回登录结果和会话令牌
        }
    }
}
```
文件路径：/Models/User.cs
```csharp
namespace Models
{
    /// <summary>
    /// 用户模型
    /// </summary>
    public class User
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserID { get; set; }
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }
        /// <summary>
        /// 密码哈希
        /// </summary>
        public string PasswordHash { get; set; }
        /// <summary>
        /// 电子邮件
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}
```
文件路径：/Controllers/UserController.cs
```csharp
using Microsoft.AspNetCore.Mvc;
using Models;
using Services;
namespace Controllers
{
    /// <summary>
    /// 用户控制器，处理用户相关的API请求
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class UserController : ControllerBase
    {
        private readonly UserService userService;
        public UserController(UserService userService)
        {
            userService = userService;
        }

        /// <summary>
        /// 用户注册API
        /// </summary>
        /// <param name="userDto">用户信息</param>
        /// <returns>注册结果</returns>
        [HttpPost("register")]
        public IActionResult Register([FromBody] UserDto userDto)
        {
            // 调用用户服务进行注册
            // 返回相应的结果
        }

        /// <summary>
        /// 用户登录API
        /// </summary>
        /// <param name="loginDto">登录信息</param>
        /// <returns>登录结果和会话令牌</returns>
        [HttpPost("login")]
        public IActionResult Login([FromBody] LoginDto loginDto)
        {
            // 调用用户服务进行登录验证
            // 返回相应的结果和会话令牌
        }
    }
}
```