@use './variables.module.scss';
@use './mixin.scss';
@use './transition.scss';
@use './element-ui.scss';
@use './sidebar.scss';
@use './btn.scss';
@use './waves.scss';

html,
body,
#app {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;
  background-color: var(--base-bg-main);
  // overflow: hidden;
  position: relative;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0 !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

.text-center {
  text-align: center;
}

.link-type,
.link-type:focus {
  color: var(--el-color-primary);
  cursor: pointer;

  &:hover {
    // color: rgb(32, 160, 255);
    opacity: 0.3;
  }
}

/** 基础通用 **/
.pt5 {
  padding-top: 5px;
}

.pr5 {
  padding-right: 5px;
}

.pb5 {
  padding-bottom: 5px;
}
.pb20 {
  padding-bottom: 20px;
}

.mt5 {
  margin-top: 5px;
}

.mr5 {
  margin-right: 5px;
}

.mb5 {
  margin-bottom: 5px;
}

.mb8 {
  margin-bottom: 8px;
}

.ml5 {
  margin-left: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mt20 {
  margin-top: 20px;
}

.mr20 {
  margin-right: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.ml20 {
  margin-left: 20px;
}

.ml {
  margin-left: auto;
}

.mr {
  margin-right: auto;
}

.mt {
  margin-top: auto;
}

.mb {
  margin-bottom: auto;
}
.w20 {
  width: 20%;
}
.w100 {
  width: 100%;
}

.pull-right {
  float: right !important;
}

/* text color */
.text-navy {
  color: #1ab394;
}
.text-pink {
  color: pink;
}
.text-primary {
  color: inherit;
}

.text-success {
  color: #1c84c6;
}

.text-info {
  color: #23c6c8;
}

.text-warning {
  color: #f8ac59;
}

.text-danger {
  color: #ff0000;
}

.text-muted {
  color: #888888;
}

.text-orange {
  color: #ff7d00;
}

.text-hotpink {
  color: hotpink;
}

.text-green {
  color: green;
}

.text-greenyellow {
  color: greenyellow;
}
.text-purple {
  color: #ff00ff;
}

/* image */
.img-circle {
  border-radius: 50%;
}

.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

.icon {
  width: 100px;
}

.table-td-thumb {
  width: 56px;
}

.flex-center {
  flex-direction: column;
  overflow: hidden;
}

.hljs {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  border-radius: 5px;
}
