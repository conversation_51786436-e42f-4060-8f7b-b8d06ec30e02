namespace RPASystem.Model
{
    /// <summary>
    /// RPA账号凭证
    /// </summary>
    public class RpaCredential
    {
        /// <summary>
        /// 凭证ID
        /// </summary>
        public long ID { get; set; }


        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }
    }
}