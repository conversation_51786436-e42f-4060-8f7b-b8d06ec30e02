﻿using System;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

public class CommandLineParser
{
    // 解析“键=值”格式字符串到 JObject
    public static JObject ParseToJObject(string input)
    {
        var result = new JObject();
        var nestedObjects = new Dictionary<string, JObject>();  // 存储所有需要 JSON 字符串的字段

        // 使用正则表达式匹配 `key=value`，支持带引号的值
        var regex = new Regex(@"(\w+(\.\w+)*)=(?:""([^""]*)""|(\S+))");
        
        var matches = regex.Matches(input);
        foreach (Match match in matches)
        {
            var key = match.Groups[1].Value;
            var value = match.Groups[3].Success ? match.Groups[3].Value : match.Groups[4].Value;
            
            // 处理点分格式，判断是否嵌套
            var keys = key.Split('.');
            if (keys.Length > 1)
            {
                // 顶级字段
                var topLevelKey = keys[0];

                // 若顶级字段未在字典中，则初始化
                if (!nestedObjects.ContainsKey(topLevelKey))
                {
                    nestedObjects[topLevelKey] = new JObject();
                }

                // 构建嵌套结构
                JObject current = nestedObjects[topLevelKey];
                for (int i = 1; i < keys.Length - 1; i++)
                {
                    if (current[keys[i]] == null)
                        current[keys[i]] = new JObject();

                    current = (JObject)current[keys[i]];
                }

                current[keys[^1]] = value;
            }
            else
            {
                // 如果是非嵌套的字段，直接添加到结果
                result[key] = value;
            }
        }

        // 将每个顶级嵌套对象转换为 JSON 字符串并添加到 result
        foreach (var entry in nestedObjects)
        {
            result[entry.Key] = entry.Value.ToString(Formatting.None);
        }

        return result;
    }

    // 将 JObject 转换为指定类型的对象
    public static T ConvertToObject<T>(JObject jObject)
    {
        return jObject.ToObject<T>();
    }
}

// // 示例对象类
// public class TaskParameters
// {
//     public int ParentTaskID { get; set; }
//     public string ProgramName { get; set; }
//     public string InputParameters { get; set; }  // 可以是 JSON 字符串的属性
//     public int TaskPriority { get; set; }
//     public int TaskType { get; set; }

//     // 其他可能的 JSON 字符串属性
//     public string AdditionalData { get; set; }
// }

// // 主程序示例
// class Program
// {
//     static void Main(string[] args)
//     {
//         // 示例输入字符串
//         string input = "ParentTaskID=123 programName=ORC_Test inputParameters.InputFile=521 inputParameters.ServerIP=************ inputParameters.UserName=\"test 111\" taskPriority=10 taskType=1 AdditionalData.Data1=abc AdditionalData.Data2=xyz";

//         // 解析为 JObject
//         JObject json = CommandLineParser.ParseToJObject(input);

//         // 将 JObject 转换为目标对象
//         TaskParameters taskParameters = CommandLineParser.ConvertToObject<TaskParameters>(json);

//         // 输出结果
//         Console.WriteLine($"ParentTaskID: {taskParameters.ParentTaskID}");
//         Console.WriteLine($"ProgramName: {taskParameters.ProgramName}");
//         Console.WriteLine($"InputParameters (JSON String): {taskParameters.InputParameters}");
//         Console.WriteLine($"TaskPriority: {taskParameters.TaskPriority}");
//         Console.WriteLine($"TaskType: {taskParameters.TaskType}");
//         Console.WriteLine($"AdditionalData (JSON String): {taskParameters.AdditionalData}");
//     }
// }
