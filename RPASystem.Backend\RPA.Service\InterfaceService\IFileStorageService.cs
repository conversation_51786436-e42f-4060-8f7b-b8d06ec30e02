using RPASystem.Model;

namespace RPASystem.Service
{
    public interface IFileStorageService
    {
        Task<long> UploadFileAsync(FileStorage fileStorage);
        Task<FileStorage> GetFileAsync(long id);
        Task<(List<FileStorage> Items, int Total)> GetFilesByPageAsync(int pageIndex, int pageSize, string? searchFileName = null, string? searchFileExtension = null);
        Task<bool> UpdateFileRemarkAsync(long id, string remark);
        Task<bool> UpdateFileAsync(long id, byte[] fileData, string fileName, string fileExtension, string remark);
        Task<bool> BatchDeleteFilesAsync(List<long> ids);
    }
} 