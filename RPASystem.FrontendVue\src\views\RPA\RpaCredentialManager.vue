<template>
    <div class="credential-manager">
      <!-- 顶部操作栏 -->
      <div class="operation-bar">
        <div class="left-section">
          <el-input
            v-model="searchQuery"
            placeholder="请输入用户名搜索"
            style="width: 300px;"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
        <div class="right-section">
          <el-button type="primary" @click="showAddDialog">新增凭证</el-button>
        </div>
      </div>
  
      <!-- 凭证列表 -->
      <el-table :data="filteredCredentials" style="width: 100%">
        <el-table-column prop="username" label="用户名" width="180" />
        <el-table-column prop="password" label="密文">
          <template #default="scope">
            <span>{{ maskPassword(scope.row.password) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="createdTime" label="创建时间" width="180" :formatter="formatDateTime" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 新增/编辑对话框 -->
      <el-dialog 
        v-model="dialogVisible" 
        :title="isEdit ? '编辑凭证' : '新增凭证'"
        width="500px"
        destroy-on-close
      >
        <el-form :model="currentCredential" label-width="100px" :rules="rules" ref="formRef">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="currentCredential.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="密文" prop="password">
            <el-input 
              v-model="currentCredential.password" 
              type="password" 
              placeholder="请输入密文" 
              show-password
            />
          </el-form-item>
          <el-form-item label="描述">
            <el-input 
              v-model="currentCredential.description" 
              type="textarea" 
              :rows="2"
              placeholder="请输入描述信息"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, onMounted } from 'vue'
  import axios from 'axios'
  import { ElMessage, ElMessageBox } from 'element-plus'
  
  // 数据定义
  const credentials = ref([])
  const dialogVisible = ref(false)
  const isEdit = ref(false)
  const formRef = ref(null)
  const searchQuery = ref('')
  
  const currentCredential = ref({
    username: '',
    password: '',
    description: ''
  })
  
  // 根据搜索条件过滤凭证列表
  const filteredCredentials = computed(() => {
    if (!searchQuery.value) return credentials.value;
    const query = searchQuery.value.toLowerCase();
    return credentials.value.filter(credential => 
      credential.username.toLowerCase().includes(query)
    );
  });
  
  // 表单验证规则
  const rules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密文', trigger: 'blur' },
      { min: 6, message: '密文长度不能小于6个字符', trigger: 'blur' }
    ]
  }
  
  // 获取所有凭证
  const fetchCredentials = async () => {
    try {
      const response = await axios.get('/api/rpacredential')
      credentials.value = response.data
    } catch (error) {
      ElMessage.error('获取凭证列表失败')
      console.error('获取凭证列表失败:', error)
    }
  }
  
  // 处理搜索
  const handleSearch = () => {
    // 本地搜索，无需调用后端
  };
  
  // 显示新增对话框
  const showAddDialog = () => {
    isEdit.value = false
    currentCredential.value = {
      username: '',
      password: '',
      description: ''
    }
    dialogVisible.value = true
  }
  
  // 显示编辑对话框
  const handleEdit = (row) => {
    isEdit.value = true
    currentCredential.value = { ...row }
    dialogVisible.value = true
  }
  
  // 处理删除
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm('确定要删除该凭证吗？', '提示', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
      
      await axios.delete(`/api/rpacredential/${row.id}`)
      ElMessage.success('删除成功')
      await fetchCredentials()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
        console.error('删除失败:', error)
      }
    }
  }
  
  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return
    
    try {
      await formRef.value.validate()
      
      if (isEdit.value) {
        await axios.put('/api/rpacredential', currentCredential.value)
      } else {
        await axios.post('/api/rpacredential', currentCredential.value)
      }
      
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      await fetchCredentials()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
        console.error(isEdit.value ? '更新失败:' : '创建失败:', error)
      }
    }
  }
  
  // 格式化日期时间
  const formatDateTime = (row, column, cellValue) => {
    if (!cellValue) return ''
    return new Date(cellValue).toLocaleString()
  }
  
  // 密文掩码显示
  const maskPassword = (password) => {
    return password ? '••••••••' : ''
  }
  
  // 页面加载时初始化数据
  onMounted(async () => {
    await fetchCredentials()
  })
  </script>
  
  <style scoped>
  .credential-manager {
    padding: 20px;
  }
  
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .left-section {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .right-section {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  
  :deep(.el-dialog__body) {
    padding: 20px 40px;
  }
  
  :deep(.el-form-item__content) {
    display: flex;
    justify-content: flex-start;
  }
  
  :deep(.el-table .cell) {
    display: flex;
    align-items: center;
  }
  
  :deep(.el-button + .el-button) {
    margin-left: 8px;
  }
  </style>