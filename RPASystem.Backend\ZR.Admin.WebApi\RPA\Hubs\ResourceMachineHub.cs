﻿using Microsoft.AspNetCore.SignalR;
using RPASystem.Service;
using System.Collections.Concurrent;
using RPASystem.Model; // Assuming this is where TaskStatusEnum is defined
using System.IO;
using Microsoft.Extensions.Logging;

namespace RPASystem.WebApi.Hubs
{
    public class ResourceMachineHub : Hub
    {
        public static ConcurrentDictionary<string, string> machineConnections = new ConcurrentDictionary<string, string>();
        // 存储远程观看者信息：key为被观看的机器名，value为观看者的连接ID
        private static ConcurrentDictionary<string, HashSet<string>> screenViewers = new ConcurrentDictionary<string, HashSet<string>>();
        private readonly IResourceMachineService resourceMachineService;
        private readonly IJobTaskService jobTaskService;
        private readonly IWebHostEnvironment environment;
        private readonly ILogger<ResourceMachineHub> _logger;

        public ResourceMachineHub(IResourceMachineService resourceMachineService, IJobTaskService jobTaskService, IWebHostEnvironment environment, ILogger<ResourceMachineHub> logger)
        {
            this.resourceMachineService = resourceMachineService;
            this.jobTaskService = jobTaskService;
            this.environment = environment;
            this._logger = logger;
        }

        public override async Task OnConnectedAsync()
        {
            string machineName = Context.GetHttpContext().Request.Query["machineName"];
            string clientVersion = Context.GetHttpContext().Request.Query["version"];

            if (!string.IsNullOrEmpty(machineName))
            {
                // 这是资源机客户端
                if (!machineConnections.ContainsKey(machineName))
                {
                    machineConnections[machineName] = Context.ConnectionId;
                }
                else
                {
                    await Clients.Caller.SendAsync("Refused", "资源机已注册！！！");
                    return;
                }

                machineConnections[machineName] = Context.ConnectionId;

                // 检查是否是最新版本
                bool isLatestVersion = false;
                if (!string.IsNullOrEmpty(clientVersion))
                {
                    var versionPath = Path.Combine(environment.ContentRootPath, "Updates", "ClientPackage", "version.txt");
                    if (System.IO.File.Exists(versionPath))
                    {
                        var serverVersion = await System.IO.File.ReadAllTextAsync(versionPath);
                        isLatestVersion = serverVersion == clientVersion;
                    }
                }

                // 注册或更新资源机
                await resourceMachineService.RegisterOrUpdateMachineAsync(machineName, clientVersion, isLatestVersion);
                // 如果不是最新版本，设置为运行中状态并通知更新
                if (!isLatestVersion)
                {
                    await Clients.Caller.SendAsync("NewVersionUpdate");
                }
                await Clients.Group("WebClients").SendAsync("RefreshResourceMachines");
            }
            else
            {
                // 这是 Web 前端客户端
                await Groups.AddToGroupAsync(Context.ConnectionId, "WebClients");
            }
            await base.OnConnectedAsync();
        }


        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var machineName = machineConnections.FirstOrDefault(x => x.Value == Context.ConnectionId).Key;
            if (!string.IsNullOrEmpty(machineName))
            {
                // 资源机客户端断开连接
                machineConnections.TryRemove(machineName, out _);
                await resourceMachineService.UpdateMachineStatusAsync(machineName, TaskStatusEnum.离线);
                
                // 通知所有观看者，被观看的机器已断开
                if (screenViewers.TryGetValue(machineName, out var viewerSet))
                {
                    foreach (var viewerId in viewerSet)
                    {
                        await Clients.Client(viewerId).SendAsync("MachineDisconnected", machineName);
                    }
                    screenViewers.TryRemove(machineName, out _);
                }
                
                await Clients.Group("WebClients").SendAsync("RefreshResourceMachines");
            }
            else
            {
                // Web前端客户端断开连接
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, "WebClients");
                
                // 从所有观看者列表中移除此连接，并在必要时发送停止命令
                foreach (var kvp in screenViewers)
                {
                    var viewerSet = kvp.Value;
                    if (viewerSet.Remove(Context.ConnectionId))
                    {
                        // 如果移除后没有观看者了，发送停止命令
                        if (viewerSet.Count == 0)
                        {
                            if (machineConnections.TryGetValue(kvp.Key, out string machineConnectionId))
                            {
                                try
                                {
                                    await Clients.Client(machineConnectionId).SendAsync("StopScreenCapture");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"发送停止截图命令失败：{ex.Message}");
                                }
                            }
                            screenViewers.TryRemove(kvp.Key, out _);
                        }
                    }
                }
            }
            await base.OnDisconnectedAsync(exception);
        }

        public async Task SendMessage(string message)
        {
            await Clients.All.SendAsync("ReceiveMessage", message);
        }

        public async Task UpdateResourceInfo(ResourceInfoDto resourceInfo)
        {
            // 从 SignalR 连接上下文中获取客户端真实 IP
            var httpContext = Context.GetHttpContext();
            var clientIp = httpContext?.Request.Headers["X-Forwarded-For"].FirstOrDefault() // 获取代理转发的IP
                ?? httpContext?.Connection.RemoteIpAddress?.ToString(); // 如果没有代理，获取直连IP

            // 处理 IPv4 映射到 IPv6 的情况
            if (!string.IsNullOrEmpty(clientIp) && clientIp.StartsWith("::ffff:"))
            {
                clientIp = clientIp.Substring(7); // 移除 "::ffff:" 前缀
            }

            resourceInfo.IpAddress = clientIp;

            await resourceMachineService.UpdateResourceInfoAsync(resourceInfo);
            await Clients.Group("WebClients").SendAsync("RefreshResourceMachines");
        }

        public async Task<bool> TaskCompleted(long id, JobTaskStatusEnum jobTaskStatusEnum, string outputResults, List<long> runningTaskIds)
        {
            try
            {
                await jobTaskService.UpdateJobTaskStatusAsync(id, jobTaskStatusEnum, outputResults: string.IsNullOrEmpty(outputResults) ? "{}" : outputResults);
                await Clients.Group("WebClients").SendAsync("RefreshJobTasks");
                var machineName = machineConnections.FirstOrDefault(x => x.Value == Context.ConnectionId).Key;
                if (!string.IsNullOrEmpty(machineName))
                {
                    if (runningTaskIds.Count > 0)
                    {
                        await resourceMachineService.UpdateMachineStatusAsync(machineName, TaskStatusEnum.任务运行中);
                    }
                    else
                    {
                        await resourceMachineService.UpdateMachineStatusAsync(machineName, TaskStatusEnum.空闲);
                    }
                    await Clients.Group("WebClients").SendAsync("RefreshResourceMachines");
                }
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return false;
            }
        }

        // 更新资源机任务状态，根据任务名称（多个任务用"|"分隔）判断执行任务的资源机是否空闲，以及任务状态与资源机是否一致
        public async Task UpdateResourceMachineTaskStatus(List<long> runningTaskIds)
        {
            // 获取当前资源机名称
            var machineName = machineConnections.FirstOrDefault(x => x.Value == Context.ConnectionId).Key;
            if (!string.IsNullOrEmpty(machineName))
            {
                await resourceMachineService.UpdateResourceMachineTaskStatusAsync(machineName, runningTaskIds);
                await Clients.Group("WebClients").SendAsync("RefreshResourceMachines");
                await Clients.Group("WebClients").SendAsync("RefreshJobTasks");
            }
        }

        #region 远程桌面功能
        
        // 开始观看指定机器的屏幕
        public async Task StartWatching(string machineName)
        {
            if (machineConnections.TryGetValue(machineName, out string machineConnectionId))
            {
                screenViewers.AddOrUpdate(machineName,
                    new HashSet<string> { Context.ConnectionId },
                    (key, existingSet) =>
                    {
                        existingSet.Add(Context.ConnectionId);
                        return existingSet;
                    });
                
                await Clients.Client(machineConnectionId).SendAsync("StartScreenCapture");
            }
        }

        // 停止观看指定机器的屏幕
        public async Task StopWatching(string machineName)
        {
            try
            {
                if (screenViewers.TryGetValue(machineName, out var viewerSet))
                {
                    viewerSet.Remove(Context.ConnectionId);
                    
                    // 如果没有观看者了，通知客户端停止截图
                    if (viewerSet.Count == 0)
                    {
                        screenViewers.TryRemove(machineName, out _);
                        
                        if (machineConnections.TryGetValue(machineName, out string machineConnectionId))
                        {
                            try
                            {
                                await Clients.Client(machineConnectionId).SendAsync("StopScreenCapture");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"发送停止截图命令失败：{ex.Message}");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"未找到机器的连接ID：{machineName}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"停止观看时发生错误：{ex.Message}");
                throw;
            }
        }

        // 接收并转发屏幕截图
        public async Task SendScreenshot(string machineName, byte[] imageData)
        {
            if (screenViewers.TryGetValue(machineName, out var viewerSet))
            {
                foreach (var viewerId in viewerSet)
                {
                    await Clients.Client(viewerId).SendAsync("ReceiveScreenshot", imageData);
                }
            }
        }
        
        // 发送鼠标控制命令
        public async Task SendMouseCommand(string machineName, string button, int x, int y)
        {
            try
            {
                if (machineConnections.TryGetValue(machineName, out string machineConnectionId))
                {
                    // 发送鼠标命令到指定客户端
                    await Clients.Client(machineConnectionId).SendAsync("ExecuteMouseCommand", button, x, y);
                    
                    // 日志记录
                    _logger.LogInformation($"发送鼠标命令: 机器={machineName}, 按钮={button}, X={x}, Y={y}");
                }
                else
                {
                    await Clients.Caller.SendAsync("ReceiveMouseCommandResult", false, "找不到目标机器");
                    _logger.LogWarning($"发送鼠标命令失败: 找不到机器 {machineName}");
                }
            }
            catch (Exception ex)
            {
                await Clients.Caller.SendAsync("ReceiveMouseCommandResult", false, $"发送命令失败: {ex.Message}");
                _logger.LogError(ex, $"发送鼠标命令出错: {ex.Message}");
            }
        }
        
        // 发送鼠标滚轮命令
        public async Task SendMouseWheelCommand(string machineName, int scrollAmount, int x, int y)
        {
            try
            {
                if (machineConnections.TryGetValue(machineName, out string machineConnectionId))
                {
                    // 发送鼠标滚轮命令到指定客户端
                    await Clients.Client(machineConnectionId).SendAsync("ExecuteMouseWheelCommand", scrollAmount, x, y);
                    
                    // 日志记录
                    _logger.LogInformation($"发送鼠标滚轮命令: 机器={machineName}, 滚动量={scrollAmount}, X={x}, Y={y}");
                }
                else
                {
                    await Clients.Caller.SendAsync("ReceiveMouseCommandResult", false, "找不到目标机器");
                    _logger.LogWarning($"发送鼠标滚轮命令失败: 找不到机器 {machineName}");
                }
            }
            catch (Exception ex)
            {
                await Clients.Caller.SendAsync("ReceiveMouseCommandResult", false, $"发送滚轮命令失败: {ex.Message}");
                _logger.LogError(ex, $"发送鼠标滚轮命令出错: {ex.Message}");
            }
        }
        
        // 接收客户端的鼠标命令执行结果
        public async Task SendMouseCommandResult(string machineName, bool success, string message)
        {
            if (screenViewers.TryGetValue(machineName, out var viewerSet))
            {
                foreach (var viewerId in viewerSet)
                {
                    await Clients.Client(viewerId).SendAsync("ReceiveMouseCommandResult", success, message);
                }
            }
        }
        
        // 发送键盘控制命令
        public async Task SendKeyboardCommand(string machineName, string eventType, string key, string[] keyCombination, int keyCode)
        {
            try
            {
                if (machineConnections.TryGetValue(machineName, out string machineConnectionId))
                {
                    // 发送键盘命令到指定客户端
                    await Clients.Client(machineConnectionId).SendAsync("ExecuteKeyboardCommand", eventType, key, keyCombination, keyCode);
                    
                    // 日志记录
                    _logger.LogInformation($"发送键盘命令: 机器={machineName}, 事件={eventType}, 键={key}");
                }
                else
                {
                    await Clients.Caller.SendAsync("ReceiveKeyboardCommandResult", false, "找不到目标机器");
                    _logger.LogWarning($"发送键盘命令失败: 找不到机器 {machineName}");
                }
            }
            catch (Exception ex)
            {
                await Clients.Caller.SendAsync("ReceiveKeyboardCommandResult", false, $"发送命令失败: {ex.Message}");
                _logger.LogError(ex, $"发送键盘命令出错: {ex.Message}");
            }
        }
        
        // 接收客户端的键盘命令执行结果
        public async Task SendKeyboardCommandResult(string machineName, bool success, string message)
        {
            if (screenViewers.TryGetValue(machineName, out var viewerSet))
            {
                foreach (var viewerId in viewerSet)
                {
                    await Clients.Client(viewerId).SendAsync("ReceiveKeyboardCommandResult", success, message);
                }
            }
        }
        
        #endregion
    }
}

