RPA运行一个程序，有输入参数。
我目前的方案是用Excel做输入，程序读取EXCEL内容做业务逻辑处理。
在Excel中每一行有一个唯一单号，在运行时，程序会在每个单号后写上状态（已完成，警告，异常），异常信息，屏幕截图。
运行结束后能看到单号对应的日志信息。
如果有异常，每一行对应一张屏幕截图。
程序运行结束后会自动将Excel文件上传到Web后台系统中。可以后台中查看。


好！现在问题是，EXCEL插入图片很困难，它不能插入到单元格中，就算插入到了Excel中，我点击不能放大，得一张张将它拉大，才能看清楚屏幕截图，图片很大，必须拉大。

所以，现在有什么好的方案可以解决？
比如能不能做个vb脚本，点击链接放大图片？
能不能将图片上传到一个api系统中管理，Excel中可以点击超链接在线查看？但这样有个缺点是增加了复杂度，如果系统崩溃或坏了就无法查看了，也不灵活。

请思考有什么更简单方便的方案？