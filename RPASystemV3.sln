﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34511.84
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RPASystem.Backend", "RPASystem.Backend", "{FC51F3DC-4E90-4844-9207-4A289F5072FA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RPASystem.FrontendVue", "RPASystem.FrontendVue", "{AAE85461-EAF3-48E3-ACF5-C32228642AF5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RPASystem.ClientWin", "RPASystem.Client\RPASystem.ClientWin\RPASystem.ClientWin.csproj", "{5236E9D4-4632-40DD-A4FB-4A5F65653775}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RPASystem.MergeTool", "RPASystem.Client\RPASystem.MergeTool\RPASystem.MergeTool.csproj", "{313432B0-767C-425E-A30C-4F46598F3C9F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ZR.Admin.WebApi", "RPASystem.Backend\ZR.Admin.WebApi\ZR.Admin.WebApi.csproj", "{28E1B75F-85E7-4337-A277-C706279E9768}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RPA.Model", "RPASystem.Backend\RPA.Model\RPA.Model.csproj", "{7C17483A-1300-4315-AC53-485EEA1D783C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RPA.Service", "RPASystem.Backend\RPA.Service\RPA.Service.csproj", "{6FEAAB8D-08F1-4EF0-A7A6-9321134D05C2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Full|Any CPU = Full|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5236E9D4-4632-40DD-A4FB-4A5F65653775}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5236E9D4-4632-40DD-A4FB-4A5F65653775}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5236E9D4-4632-40DD-A4FB-4A5F65653775}.Full|Any CPU.ActiveCfg = Full|Any CPU
		{5236E9D4-4632-40DD-A4FB-4A5F65653775}.Full|Any CPU.Build.0 = Full|Any CPU
		{5236E9D4-4632-40DD-A4FB-4A5F65653775}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5236E9D4-4632-40DD-A4FB-4A5F65653775}.Release|Any CPU.Build.0 = Release|Any CPU
		{313432B0-767C-425E-A30C-4F46598F3C9F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{313432B0-767C-425E-A30C-4F46598F3C9F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{313432B0-767C-425E-A30C-4F46598F3C9F}.Full|Any CPU.ActiveCfg = Release|Any CPU
		{313432B0-767C-425E-A30C-4F46598F3C9F}.Full|Any CPU.Build.0 = Release|Any CPU
		{313432B0-767C-425E-A30C-4F46598F3C9F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{313432B0-767C-425E-A30C-4F46598F3C9F}.Release|Any CPU.Build.0 = Release|Any CPU
		{28E1B75F-85E7-4337-A277-C706279E9768}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28E1B75F-85E7-4337-A277-C706279E9768}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28E1B75F-85E7-4337-A277-C706279E9768}.Full|Any CPU.ActiveCfg = Release|Any CPU
		{28E1B75F-85E7-4337-A277-C706279E9768}.Full|Any CPU.Build.0 = Release|Any CPU
		{28E1B75F-85E7-4337-A277-C706279E9768}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28E1B75F-85E7-4337-A277-C706279E9768}.Release|Any CPU.Build.0 = Release|Any CPU
		{7C17483A-1300-4315-AC53-485EEA1D783C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7C17483A-1300-4315-AC53-485EEA1D783C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7C17483A-1300-4315-AC53-485EEA1D783C}.Full|Any CPU.ActiveCfg = Release|Any CPU
		{7C17483A-1300-4315-AC53-485EEA1D783C}.Full|Any CPU.Build.0 = Release|Any CPU
		{7C17483A-1300-4315-AC53-485EEA1D783C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7C17483A-1300-4315-AC53-485EEA1D783C}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FEAAB8D-08F1-4EF0-A7A6-9321134D05C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FEAAB8D-08F1-4EF0-A7A6-9321134D05C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FEAAB8D-08F1-4EF0-A7A6-9321134D05C2}.Full|Any CPU.ActiveCfg = Release|Any CPU
		{6FEAAB8D-08F1-4EF0-A7A6-9321134D05C2}.Full|Any CPU.Build.0 = Release|Any CPU
		{6FEAAB8D-08F1-4EF0-A7A6-9321134D05C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FEAAB8D-08F1-4EF0-A7A6-9321134D05C2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5236E9D4-4632-40DD-A4FB-4A5F65653775} = {AAE85461-EAF3-48E3-ACF5-C32228642AF5}
		{313432B0-767C-425E-A30C-4F46598F3C9F} = {AAE85461-EAF3-48E3-ACF5-C32228642AF5}
		{28E1B75F-85E7-4337-A277-C706279E9768} = {FC51F3DC-4E90-4844-9207-4A289F5072FA}
		{7C17483A-1300-4315-AC53-485EEA1D783C} = {FC51F3DC-4E90-4844-9207-4A289F5072FA}
		{6FEAAB8D-08F1-4EF0-A7A6-9321134D05C2} = {FC51F3DC-4E90-4844-9207-4A289F5072FA}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0E2C88DD-5D87-444D-AB86-ABB401896D8F}
	EndGlobalSection
EndGlobal
