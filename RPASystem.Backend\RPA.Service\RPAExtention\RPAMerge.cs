using System.Text.Json;
using RPASystem.Service;
using RPASystem.Model;
using RPASystem.Service;
using Infrastructure.Attribute;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace RPASystem.Service;

public partial class JobTaskService
{
    /// <summary>
    /// 处理RPA任务的合并操作,
    /// task = 父任务对象
    /// </summary>
    public async Task<JobTaskStatusEnum> MergeDirFilesAsync(JobTask parentTask, JobTaskStatusEnum jobTaskStatusEnum, List<JobTask> siblingTasks)
    {
        var mergeTaskName = parentTask.TaskName + "_merge";
        // 状态如果不为成功直接返回，只处理成功状态的, 如果已经生成合并任务，也返回
        if (jobTaskStatusEnum != JobTaskStatusEnum.Success)
        {
            return jobTaskStatusEnum;
        }

        try
        {
            // 解析输入参数
            var inputParams = JsonSerializer.Deserialize<Dictionary<string, string>>(parentTask.InputParameters ?? "{}");

            // 检查必要参数
            if (!inputParams.TryGetValue("MergeType", out var mergeTypeStr) || !inputParams.TryGetValue("ServerIP", out var serverIP))
            {
                return jobTaskStatusEnum;
            }

            // 如果ServerIP为空，抛出异常
            if (string.IsNullOrEmpty(serverIP))
            {
                throw new Exception("合并任务的ServerIP不能为空");
            }

            // 获取顶层任务名称 
            var topTaskName = await GetTopTaskNameAsync(parentTask.ID);
            // 如果相同则认为是系统编排拆分任务
            if (topTaskName == parentTask.TaskName)
            {
                topTaskName = string.Empty;
            }
            // 获取顶层任务的程序名称
            var topTaskProgramName = await GetTopTaskProgramNameAsync(parentTask.ID);


            var parentPath = $@"\\{serverIP}\VShare\WAutoDeploy\{topTaskProgramName}\OUTPUTRPASYSTEM\{topTaskName}\{parentTask.TaskName}\Output";
            // 如果有合并,成功后结束,写入结果
            if (siblingTasks.Any(t => t.TaskName == mergeTaskName))
            {
                // 创建对象并序列化
                var result = new 
                {
                    IsSucceed = true,
                    ReturnResult = parentPath
                };
                parentTask.OutputResults = JsonSerializer.Serialize(result);
                return jobTaskStatusEnum;
            }

            // 创建合并任务的参数
            var orchestrationTask = new OrchestrationTaskDto
            {
                TaskName = mergeTaskName,
                ParentTaskID = parentTask.ID,
                ProgramName = "RPASystem.MergeTool",
                ResourceSelection = serverIP,
                TaskPriority = parentTask.Priority,
                InputParameters = JsonSerializer.Serialize(new
                {
                    targetPath = parentPath,
                    sourcePaths = $@"\\{serverIP}\VShare\WAutoDeploy\{topTaskProgramName}\OUTPUTRPASYSTEM\{topTaskName}\{parentTask.TaskName}\{parentTask.TaskName}_{{X}}\Output",
                    processType = mergeTypeStr
                })
            };

            // 使用OrchestrationService创建合并任务
            await orchestrationService.CreateOrchestrationTask(orchestrationTask);

            // 创建成功后返回运行状态
            return JobTaskStatusEnum.Running;
        }
        catch (Exception ex)
        {
            parentTask.OutputResults = $@"{{""ErrorMessage"":""创建合并任务失败: {ex.Message}""}}";
            //task.Status = JobTaskStatusEnum.Failed;
            return JobTaskStatusEnum.Failed;
        }
    }
}