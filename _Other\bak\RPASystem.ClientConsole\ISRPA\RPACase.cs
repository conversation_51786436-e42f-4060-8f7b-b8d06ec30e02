using System;
using System.IO;

public class RPACase
{
    public const string ExceptionLogFilename = "ExceptionsLog.xlsx";
    private const string BaseDirectory = @"D:\isearch\RPACase";

    public enum Folder
    {
        Root,
        Application,
        Input,
        Config,
        Log,
        Output
    }

    public string Name { get; }

    public RPACase(string rpaName)
    {
        Name = rpaName;
    }

    public string GetPath(Folder folder)
    {
        string path = Path.Combine(BaseDirectory, Name);
        
        if (folder != Folder.Root)
        {
            path = Path.Combine(path, folder.ToString());
        }

        return path;
    }

    // 为了方便使用，我们可以为每个文件夹类型添加属性
    public string RootPath => GetPath(Folder.Root);
    public string ApplicationPath => GetPath(Folder.Application);
    public string InputPath => GetPath(Folder.Input);
    public string ConfigPath => GetPath(Folder.Config);
    public string LogPath => GetPath(Folder.Log);
    public string OutputPath => GetPath(Folder.Output);
}
