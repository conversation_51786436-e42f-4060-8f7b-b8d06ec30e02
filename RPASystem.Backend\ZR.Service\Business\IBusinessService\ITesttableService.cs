using ZR.Model.Business.Dto;
using ZR.Model.Business;

namespace ZR.Service.Business.IBusinessService
{
    /// <summary>
    /// 只是测试service接口
    /// </summary>
    public interface ITesttableService : IBaseService<Testtable>
    {
        PagedInfo<TesttableDto> GetList(TesttableQueryDto parm);

        Testtable GetInfo(long Id);


        Testtable AddTesttable(Testtable parm);
        int UpdateTesttable(Testtable parm);
        
        bool TruncateTesttable();

        (string, object, object) ImportTesttable(List<Testtable> list);

        PagedInfo<TesttableDto> ExportList(TesttableQueryDto parm);
    }
}
