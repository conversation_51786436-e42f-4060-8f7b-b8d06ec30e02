using Microsoft.EntityFrameworkCore;
using RPASystem.Model;
using RPASystem.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using RPASystem.Service;
using Infrastructure.Attribute;

namespace RPASystem.Service
{
    [AppService(ServiceType = typeof(IOrchestrationService), ServiceLifetime = LifeTime.Scoped)]
    public class OrchestrationService : IOrchestrationService
    {
        private readonly IServiceScopeFactory scopeFactory;

        public OrchestrationService(IServiceScopeFactory scopeFactory)
        {
            this.scopeFactory = scopeFactory;
        }

        /// <summary>
        /// 创建编排任务
        /// </summary>
        public async Task<long> CreateOrchestrationTask(OrchestrationTaskDto dto)
        {
            using var scope = scopeFactory.CreateScope();
            var jobTaskService = scope.ServiceProvider.GetRequiredService<IJobTaskService>();
            var exeProgramService = scope.ServiceProvider.GetRequiredService<IExeProgramService>();
            var taskNameGenerator = scope.ServiceProvider.GetRequiredService<ITaskNameGenerator>();

            var exeProgram = exeProgramService.GetExeProgramByName(dto.ProgramName);
            if (exeProgram == null)
            {
                throw new Exception($"Program not found: {dto.ProgramName}");
            }

            // 如果为空则生成
            if (string.IsNullOrWhiteSpace(dto.TaskName))
            {
                if (dto.ParentTaskID.HasValue && dto.ParentTaskID.Value > 0)
                {
                    var parentTask = await jobTaskService.GetJobTaskByIdAsync(dto.ParentTaskID.Value);
                    dto.TaskName = await taskNameGenerator.GenerateSubTaskNameAsync(parentTask.TaskName);
                }
                else
                {
                    dto.TaskName = await taskNameGenerator.GenerateMainTaskNameAsync();
                }
            }
            var jobTask = new JobTask
            {
                ParentTaskID = dto.ParentTaskID ?? 0,
                ExeProgramID = exeProgram.ID,
                TaskName = dto.TaskName,
                Priority = dto.TaskPriority ?? 0,
                InputParameters = dto.InputParameters,
                Status = JobTaskStatusEnum.Pending,
                CreatedAt = DateTime.Now,
                ResourceSelection = dto.ResourceSelection,
                Notes = dto.Notes,
            };
            if (exeProgram.ProgramType == ProgramTypeEnum.Orchestration)
            {
                jobTask.Status = JobTaskStatusEnum.Running;
                jobTask.TaskType = TaskType.Orchestration;
                jobTask.StartTime = DateTime.Now;
            }
            else if (exeProgram.ProgramType == ProgramTypeEnum.RPA && dto.InputParameters.Contains("ExcelPerSplitNum"))
            {
                jobTask.TaskType = TaskType.SystemOrchestrationSplit;
                jobTask.StartTime = DateTime.Now;
            }
            else
            {
                jobTask.TaskType = TaskType.Normal;
            }
            var taskId = await jobTaskService.CreateJobTaskAsync(jobTask);

            await ProcessSplitTaskAsync(jobTask);




            return taskId;
        }

        /// <summary>
        /// 处理Excel拆分任务
        /// </summary>
        public async Task ProcessSplitTaskAsync(JobTask jobTask)
        {
            if (jobTask.TaskType == TaskType.SystemOrchestrationSplit)
            {
                using var scope = scopeFactory.CreateScope();
                var jobTaskService = scope.ServiceProvider.GetRequiredService<JobTaskService>();
                var fileStorageService = scope.ServiceProvider.GetRequiredService<FileStorageService>();

                try
                {
                    // 解析输入参数
                    var inputParams = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(jobTask.InputParameters);

                    if (!inputParams.TryGetValue("InputFile", out var fileIdStr) || !inputParams.TryGetValue("ExcelPerSplitNum", out var splitNumStr)
                        || !int.TryParse(splitNumStr, out var splitNum))
                    {
                        throw new Exception("缺少必要的输入参数或参数格式错误");
                    }

                    byte[] fileData = null;
                    FileStorage fileStorage = null;
                    string fileExtension = ".xlsx";
                    if (fileIdStr.StartsWith("\\"))
                    {
                        fileData = File.ReadAllBytes(fileIdStr);
                        fileExtension = Path.GetExtension(fileIdStr);
                    }
                    else
                    {
                        if (!long.TryParse(fileIdStr, out var fileId))
                        {
                            throw new Exception($"fileId = {fileIdStr} 格式错误!!!");
                        }
                        // 获取Excel文件数据
                        fileStorage = await fileStorageService.GetFileAsync(fileId);
                        if (fileStorage == null)
                        {
                            throw new Exception($"找不到指定的Excel文件: {fileId}");
                        }
                        fileData = fileStorage.FileData;
                        fileExtension = fileStorage.FileExtension;
                    }



                    // 使用内存流处理Excel文件
                    using (var inputStream = new MemoryStream(fileData))
                    {
                        ExcelSplitter.Instance.SplitExcelByNum(inputStream, splitNum, async (index, splitStream) =>
                        {
                            using var splitScope = scopeFactory.CreateScope();
                            var splitFileService = splitScope.ServiceProvider.GetRequiredService<FileStorageService>();
                            var splitJobTaskService = splitScope.ServiceProvider.GetRequiredService<JobTaskService>();

                            // 保存拆分后的Excel文件
                            var newFileStorage = new FileStorage
                            {
                                FileName = $"{jobTask.TaskName}_{index}",
                                FileExtension = fileExtension,
                                FileData = ((MemoryStream)splitStream).ToArray(),
                                UploadTime = DateTime.Now
                            };

                            var newFileId = await splitFileService.UploadFileAsync(newFileStorage);

                            // 创建新的输入参数，替换文件ID
                            var newInputParams = new Dictionary<string, string>(inputParams);
                            newInputParams["InputFile"] = newFileId.ToString();

                            var subTaskName = $"{jobTask.TaskName}_{index}";
                            var subTask = new JobTask
                            {
                                ParentTaskID = jobTask.ID,
                                ExeProgramID = jobTask.ExeProgramID,
                                TaskName = subTaskName,
                                Priority = jobTask.Priority,
                                InputParameters = System.Text.Json.JsonSerializer.Serialize(newInputParams),
                                Status = JobTaskStatusEnum.Pending,
                                CreatedAt = DateTime.Now,
                                TaskType = TaskType.Normal,
                                ResourceSelection = jobTask.ResourceSelection
                            };

                            await splitJobTaskService.CreateJobTaskAsync(subTask);
                        });

                        // 更新父任务状态
                        // await jobTaskService.UpdateJobTaskStatusAsync(jobTask.ID, JobTaskStatusEnum.Running);
                    }
                }
                catch (Exception ex)
                {
                    await jobTaskService.UpdateJobTaskStatusAsync(jobTask.ID, JobTaskStatusEnum.Failed, outputResults: "处理Excel拆分任务异常：" + ex.ToString());
                    throw;
                }
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        public async Task<OrchestrationTaskStatusDto> GetOrchestrationTaskStatus(long taskId)
        {
            using var scope = scopeFactory.CreateScope();
            var jobTaskService = scope.ServiceProvider.GetRequiredService<JobTaskService>();

            var task = await jobTaskService.GetJobTaskByIdAsync(taskId);
            if (task == null)
            {
                throw new Exception($"Task not found: {taskId}");
            }

            return new OrchestrationTaskStatusDto
            {
                TaskId = task.ID,
                Status = task.Status,
                OutputResults = task.OutputResults
            };
        }
    }
}

