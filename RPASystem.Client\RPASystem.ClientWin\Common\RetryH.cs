using Polly;
using System;
using System.Threading.Tasks;


/// <summary>
/// 通用重试工具类
/// </summary>
public static class RetryH
{
    /// <summary>
    /// 执行需要重试的同步操作
    /// </summary>
    /// <param name="action">要执行的操作</param>
    /// <param name="retryCount">重试次数，默认60次</param>
    /// <param name="retryInterval">重试间隔（秒），默认2秒</param>
    /// <returns>是否执行成功</returns>
    public static bool Execute(Action action, int retryCount = 60, int retryInterval = 2)
    {
        try
        {
            var policy = Policy
                .Handle<Exception>()
                .WaitAndRetry(retryCount,
                    retryAttempt => TimeSpan.FromSeconds(retryInterval),
                    onRetry: (exception, timeSpan, retryNumber, context) =>
                    {
                        // 记录重试信息
                        NLog.LogManager.GetCurrentClassLogger()
                            .Warn($"第 {retryNumber} 次重试失败: {exception.Message}");
                    });

            policy.Execute(() => action());
            return true;
        }
        catch (Exception ex)
        {
            NLog.LogManager.GetCurrentClassLogger()
                .Error(ex, "重试最终失败");
            return false;
        }
    }

    /// <summary>
    /// 执行需要重试的异步操作
    /// </summary>
    /// <param name="action">要执行的异步操作</param>
    /// <param name="retryCount">重试次数，默认60次</param>
    /// <param name="retryInterval">重试间隔（秒），默认2秒</param>
    /// <returns>是否执行成功</returns>
    public static async Task<bool> ExecuteAsync(Func<Task> action, int retryCount = 60, int retryInterval = 2)
    {
        try
        {
            var policy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(retryCount,
                    retryAttempt => TimeSpan.FromSeconds(retryInterval),
                    onRetry: (exception, timeSpan, retryNumber, context) =>
                    {
                        // 记录重试信息
                        NLog.LogManager.GetCurrentClassLogger()
                            .Warn($"第 {retryNumber} 次重试失败: {exception.Message}");
                    });

            await policy.ExecuteAsync(async () => await action());
            return true;
        }
        catch (Exception ex)
        {
            NLog.LogManager.GetCurrentClassLogger()
                .Error(ex, "重试最终失败");
            return false;
        }
    }

    /// <summary>
    /// 执行需要重试的同步操作并返回结果
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="func">要执行的操作</param>
    /// <param name="retryCount">重试次数，默认60次</param>
    /// <param name="retryInterval">重试间隔（秒），默认2秒</param>
    /// <returns>操作结果</returns>
    public static T Execute<T>(Func<T> func, int retryCount = 60, int retryInterval = 2)
    {
        var policy = Policy<T>
            .Handle<Exception>()
            .WaitAndRetry(retryCount,
                retryAttempt => TimeSpan.FromSeconds(retryInterval),
                onRetry: (exception, timeSpan, retryNumber, context) =>
                {
                    // 记录重试信息
                    NLog.LogManager.GetCurrentClassLogger()
                        .Warn($"第 {retryNumber} 次重试失败: {exception.Exception.Message}");
                });

        return policy.Execute(() => func());
    }

    /// <summary>
    /// 执行需要重试的异步操作并返回结果
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="func">要执行的异步操作</param>
    /// <param name="retryCount">重试次数，默认60次</param>
    /// <param name="retryInterval">重试间隔（秒），默认2秒</param>
    /// <returns>操作结果</returns>
    public static async Task<T> ExecuteAsync<T>(Func<Task<T>> func, int retryCount = 60, int retryInterval = 2)
    {
        var policy = Policy<T>
            .Handle<Exception>()
            .WaitAndRetryAsync(retryCount,
                retryAttempt => TimeSpan.FromSeconds(retryInterval),
                onRetry: (exception, timeSpan, retryNumber, context) =>
                {
                    // 记录重试信息
                    NLog.LogManager.GetCurrentClassLogger()
                        .Warn($"第 {retryNumber} 次重试失败: {exception.Exception.Message}");
                });

        return await policy.ExecuteAsync(async () => await func());
    }
}
