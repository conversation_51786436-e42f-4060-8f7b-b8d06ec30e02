<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="EPPlus" Version="7.4.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.10" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="ZR.Infrastructure" Version="1.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RPA.Model\RPA.Model.csproj" />
  </ItemGroup>
</Project>