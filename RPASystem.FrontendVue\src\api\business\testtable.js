import request from '@/utils/request'
import { downFile } from '@/utils/request'

/**
 * 只是测试分页查询
 * @param {查询条件} data
 */
export function listTesttable(query) {
  return request({
    url: 'business/Testtable/list',
    method: 'get',
    params: query,
  })
}

/**
 * 新增只是测试
 * @param data
 */
export function addTesttable(data) {
  return request({
    url: 'business/Testtable',
    method: 'post',
    data: data,
  })
}
/**
 * 修改只是测试
 * @param data
 */
export function updateTesttable(data) {
  return request({
    url: 'business/Testtable',
    method: 'PUT',
    data: data,
  })
}
/**
 * 获取只是测试详情
 * @param {Id}
 */
export function getTesttable(id) {
  return request({
    url: 'business/Testtable/' + id,
    method: 'get'
  })
}

/**
 * 删除只是测试
 * @param {主键} pid
 */
export function delTesttable(pid) {
  return request({
    url: 'business/Testtable/delete/' + pid,
    method: 'POST'
  })
}
// 清空只是测试
export function clearTesttable() {
  return request({
    url: 'business/Testtable/clean',
    method: 'POST'
  })
}
// 导出只是测试
export async function exportTesttable(query) {
  await downFile('business/Testtable/export', { ...query })
}
