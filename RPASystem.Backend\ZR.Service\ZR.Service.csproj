﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<GeneratePackageOnBuild>True</GeneratePackageOnBuild>
		<PackageOutputPath>..\Lib</PackageOutputPath>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<NoWarn>1591</NoWarn>
	</PropertyGroup>
	<ItemGroup>
	  <Compile Remove="RPA\**" />
	  <EmbeddedResource Remove="RPA\**" />
	  <None Remove="RPA\**" />
	</ItemGroup>
	<ItemGroup>
	  <PackageReference Include="ZR.ServiceCore" Version="1.0.0" />
	</ItemGroup>

</Project>
