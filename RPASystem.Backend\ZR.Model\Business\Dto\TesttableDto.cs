
namespace ZR.Model.Business.Dto
{
    /// <summary>
    /// 只是测试查询对象
    /// </summary>
    public class TesttableQueryDto : PagerInfo 
    {
        public string Name { get; set; }
        public int? MyType { get; set; }
    }

    /// <summary>
    /// 只是测试输入输出对象
    /// </summary>
    public class TesttableDto
    {
        [ExcelColumn(Name = "Id")]
        [ExcelColumnName("Id")]
        public long? Id { get; set; }

        [ExcelColumn(Name = "Name")]
        [ExcelColumnName("Name")]
        public string Name { get; set; }

        [ExcelColumn(Name = "MyType")]
        [ExcelColumnName("MyType")]
        public int? MyType { get; set; }



        [ExcelColumn(Name = "MyType")]
        public string MyTypeLabel { get; set; }
    }
}