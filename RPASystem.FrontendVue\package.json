{"name": "zr.admin", "version": "v20240105", "description": "RPASystem管理系统", "author": "ZR", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/izory/ZrAdminNetCore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@microsoft/signalr": "^8.0.0", "@vueuse/core": "^10.7.0", "@wangeditor/editor": "^5.1.1", "@wangeditor/editor-for-vue": "^5.1.11", "axios": "^1.6.2", "crypto-js": "^4.1.1", "echarts": "5.2.2", "element-plus": "^2.9.0", "file-saver": "2.0.5", "fuse.js": "6.4.6", "highlight.js": "^11.5.1", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "md-editor-v3": "^4.9.0", "nprogress": "0.2.0", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^3.2.3", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.0", "sortablejs": "^1.15.0", "v-code-diff": "^1.8.0", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-countup-v3": "^1.4.0", "vue-cropper": "1.0.2", "vue-i18n": "9.2.2", "vue-router": "^4.5.0", "vxe-pc-ui": "^4.0.12", "vxe-table": "^4.9.16", "xe-utils": "^3.5.32"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-sfc": "^3.3.4", "consola": "^3.2.3", "sass": "^1.82.0", "unplugin-auto-import": "^0.17.2", "vite": "^6.0.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}