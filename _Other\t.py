import pandas as pd
import os
from datetime import datetime
import pyautogui
from openpyxl import load_workbook
from openpyxl.drawing.image import Image as XLImage
from PIL import Image as PILImage
import glob
import io

# 全局变量
BASE_PATH = r"D:\RPA_List"
ORDER_COLUMN = "订单号"  # 订单号列名

def get_input_excel_path():
    """获取输入Excel文件路径"""
    input_dir = os.path.join(BASE_PATH, "Input")
    # 确保输入目录存在
    os.makedirs(input_dir, exist_ok=True)
    # 查找所有RPA_List开头的Excel文件
    excel_files = glob.glob(os.path.join(input_dir, "RPA_List*.xlsx"))
    if not excel_files:
        raise FileNotFoundError("未找到RPA_List*.xlsx文件")
    # 返回第一个匹配的文件
    return excel_files[0]

def get_screenshot_dir():
    """获取截图保存目录"""
    screenshot_dir = os.path.join(BASE_PATH, "Log", "Img")
    # 确保目录存在
    os.makedirs(screenshot_dir, exist_ok=True)
    return screenshot_dir

def save_screenshot(no):
    """保存全屏截图"""
    try:
        screenshot_dir = get_screenshot_dir()
        
        # 生成截图文件名（使用订单号作为文件名的一部分）
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        screenshot_path = os.path.join(screenshot_dir, f"{no}_{timestamp}.png")
        
        # 截取全屏并保存
        screenshot = pyautogui.screenshot()
        screenshot.save(screenshot_path)
        
        return screenshot_path
    except Exception as e:
        print(f"保存截图时出错: {str(e)}")
        return None

def update_excel_row(no, status, msg="", screenshot_path=None):
    """更新Excel中的指定行"""
    try:
        excel_path = get_input_excel_path()
        # 读取当前数据并更新状态和信息
        df = pd.read_excel(excel_path)
        row_index = df[df[ORDER_COLUMN] == no].index
        if len(row_index) == 0:
            print(f"未找到订单号: {no}")
            return
            
        # 更新状态和信息
        df.loc[row_index, 'RPA状态'] = status
        if msg:
            df.loc[row_index, 'PRA信息'] = msg
        
        # 如果有新截图，保存图片路径
        if screenshot_path:
            df.loc[row_index, 'RPA截图'] = screenshot_path
            
        # 保存数据更新
        df.to_excel(excel_path, index=False)
        
        # 重新插入所有图片
        wb = load_workbook(excel_path)
        ws = wb.active
        
        # 清空所有现有图片
        ws._images.clear()
        
        # 设置列宽以适应缩略图
        ws.column_dimensions['C'].width = 20  # 设置第C列的宽度
        
        # 遍历所有行，重新插入所有图片
        for idx, row in df.iterrows():
            if pd.notna(row['RPA截图']) and os.path.exists(row['RPA截图']):
                img = XLImage(row['RPA截图'])
                # 设置图片在单元格中的大小
                img.width = 140  # 像素，大约等于20个字符宽度
                img.height = 100  # 像素
                cell = f'C{idx + 2}'
                ws.add_image(img, cell)
                # 设置行高以适应缩略图
                ws.row_dimensions[idx + 2].height = 75  # 设置行高
        
        # 保存工作簿
        wb.save(excel_path)
            
    except Exception as e:
        print(f"更新Excel时出错: {str(e)}")
        raise e

def check_and_create_columns(excel_path):
    """检查并创建必要的列"""
    try:
        df = pd.read_excel(excel_path)
        modified = False
        
        # 检查是否需要插入列
        columns_to_check = [
            ('RPA状态', 0),
            ('PRA信息', 1),
            ('RPA截图', 2)
        ]
        
        for col_name, pos in columns_to_check:
            if col_name not in df.columns:
                df.insert(pos, col_name, '')
                modified = True
            
        if modified:
            df.to_excel(excel_path, index=False)
        return df
    except Exception as e:
        print(f"检查和创建列时出错: {str(e)}")
        raise e  # 抛出异常以便调试


def ErrorInputLog(no, msg):
    """记录错误状态"""
    screenshot_path = save_screenshot(no)
    update_excel_row(no, "异常", msg, screenshot_path)

def WarningInputLog(no, msg):
    """记录警告状态"""
    screenshot_path = save_screenshot(no)
    update_excel_row(no, "警告", msg, screenshot_path)

def DoneInputLog(no):
    """记录完成状态"""
    update_excel_row(no, "已完成")

ErrorInputLog('N10000005',"6666666666")
WarningInputLog('N10000008',"888886666666666")
DoneInputLog('N10000009')