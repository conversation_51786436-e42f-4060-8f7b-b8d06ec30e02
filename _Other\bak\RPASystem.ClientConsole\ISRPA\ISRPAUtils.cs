﻿using Microsoft.Win32;
using System.IO;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;


public class ISRPAUtils
{
    /// <summary>
    /// 获取安装目录
    /// </summary>
    /// <returns>返回安装目录</returns>
    public static string GetInstallPath()
    {
        string installPath;
        using (var key = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64).OpenSubKey(@"SOFTWARE\RPA2020", true))
        {
            installPath = key?.GetValue("RPAPath", string.Empty) as string;
            key?.Close();
        }

        string exePath = Path.Combine(installPath ?? string.Empty, @"Studio-X64\RPAStudio.exe");
        if (File.Exists(exePath))
        {
            return installPath;
        }

        exePath = @"D:\Program Files\ISRPA\Studio-X64\RPAStudio.exe";
        if (File.Exists(exePath))
        {
            return @"D:\Program Files\ISRPA";
        }

        return string.Empty;
    }

    /// <summary>
    /// 获取Python文件内Class名称
    /// </summary>
    /// <param name="codePath">python文件路径</param>
    /// <returns>返回class名称</returns>
    public static string GetPythonClassName(string codePath)
    {
        var s = File.ReadAllText(codePath);
        var m = Regex.Match(s, "class (.*?):");
        return m.Groups[1].Value;
    }

    /// <summary>
    /// 获取Python库文件目录
    /// </summary>
    /// <returns>返回Python库目录</returns>
    public static string GetPythonLibDirePath()
    {
        return Path.Combine(GetInstallPath(), @"Python\Lib");
    }


    /// <summary>
    /// 获取版本号
    /// </summary>
    /// <returns>版本号</returns>
    public static string GetRPAMainVersion()
    {
        var registryKey = GetRegistryKey();

        return registryKey.GetValue("RPAMainVersion", string.Empty).ToString();
    }

    public static RegistryKey GetRegistryKey()
    {
        RegistryKey baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, RegistryView.Registry64);
        RegistryKey subKey = baseKey.OpenSubKey(@"SOFTWARE\RPA2020");

        return subKey;
    }
}

