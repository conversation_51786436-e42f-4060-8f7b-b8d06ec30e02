{"menu": {"home": "Home", "dashboard": "Dashboard", "system": "System", "monitoring": "Monitoring", "systemTools": "Tools", "externalOpen": "External open", "icon": "Icon", "systemMenu": "<PERSON><PERSON>", "systemRole": "Role", "systemUser": "User", "systemDept": "Dept", "systemDic": "Dict", "systemPost": "Post", "systemParam": "Config", "systemLog": "Log", "operLog": "Oper log", "loginLog": "Login log", "systemNotice": "Notice", "systemLang": "Language set", "timedTask": "Timed task", "serviceMonitor": "Service monitoring", "codeGeneration": "Code generation", "systemInterface": "System interface", "sendEmail": "Email", "systemArticle": "Article", "articleList": "Article list", "formBuild": "Form building", "officialWebsite": "Official website", "fileStorage": "File storage", "personalCenter": "Personal", "menuPermi": "Menu permissions", "assignUsers": "Authorized user", "cacheMonitor": "Cache monitoring", "dataScreen": "dataScreen", "emailList": "Mail management", "emailTemplate": "Email templates", "emailLog": "Mail records", "articleCategory": "Article category", "userOnlineLog": "User online logs", "dataDiffLog": "Data discrepancy logs", "smsLog": "SMS logs", "zujianDemo": "Demo"}, "tagsView": {"refresh": "refresh", "close": "close", "closeOther": "close other", "closeLeft": "close left", "closeRight": "close right", "closeAll": "close all", "fullscreen": "fullscreen", "closeFullscreen": "close fullscreen"}, "layout": {"tip1": "If you feel good, welcome to ⭐ Star ⭐ Collect it so that the author can continue to be free. Thank you!", "rewardUser": "Reward the author with a cup of coffee to express encouragement", "contactUs": "contact us", "donationSupport": "donation support", "officialWebsite": "Official website", "qqGroup": "QQ Group", "changeLog": "change log", "currentVersion": "current version", "codeSourceAddress": "source address", "fullscreen": "fullscreen", "sizeSelect": "size", "personalCenter": "Personal center", "layoutSetting": "layout setting", "logOut": "Login out", "themeStyleSet": "theme style settings", "themeColor": "theme color", "sysLayoutSet": "system layout settings", "open": "open", "fixed": "fixed", "show": "show", "dynamicTitle": "dynamic title", "darkMode": "dark", "lightMode": "light", "saveConfig": "save", "resetConfig": "reset", "logOutConfirm": "Are you sure you want to exit the current login?", "large": "Large", "default": "<PERSON><PERSON><PERSON>", "small": "Small", "myWorkbench": "My workbench", "onlineUsers": "Online user", "message": "Message", "amount": "Amount", "order": "Order", "modifyInformation": "Modify information", "technicalSelection": "Technical selection", "frontendTechnology": "Front-end technology", "backendTechnology": "Backend technology", "backstageManagement": "Backstage management", "bottomBar": "Footer", "identity": "Identity", "content1": "The code is completely free and open source, easy to read and understand, and the interface is simple and beautiful, giving you one more choice and reference for your project.", "topNav": "top nav", "commonFuncs": "Common Functions", "openWatermark": "Open Watermark", "workTime": "Today's working times(min)", "onlineClientNum": "Number of online devices", "tagsView": "tabs", "tagsPersist": "tabs Persist", "tagsShowIcon": "tabas show icon", "navMode": "nav mode", "sideColor": "side color"}, "common": {"ok": "Ok", "cancel": "Cancel", "tips": "Tips", "english": "English", "chinese": "Chinese", "traditionalChinese": "TraditionalChinese", "name": "Name", "content": "Content", "addTime": "AddTime", "nickName": "<PERSON><PERSON><PERSON>", "time": "Time", "yes": "Yes", "no": "No", "yesterday": "yesterday", "thisWeek": "this week", "lastWeek": "last week", "thisMonth": "this month", "lastMonth": "last month", "today": "today", "female": "Female", "male": "male", "sex": "gender", "systemTips": "system hint", "default": "default", "hidden": "hide", "show": "show", "system": "system", "abnormal": "abnormal", "unknow": "unknown", "normal": "normal", "disable": "deactivate"}, "btn": {"add": "Add", "delete": "Delete", "edit": "Edit", "search": "Search", "reset": "Reset", "preview": "Preview", "import": "Import", "export": "Export", "synchronize": "Synchronize", "generateCode": "GenerateCode", "start": "Start", "stop": "Stop", "run": "Run", "runOnce": "RunOnce", "log": "Log", "expand": "Expand", "collapse": "Collapse", "details": "Details", "submit": "Submit", "cancel": "Cancel", "more": "More", "save": "Save", "close": "Close", "authorize": "Authorize", "multi": "Multi", "upload": "Upload", "view": "To view", "copy": "Copy", "operate": "Operate", "clean": "Clean"}, "language": "Language", "languageKey": "LanguageKey", "showWay": "Show Way", "table": "Table", "user": {"phoneNumber": "cellphone number", "registerTime": "Date created", "basicInfo": "basic information", "department": "Department", "personalInfo": "Personal information", "role": "own role", "userEmail": "User mailbox", "userName": "user name", "changePwd": "change Password", "confirmPwd": "Confirm Password", "newPwd": "new password", "oldPwd": "Old Password", "nickName": "User's Nickname"}}