@echo off
setlocal enabledelayedexpansion

rem 设置发布目标路径（可根据需要修改）
set "PUBLISH_PATH=Z:\VV35"

rem 设置项目路径和输出路径
set "projectPath=%~dp0"
set "outputPath=%projectPath%bin\Release"

rem 使用vswhere查找MSBuild路径
for /f "usebackq tokens=*" %%i in (`"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -latest -requires Microsoft.Component.MSBuild -find MSBuild\**\Bin\MSBuild.exe`) do (
    set "MSBUILD_PATH=%%i"
)

if "%MSBUILD_PATH%"=="" (
    echo Error: Could not find MSBuild. Please ensure Visual Studio is installed.
    pause
    exit /b 1
)

rem 1. 使用MSBuild编译项目
"%MSBUILD_PATH%" "%projectPath%RPASystem.ClientWin.csproj" /t:Build /p:Configuration=Release

rem 2. 进入输出目录
cd /d "%outputPath%"

rem 3. 删除config文件
if exist "RPASystem.ClientWin.exe.config" (
    del /f /q "RPASystem.ClientWin.exe.config"
)

rem 4. 读取Version.dat的第一行作为版本号
set "version="
if exist "Version.dat" (
    for /f "usebackq delims=" %%a in ("Version.dat") do (
        set "version=%%a"
        goto :next
    )
)
:next

rem 如果没有读取到版本号，使用默认值
if "%version%"=="" set "version=1.0.0"

rem 5. 创建zip文件名
set "zipFileName=RPASystem.ClientWinV3-%version%.zip"

rem 6. 使用PowerShell压缩文件
powershell -command "Compress-Archive -Path '%outputPath%\*' -DestinationPath '%outputPath%\%zipFileName%' -Force"

rem 7. 确保目标目录存在
if not exist "%PUBLISH_PATH%" mkdir "%PUBLISH_PATH%"

rem 8. 移动zip文件到指定位置
move /y "%outputPath%\%zipFileName%" "%PUBLISH_PATH%\%zipFileName%"

echo Build and publish process completed successfully.
pause
