using RPASystem.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using RPASystem.Service;
using Infrastructure.Attribute;

namespace RPASystem.Service
{
    [AppService(ServiceType = typeof(IFileStorageService), ServiceLifetime = LifeTime.Scoped)]
    public class FileStorageService : IFileStorageService
    {
        private readonly RPASystemDbContext dbContext;

        public FileStorageService(RPASystemDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<long> UploadFileAsync(FileStorage fileStorage)
        {
            fileStorage.UploadTime = DateTime.Now;
            dbContext.FileStorages.Add(fileStorage);
            await dbContext.SaveChangesAsync();
            return fileStorage.ID;
        }

        public async Task<FileStorage> GetFileAsync(long id)
        {
            return await dbContext.FileStorages.FindAsync(id);
        }

        public async Task<(List<FileStorage> Items, int Total)> GetFilesByPageAsync(int pageIndex, int pageSize, string? searchFileName = null, string? searchFileExtension = null)
        {
            var query = dbContext.FileStorages.AsQueryable();
            
            // 添加搜索条件
            if (!string.IsNullOrWhiteSpace(searchFileName))
            {
                query = query.Where(f => f.FileName.Contains(searchFileName));
            }
            
            if (!string.IsNullOrWhiteSpace(searchFileExtension))
            {
                query = query.Where(f => f.FileExtension.Contains(searchFileExtension));
            }

            var total = await query.CountAsync();
            var items = await query
                .OrderByDescending(f => f.UploadTime)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (items, total);
        }

        public async Task<bool> UpdateFileRemarkAsync(long id, string remark)
        {
            var file = await dbContext.FileStorages.FindAsync(id);
            if (file == null) return false;
            file.Remark = remark;
            file.ModifyTime = DateTime.Now;
            await dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateFileAsync(long id, byte[] fileData, string fileName, string fileExtension, string remark)
        {
            var file = await dbContext.FileStorages.FindAsync(id);
            if (file == null) return false;

            file.FileData = fileData;
            file.FileName = fileName;
            file.FileExtension = fileExtension;
            file.Remark = remark;
            file.ModifyTime = DateTime.Now;

            await dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<bool> BatchDeleteFilesAsync(List<long> ids)
        {
            var files = await dbContext.FileStorages
                .Where(f => ids.Contains(f.ID))
                .ToListAsync();

            if (!files.Any()) return false;

            dbContext.FileStorages.RemoveRange(files);
            await dbContext.SaveChangesAsync();
            return true;
        }
    }
}
