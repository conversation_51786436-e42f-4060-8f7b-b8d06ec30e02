﻿namespace RPASystem.Model
{
    using System.Text.Json.Serialization;
    using System.ComponentModel.DataAnnotations.Schema;
    /// <summary>
    /// EXE程序的模型类
    /// </summary>
    public class ExeProgram
    {
        /// <summary>
        /// 程序ID
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
        public long ID { get; set; }

        /// <summary>
        /// 程序名称
        /// </summary>
        public string ProgramName { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string? Version { get; set; }

        /// <summary>
        /// 程序输入参数（JSON格式）
        /// </summary>
        public string? InputParameters { get; set; }

        /// <summary>
        /// 是否独占资源机
        /// </summary>
        public bool IsExclusive { get; set; }

        /// <summary>
        /// 程序包ZIP（文件路径存储）
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
        public string? ProgramPackage { get; set; }

        /// <summary>
        /// 程序类型（RPA或EXE）
        /// </summary>
        [Column(TypeName = "ENUM('RPA', 'EXE')")]
        public ProgramTypeEnum ProgramType { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 指定资源机和资源池运行，多个用|分隔
        /// </summary>
        public string? ResourceSelection { get; set; }
    }
}
