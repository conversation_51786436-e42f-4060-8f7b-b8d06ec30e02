﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO.Compression;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using OfficeOpenXml;
using System.Collections.Generic;
using RPASystem.Client;
using NLog;
using System.Net.Http.Headers;
using System.Threading;
using RPASystem.Model;

public class ISRPAStarter
{
    private static readonly ILogger logger = LogManager.GetCurrentClassLogger();
    public readonly RPACase RpaCase;
    private readonly JobModel jobModel;
    private readonly HttpClient httpClient;
    private string ServerIP; // 新增 ServerIP 字段
    string FileId;
    string TaskOutputServerDir => $@"\\{ServerIP}\VShare\WAutoDeploy\{jobModel.ProgramName}\OUTPUTRPASYSTEM\{jobModel.TopJobTaskName}\{ParentTaskName}\{jobModel.JobTaskName}";
    string ParentTaskName
    {
        get
        {
            var match = System.Text.RegularExpressions.Regex.Match(jobModel.JobTaskName, @"(.+)_\d+$");
            // 如果ParentTaskName和TopJobTaskName相同，证明是系统编排拆分任务类型，所以不用再加一层目录
            return match.Success ? (match.Groups[1].Value == jobModel.TopJobTaskName ? "" : match.Groups[1].Value) : "";
        }
    }//匹配_数字结尾的字符串，返回_数字之前的部分，如果匹配失败返回空字符串. 

    public ISRPAStarter(JobModel model)
    {
        jobModel = model;
        RpaCase = new RPACase(model.ProgramName);
        httpClient = new HttpClient();
    }

    public async Task<RunResult> FastRunAsync(CancellationToken cancellationToken = default)
    {
        var result = new RunResult
        {
            IsSucceed = true,
            Status = 1
        };

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            await new PackagesMan(jobModel.ProgramName, RpaCase.RootPath, jobModel.ProgramVersion).CheckAndUpdateAsync();
            CloseAllRPA();
            DeleteProjectInt();
            DeleteConfigLogOutput();
            ShowDesktop();

            await ProcessRPAParameters();

            using (var rpaProcess = GetFastRunProcess())
            {
                cancellationToken.Register(() =>
                {
                    try
                    {
                        if (!rpaProcess.HasExited)
                        {
                            rpaProcess.Kill();
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Error(ex, "终止RPA进程失败");
                    }
                });

                rpaProcess.Start();
                Console.WriteLine($"已启动艺赛旗Python!进程ID:{rpaProcess.Id}");
                Console.WriteLine($"已启动艺赛旗参数：{rpaProcess.StartInfo.FileName} {rpaProcess.StartInfo.Arguments}");
                rpaProcess.BeginOutputReadLine();

                await Task.Run(() => rpaProcess.WaitForExit(), cancellationToken);

                var rpaResult = RunResult.FromJson(await ISRPALog.GetInstance.GetRpaRetStringAsync(RpaCase.LogPath));
                result.IsSucceed = rpaResult?.Status > 0;
                result.ReturnResult = string.IsNullOrWhiteSpace(rpaResult?.ReturnResult) ? TaskOutputServerDir : rpaResult.ReturnResult;
                result.ErrorMessage = rpaResult?.ErrorMessage;
                result.Status = rpaResult?.Status;
            }

            cancellationToken.ThrowIfCancellationRequested();

            if (!await ISRPALog.GetInstance.IsRpaCorrectRunCompletedAsync(jobModel.ProgramName))
            {
                ISRPALog.GetInstance.RpaCorrectRunCompletedRecord(jobModel.ProgramName);
                result.ErrorMessage += "  RPA非正常运行结束";
                result.Status = 0;
            }

            await ProcessRPAResults();
        }
        catch (OperationCanceledException)
        {
            result.IsSucceed = false;
            result.Status = 0;
            result.ErrorMessage = "任务被取消";
            throw;
        }
        catch (Exception ex)
        {
            result.IsSucceed = false;
            result.Status = 0;
            result.ErrorMessage += $"  运行RPA异常：{jobModel.ProgramName} 异常：{ex.Message}";
            logger.Error(ex, $"运行RPA异常：{jobModel.ProgramName} 路径：{RpaCase.RootPath}");
            Console.WriteLine(result.ErrorMessage);
            throw;
        }

        return result;
    }

    /// <summary>
    /// 删除文件夹Config Log Output Input
    /// </summary>
    private void DeleteConfigLogOutput()
    {
        // 需要删除的目录路径数组
        string[] pathsToDelete = new[]
        {
            RpaCase.ConfigPath,
            RpaCase.LogPath,
            RpaCase.OutputPath,
            RpaCase.InputPath
        };

        foreach (string path in pathsToDelete)
        {
            if (Directory.Exists(path))
            {
                RetryH.Execute(() => Directory.Delete(path, true));
            }
        }
    }

    private async Task ProcessRPAParameters()
    {
        var jsonParams = JObject.Parse(jobModel.Parameter);
        var configParams = new Dictionary<string, string>();

        foreach (var param in jsonParams.Properties().ToList())
        {
            if (param.Name == "InputFile")
            {
                var inputVal = param.Value.ToString();
                if (string.IsNullOrEmpty(inputVal))
                {
                    throw new Exception("InputFile 为空！！！");
                }
                // 确保目录存在
                Directory.CreateDirectory(RpaCase.InputPath);
                if (inputVal.StartsWith("\\"))
                    File.Copy(inputVal, Path.Combine(RpaCase.InputPath, $"RPA_List-{jobModel.JobTaskName}.xlsx"));
                else
                    await DownloadAndSaveInputFile(inputVal);
                jsonParams.Remove("InputFile");
            }
            else if (param.Name == "UserName")
            {
                // 获取用户密文
                var response = await httpClient.GetAsync($"{MachineMan.BaseUrl}/api/RpaCredential/password/{param.Value}");
                if (response.IsSuccessStatusCode)
                {
                    var password = await response.Content.ReadAsStringAsync();
                    configParams.Add("PassWord", password); // 使用固定的 PassWord 作为密文的 KEY
                    configParams.Add(param.Name, param.Value.ToString());
                }
                else
                {
                    throw new Exception($"获取用户密文失败: {response.StatusCode}");
                }
            }
            else if (param.Name == "ServerIP")
            {
                ServerIP = param.Value.ToString();
                configParams.Add(param.Name, ServerIP);
                jsonParams.Remove("ServerIP");
            }
            else
            {
                configParams.Add(param.Name, param.Value.ToString());
            }
        }

        SaveConfigToExcel(configParams);
    }

    private async Task DownloadAndSaveInputFile(string fileId)
    {
        var response = await httpClient.GetAsync($"{MachineMan.BaseUrl}/api/FileStorage/{fileId}");
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsByteArrayAsync();
            string inputPath = Path.Combine(RpaCase.InputPath, $"RPA_List-{jobModel.JobTaskName}.xlsx");

            File.WriteAllBytes(inputPath, content);
            FileId = fileId;
        }
        else
        {
            throw new Exception($"下载InputFile失败: {response.StatusCode}");
        }
    }

    private void SaveConfigToExcel(Dictionary<string, string> configParams)
    {
        string configPath = Path.Combine(RpaCase.ConfigPath, "RPA_Config.xlsx");

        Directory.CreateDirectory(Path.GetDirectoryName(configPath));

        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        using (var package = new ExcelPackage(new FileInfo(configPath)))
        {
            var worksheet = package.Workbook.Worksheets.Add("Config");

            // 添加表头
            worksheet.Cells[1, 1].Value = "KEY";
            worksheet.Cells[1, 2].Value = "VALUE";

            // 从第二行开始写入数据
            int row = 2;
            foreach (var param in configParams)
            {
                worksheet.Cells[row, 1].Value = param.Key;
                worksheet.Cells[row, 2].Value = param.Value;
                row++;
            }
            package.Save();
        }
    }

    private async Task ProcessRPAResults()
    {
        try
        {
            if (!string.IsNullOrEmpty(ServerIP))
            {
                CopyOutputToServer();
            }

            await UploadUpdatedInputFile();
        }
        catch (Exception ex)
        {
            throw new Exception($"处理RPA结果时出错: {ex.Message}", ex);
        }
    }

    private void CopyOutputToServer()
    {
        Directory.CreateDirectory(TaskOutputServerDir);

        var pathsToCopy = new[] { RpaCase.OutputPath, RpaCase.InputPath, RpaCase.LogPath }; // 复制目录

        foreach (var sourcePath in pathsToCopy)
        {
            if (!string.IsNullOrWhiteSpace(sourcePath) && Directory.Exists(sourcePath))
            {
                string dirName = Path.GetFileName(sourcePath.TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar));
                string destPath = Path.Combine(TaskOutputServerDir, dirName);
                CopyDirectoryRecursively(sourcePath, destPath);
            }
        }
    }


    private void CopyDirectoryRecursively(string sourcePath, string targetPath)
    {
        Directory.CreateDirectory(targetPath);
        foreach (string file in Directory.GetFiles(sourcePath))
        {
            File.Copy(file, Path.Combine(targetPath, Path.GetFileName(file)), true);
        }
        foreach (string directory in Directory.GetDirectories(sourcePath))
        {
            string targetDir = Path.Combine(targetPath, Path.GetFileName(directory));
            CopyDirectoryRecursively(directory, targetDir);
        }
    }


    private async Task UploadUpdatedInputFile()
    {
        if (string.IsNullOrEmpty(FileId))
        {
            return;
        }
        string inputFilePath = Path.Combine(RpaCase.InputPath, $"RPA_List-{jobModel.JobTaskName}.xlsx");

        if (!File.Exists(inputFilePath))
        {
            throw new FileNotFoundException($"文件未找到: {inputFilePath}");
        }

        using (var httpClient = new HttpClient())
        {
            // 构建目标 URL，包含文件 ID
            string requestUri = $"{MachineMan.BaseUrl}/api/FileStorage/{FileId}";

            using (var content = new MultipartFormDataContent())
            {
                // 读取文件内容
                byte[] fileBytes = File.ReadAllBytes(inputFilePath);
                var fileContent = new ByteArrayContent(fileBytes);
                fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                // 添加文件到 MultipartFormDataContent，字段名必须与服务端参数匹配
                content.Add(fileContent, "file", jobModel.JobTaskName);

                // 添加备注信息
                string remark = $"{DateTime.Now:yyyyMMddHHmmss} 已更新文件。";
                content.Add(new StringContent(remark), "remark");

                // 发送 PUT 请求
                HttpResponseMessage response = await httpClient.PutAsync(requestUri, content);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("文件更新成功。");
                }
                else
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"上传更新后的 InputFile 失败: {response.StatusCode}, 详情: {responseContent}");
                }
            }
        }
    }


    private Process GetFastRunProcess()
    {
        var codesDirPath = Path.Combine(RpaCase.RootPath, "Application", "codes");
        var mainPyPath = Path.Combine(codesDirPath, "Main.py");
        var pythonClassName = ISRPAUtils.GetPythonClassName(mainPyPath);
        var pythonExePath = Path.Combine(ISRPAUtils.GetInstallPath(), "Python", "pythons.exe");

        var pythonProcess = new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = pythonExePath,
                Arguments = $"\"{mainPyPath}\" -p \"{pythonClassName}\" {GetRpaParam()}",
                WorkingDirectory = codesDirPath,
                CreateNoWindow = true,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                Verb = "runas"
            }
        };

        pythonProcess.OutputDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data))
            {
                var message = Encoding.UTF8.GetString(Encoding.Default.GetBytes(e.Data));
                ISRPALog.GetInstance.WriteToLogFile(jobModel.ProgramName, message);
            }
        };

        return pythonProcess;
    }

    private string GetRpaParam()
    {
        return "";
        //return !string.IsNullOrEmpty(jobModel.Parameter) ? $"-i\"{Convert.ToBase64String(Encoding.UTF8.GetBytes(jobModel.Parameter))}\"" : string.Empty;
    }

    private void DeleteProjectInt()
    {
        string pathProject = Path.Combine(RpaCase.RootPath, "Application", "Project.int");
        if (File.Exists(pathProject))
        {
            File.Delete(pathProject);
        }
    }

    public static void CloseAllRPA()
    {
        var processNames = new[] { "RPAUpdate", "RPAStudio", "pythons" };
        foreach (var processName in processNames)
        {
            foreach (var process in Process.GetProcessesByName(processName))
            {
                try
                {
                    process.Kill();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"无法结束进程 {processName}：{ex.Message}");
                }
            }
        }
    }

    private static void ShowDesktop()
    {
        try
        {
            Keybd_event((byte)Keys.LWin, 0, 0, 0);
            Keybd_event((byte)Keys.M, 0, 0, 0);
            Keybd_event((byte)Keys.M, 0, 2, 0);
            Keybd_event((byte)Keys.LWin, 0, 2, 0);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"显示桌面失败：{ex.Message}");
        }
    }

    [DllImport("user32.dll", EntryPoint = "keybd_event", SetLastError = true)]
    private static extern void Keybd_event(byte key, byte bscan, uint dwflags, uint dwextraInfo);
}
