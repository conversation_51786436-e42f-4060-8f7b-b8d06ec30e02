using Microsoft.AspNetCore.Mvc;
using RPASystem.Service;
using RPASystem.Model;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ResourcePoolController : ControllerBase
    {
        private readonly IResourcePoolService resourcePoolService;
        private readonly IResourceMachineService resourceMachineService;

        public ResourcePoolController(IResourcePoolService resourcePoolService, IResourceMachineService resourceMachineService)
        {
            this.resourcePoolService = resourcePoolService;
            this.resourceMachineService = resourceMachineService;
        }

        /// <summary>
        /// 获取所有资源池
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<ResourcePool>>> GetAll()
        {
            var pools = await resourcePoolService.GetAllResourcePools();
            return Ok(pools);
        }

        /// <summary>
        /// 获取所有资源机
        /// </summary>
        [HttpGet("machines")]
        public async Task<ActionResult<List<ResourceMachine>>> GetAllMachines()
        {
            var machines = await resourceMachineService.GetAllResourceMachinesAsync();
            return Ok(machines);
        }

        /// <summary>
        /// 创建资源池
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<bool>> Create([FromBody] ResourcePool pool)
        {
            var result = await resourcePoolService.CreateResourcePool(pool);
            return Ok(result);
        }

        /// <summary>
        /// 更新资源池
        /// </summary>
        [HttpPut]
        public async Task<ActionResult<bool>> Update([FromBody] ResourcePool pool)
        {
            var result = await resourcePoolService.UpdateResourcePool(pool);
            return Ok(result);
        }

        /// <summary>
        /// 删除资源池
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<bool>> Delete(long id)
        {
            var result = await resourcePoolService.DeleteResourcePool(id);
            return Ok(result);
        }
    }
} 