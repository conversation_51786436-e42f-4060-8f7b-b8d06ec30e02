using Microsoft.AspNetCore.Mvc;
using ZR.Model.Business.Dto;
using ZR.Model.Business;
using ZR.Service.Business.IBusinessService;
using ZR.Admin.WebApi.Filters;

//创建时间：2024-12-11
namespace ZR.Admin.WebApi.Controllers.Business
{
    /// <summary>
    /// 功能名只是演示
    /// </summary>
    [Verify]
    [Route("business/Justdemo")]
    public class JustdemoController : BaseController
    {
        /// <summary>
        /// 功能名只是演示接口
        /// </summary>
        private readonly IJustdemoService _JustdemoService;

        public JustdemoController(IJustdemoService JustdemoService)
        {
            _JustdemoService = JustdemoService;
        }

        /// <summary>
        /// 查询功能名只是演示列表
        /// </summary>
        /// <param name="parm"></param>
        /// <returns></returns>
        [HttpGet("list")]
        [ActionPermissionFilter(Permission = "justdemo:list")]
        public IActionResult QueryJustdemo([FromQuery] JustdemoQueryDto parm)
        {
            var response = _JustdemoService.GetList(parm);
            return SUCCESS(response);
        }


        /// <summary>
        /// 查询功能名只是演示详情
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet("{Id}")]
        [ActionPermissionFilter(Permission = "justdemo:query")]
        public IActionResult GetJustdemo(long Id)
        {
            var response = _JustdemoService.GetInfo(Id);
            
            var info = response.Adapt<JustdemoDto>();
            return SUCCESS(info);
        }

        /// <summary>
        /// 添加功能名只是演示
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionPermissionFilter(Permission = "justdemo:add")]
        [Log(Title = "功能名只是演示", BusinessType = BusinessType.INSERT)]
        public IActionResult AddJustdemo([FromBody] JustdemoDto parm)
        {
            var modal = parm.Adapt<Justdemo>().ToCreate(HttpContext);

            var response = _JustdemoService.AddJustdemo(modal);

            return SUCCESS(response);
        }

        /// <summary>
        /// 更新功能名只是演示
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [ActionPermissionFilter(Permission = "justdemo:edit")]
        [Log(Title = "功能名只是演示", BusinessType = BusinessType.UPDATE)]
        public IActionResult UpdateJustdemo([FromBody] JustdemoDto parm)
        {
            var modal = parm.Adapt<Justdemo>().ToUpdate(HttpContext);
            var response = _JustdemoService.UpdateJustdemo(modal);

            return ToResponse(response);
        }

        /// <summary>
        /// 删除功能名只是演示
        /// </summary>
        /// <returns></returns>
        [HttpPost("delete/{ids}")]
        [ActionPermissionFilter(Permission = "justdemo:delete")]
        [Log(Title = "功能名只是演示", BusinessType = BusinessType.DELETE)]
        public IActionResult DeleteJustdemo([FromRoute]string ids)
        {
            var idArr = Tools.SplitAndConvert<long>(ids);

            return ToResponse(_JustdemoService.Delete(idArr));
        }

        /// <summary>
        /// 导出功能名只是演示
        /// </summary>
        /// <returns></returns>
        [Log(Title = "功能名只是演示", BusinessType = BusinessType.EXPORT, IsSaveResponseData = false)]
        [HttpGet("export")]
        [ActionPermissionFilter(Permission = "justdemo:export")]
        public IActionResult Export([FromQuery] JustdemoQueryDto parm)
        {
            parm.PageNum = 1;
            parm.PageSize = 100000;
            var list = _JustdemoService.ExportList(parm).Result;
            if (list == null || list.Count <= 0)
            {
                return ToResponse(ResultCode.FAIL, "没有要导出的数据");
            }
            var result = ExportExcelMini(list, "功能名只是演示", "功能名只是演示");
            return ExportExcel(result.Item2, result.Item1);
        }

    }
}