namespace RPASystem.Model
{
    /// <summary>
    /// 资源池模型
    /// </summary>
    public class ResourcePool
    {
        /// <summary>
        /// 资源池ID
        /// </summary>
        public long ID { get; set; }

        /// <summary>
        /// 资源池名称
        /// </summary>
        public string PoolName { get; set; }

        /// <summary>
        /// 资源池描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 资源机名称列表（用|分隔）
        /// </summary>
        public string ResourceMachineNames { get; set; }

        /// <summary>
        /// 资源机名称列表（转换后的数组）
        /// </summary>
        public List<string> MachineList => 
            string.IsNullOrEmpty(ResourceMachineNames) 
                ? new List<string>() 
                : ResourceMachineNames.Split('|').ToList();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }
    }
} 