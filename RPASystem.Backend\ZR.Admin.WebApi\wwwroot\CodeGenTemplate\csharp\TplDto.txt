$if(null != genTable.SubTableName && "" != genTable.SubTableName)
using ${subTableOptions.ModelsNamespace}.${subTableOptions.SubNamespace};
$end

namespace ${options.DtosNamespace}.${options.SubNamespace}.Dto
{
    /// <summary>
    /// ${genTable.FunctionName}查询对象
    /// </summary>
    public class ${replaceDto.ModelTypeName}QueryDto : PagerInfo 
    {
$foreach(item in genTable.Columns)
$if(item.IsQuery)
$if(item.htmlType.StartsWith("date"))
        public DateTime? Begin$item.CsharpField { get; set; }
        public DateTime? End$item.CsharpField { get; set; }
$elseif(item.htmlType == "selectMulti")
        public ${item.CsharpType}[] $item.CsharpField { get; set; }
$else
        public $item.CsharpType$if(item.CsharpType != "string")?$end $item.CsharpField { get; set; }
$end
$end
$end
    }

    /// <summary>
    /// ${genTable.FunctionName}输入输出对象
    /// </summary>
    public class ${replaceDto.ModelTypeName}Dto
    {
$foreach(item in genTable.Columns)
$set(labelName = "")
$if(item.ColumnComment != "")
$set(labelName = item.ColumnComment)
$else
$set(labelName = item.CsharpField)
$end
$if(item.IsRequired)
        [Required(ErrorMessage = "${item.ColumnComment}不能为空")]
$end
$if(replaceDto.ShowBtnExport)
$if(item.IsExport)
        [ExcelColumn(Name = "$labelName"$if(item.CsharpType == "DateTime"), Format = "yyyy-MM-dd HH:mm:ss", Width = 20$end)]
        [ExcelColumnName("$labelName")]
$else
        [ExcelIgnore]
$end
$end
$if(item.CsharpType == "long" && replaceDto.useSnowflakeId == true && item.IsPk)
        [JsonConverter(typeof(ValueToStringConverter))]
$end
        public $item.CsharpType$item.RequiredStr $item.CsharpField { get; set; }

$end

$if(genTable.TplCategory == "subNav" && genTable.SubTable != null)
$if(replaceDto.ShowBtnExport)
        [ExcelIgnore]
$end
        public ${genTable.SubTable.ClassName}Dto ${genTable.SubTable.ClassName}Nav { get; set; }
$end

$if(genTable.TplCategory == "subNavMore" && genTable.SubTable != null)
$if(replaceDto.ShowBtnExport)
        [ExcelIgnore]
$end
        public List<${genTable.SubTable.ClassName}Dto> ${genTable.SubTable.ClassName}Nav { get; set; }
$end
$foreach(column in dicts)
$if(column.IsExport)
        [ExcelColumn(Name = "$if(column.ColumnComment == "")${column.CsharpField}${else}${column.ColumnComment}${end}")]
        public string ${column.CsharpField}Label { get; set; }
$end
$end
    }
}